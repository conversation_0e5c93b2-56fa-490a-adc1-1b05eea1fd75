-- CRM4CA SQLite Database Schema
-- Professional database design for Chartered Accountant CRM application
-- Created: June 2025

PRAGMA foreign_keys = ON;
PRAGMA journal_mode = WAL;

-- Users table
CREATE TABLE users (
    user_id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    email TEXT UNIQUE,
    password_hash TEXT, -- Store hashed passwords
    role TEXT NOT NULL CHECK (role IN ('admin', 'editor', 'viewer')),
    permissions TEXT, -- JSON array of permissions
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT 1,
    is_email_verified BOOLEAN DEFAULT 0,
    last_login DATETIME,
    can_manage_campaigns BOOLEAN DEFAULT 0,
    can_edit_subscribers BOOLEAN DEFAULT 0,
    can_view_analytics BOOLEAN DEFAULT 0,
    default_sender_name TEXT,
    default_sender_email TEXT,
    default_sender_phone TEXT,
    table_preferences TEXT, -- JSON for user-specific column visibility
    is_admin_only BOOLEAN DEFAULT 0,
    owner_user_id TEXT
);

-- Areas of Interest table
CREATE TABLE areas_of_interest (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_admin_only BOOLEAN DEFAULT 0,
    owner_user_id TEXT,
    FOREIGN KEY (owner_user_id) REFERENCES users(user_id)
);

-- Subscribers table
CREATE TABLE subscribers (
    id TEXT PRIMARY KEY,
    email TEXT NOT NULL UNIQUE,
    firstName TEXT,
    lastName TEXT,
    entityName TEXT,
    phone TEXT,
    birthDate DATE, -- YYYY-MM-DD format
    status TEXT NOT NULL CHECK (status IN ('active', 'unsubscribed', 'pending_confirmation', 'cleaned', 'archived')),
    subscribed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    unsubscribed_at DATETIME,
    customFields TEXT, -- JSON for extra data
    allowWhatsApp BOOLEAN DEFAULT 0,
    allowSms BOOLEAN DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_admin_only BOOLEAN DEFAULT 0,
    owner_user_id TEXT,
    FOREIGN KEY (owner_user_id) REFERENCES users(user_id)
);

-- Subscriber Areas of Interest mapping table (many-to-many)
CREATE TABLE subscriber_areas_of_interest (
    subscriber_id TEXT,
    area_of_interest_id TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (subscriber_id, area_of_interest_id),
    FOREIGN KEY (subscriber_id) REFERENCES subscribers(id) ON DELETE CASCADE,
    FOREIGN KEY (area_of_interest_id) REFERENCES areas_of_interest(id) ON DELETE CASCADE
);

-- Message Signatures table
CREATE TABLE message_signatures (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    channel TEXT NOT NULL CHECK (channel IN ('email', 'whatsapp', 'sms')),
    content TEXT NOT NULL,
    html_content TEXT,
    is_default BOOLEAN DEFAULT 0,
    is_active BOOLEAN DEFAULT 1,
    uses_placeholders BOOLEAN DEFAULT 0,
    available_placeholders TEXT, -- JSON array
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    is_admin_only BOOLEAN DEFAULT 0,
    owner_user_id TEXT,
    FOREIGN KEY (created_by) REFERENCES users(user_id),
    FOREIGN KEY (owner_user_id) REFERENCES users(user_id)
);

-- Campaign Templates table
CREATE TABLE campaign_templates (
    id TEXT PRIMARY KEY,
    template_name TEXT NOT NULL,
    display_name TEXT NOT NULL,
    description TEXT,
    campaign_type TEXT NOT NULL CHECK (campaign_type IN ('newsletter', 'tax_advisory', 'promotion', 'event_invitation', 'birthday_wish', 'other')),
    status TEXT NOT NULL CHECK (status IN ('active', 'archived')),
    subject_template TEXT NOT NULL,
    email_content TEXT,
    email_content_type TEXT CHECK (email_content_type IN ('plain', 'html')),
    whatsapp_content_template TEXT,
    whatsapp_content_type TEXT CHECK (whatsapp_content_type IN ('plain', 'formatted')),
    sms_content_template TEXT,
    email_signature_id TEXT,
    whatsapp_signature_id TEXT,
    sms_signature_id TEXT,
    requires_approval BOOLEAN DEFAULT 0,
    sender_name TEXT,
    sender_email TEXT,
    sender_phone TEXT,
    merge_fields TEXT, -- JSON
    target_segments TEXT, -- JSON array
    interest_area_id TEXT,
    is_active BOOLEAN DEFAULT 1,
    is_public BOOLEAN DEFAULT 1,
    version INTEGER DEFAULT 1,
    uses_placeholders BOOLEAN DEFAULT 0,
    available_placeholders TEXT, -- JSON array
    created_by TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    attachments TEXT, -- JSON array
    is_recurring BOOLEAN DEFAULT 0,
    recurrence_type TEXT CHECK (recurrence_type IN ('monthly', 'specific_dates')),
    recurrence_days_of_month TEXT, -- JSON array
    recurrence_months_of_year TEXT, -- JSON array
    recurrence_specific_dates TEXT, -- JSON array
    is_admin_only BOOLEAN DEFAULT 0,
    owner_user_id TEXT,
    FOREIGN KEY (email_signature_id) REFERENCES message_signatures(id),
    FOREIGN KEY (whatsapp_signature_id) REFERENCES message_signatures(id),
    FOREIGN KEY (sms_signature_id) REFERENCES message_signatures(id),
    FOREIGN KEY (interest_area_id) REFERENCES areas_of_interest(id),
    FOREIGN KEY (created_by) REFERENCES users(user_id),
    FOREIGN KEY (owner_user_id) REFERENCES users(user_id)
);

-- Campaigns table
CREATE TABLE campaigns (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    subject TEXT NOT NULL,
    template_id TEXT,
    email_content TEXT,
    email_content_type TEXT CHECK (email_content_type IN ('plain', 'html')),
    whatsapp_content TEXT,
    whatsapp_content_type TEXT CHECK (whatsapp_content_type IN ('plain', 'formatted')),
    sms_content TEXT,
    email_signature_id TEXT,
    whatsapp_signature_id TEXT,
    sms_signature_id TEXT,
    status TEXT NOT NULL CHECK (status IN ('draft', 'scheduled', 'sending', 'sent', 'failed', 'archived')),
    campaign_type TEXT NOT NULL CHECK (campaign_type IN ('newsletter', 'tax_advisory', 'promotion', 'event_invitation', 'birthday_wish', 'other')),
    uses_placeholders BOOLEAN DEFAULT 0,
    available_placeholders TEXT, -- JSON array
    campaign_specific_placeholder_values TEXT, -- JSON
    sender_name TEXT, -- Campaign-specific sender name
    sender_email TEXT, -- Campaign-specific sender email
    sender_phone TEXT, -- Campaign-specific sender phone
    created_by TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    scheduled_date DATETIME,
    sent_date DATETIME,
    total_recipients INTEGER DEFAULT 0,
    opened INTEGER DEFAULT 0,
    clicked INTEGER DEFAULT 0,
    bounced INTEGER DEFAULT 0,
    target_subscriber_ids TEXT, -- JSON array
    attachments TEXT, -- JSON array
    birthday_send_offset_days INTEGER DEFAULT 0,
    birthday_send_time TEXT, -- HH:MM format
    holiday_handling_rule TEXT CHECK (holiday_handling_rule IN ('send_on_holiday', 'send_day_before_if_holiday', 'send_day_after_if_holiday')),
    triggered_from_template_recurrence_date TEXT,
    processed_recipients_count INTEGER DEFAULT 0,
    last_batch_sent_date DATETIME,
    sent_in_current_hour_count INTEGER DEFAULT 0,
    current_hour_window_start_date DATETIME,
    sent_in_current_day_count INTEGER DEFAULT 0,
    current_day_window_start_date DATETIME,
    next_batch_eligible_at DATETIME,
    is_admin_only BOOLEAN DEFAULT 0,
    owner_user_id TEXT,
    remarks TEXT, -- Campaign notes/remarks
    FOREIGN KEY (template_id) REFERENCES campaign_templates(id),
    FOREIGN KEY (email_signature_id) REFERENCES message_signatures(id),
    FOREIGN KEY (whatsapp_signature_id) REFERENCES message_signatures(id),
    FOREIGN KEY (sms_signature_id) REFERENCES message_signatures(id),
    FOREIGN KEY (created_by) REFERENCES users(user_id),
    FOREIGN KEY (owner_user_id) REFERENCES users(user_id)
);

-- Campaign Subscribers relationship table
CREATE TABLE campaign_subscribers (
    id TEXT PRIMARY KEY,
    campaign_id TEXT NOT NULL,
    subscriber_id TEXT NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('pending', 'sent', 'delivered', 'opened', 'clicked', 'failed', 'bounced')),
    personalized_subject TEXT,
    personalized_email_content TEXT,
    personalized_whatsapp_content TEXT,
    personalized_sms_content TEXT,
    sent_at DATETIME,
    delivered_at DATETIME,
    opened_at DATETIME,
    clicked_at DATETIME,
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (campaign_id) REFERENCES campaigns(id) ON DELETE CASCADE,
    FOREIGN KEY (subscriber_id) REFERENCES subscribers(id) ON DELETE CASCADE
);

-- Placeholder Managers table
CREATE TABLE placeholder_managers (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    display_name TEXT NOT NULL,
    description TEXT,
    context TEXT NOT NULL CHECK (context IN ('campaign', 'signature', 'both')),
    field_path TEXT NOT NULL,
    default_value TEXT,
    format_type TEXT NOT NULL CHECK (format_type IN ('text', 'date', 'currency', 'number', 'percentage')),
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Email Settings table
CREATE TABLE email_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    smtpServer TEXT NOT NULL,
    smtpPort INTEGER NOT NULL,
    encryption TEXT NOT NULL CHECK (encryption IN ('tls', 'ssl', 'none')),
    username TEXT NOT NULL,
    password_encrypted TEXT, -- Store encrypted password
    senderName TEXT NOT NULL,
    senderEmail TEXT NOT NULL,
    whatsAppNumber TEXT,
    smsNumber TEXT,
    dailyEmailLimit INTEGER DEFAULT 500,
    dailyWhatsAppLimit INTEGER DEFAULT 0,
    dailySmsLimit INTEGER DEFAULT 0,
    hourlyEmailLimit INTEGER DEFAULT 400,
    hourlyWhatsAppLimit INTEGER DEFAULT 400,
    hourlySmsLimit INTEGER DEFAULT 400,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Registration Details table
CREATE TABLE registration_details (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    isRegistered BOOLEAN DEFAULT 0,
    registeredName TEXT,
    registeredOrganizationName TEXT,
    registeredEmail TEXT,
    registeredPhoneNumber TEXT,
    registrationKey TEXT,
    registeredAddress TEXT,
    registeredCity TEXT,
    registeredPinCode TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Audit Log table
CREATE TABLE audit_log (
    id TEXT PRIMARY KEY,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    user_id TEXT,
    user_name TEXT NOT NULL,
    action TEXT NOT NULL CHECK (action IN ('CREATE', 'UPDATE', 'DELETE', 'EXPORT', 'IMPORT', 'SEND_MANUAL', 'SEND_BATCH', 'SYSTEM_ACTION', 'LOGIN', 'LOGOUT', 'SECURITY', 'CONFIGURATION')),
    entityType TEXT,
    entityId TEXT,
    entityName TEXT,
    description TEXT NOT NULL,
    details TEXT, -- JSON for old/new values
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- Display Settings table
CREATE TABLE display_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    autoResizeTableWidth BOOLEAN DEFAULT 1,
    showWeatherReport BOOLEAN DEFAULT 1,
    campaignQueueDays INTEGER DEFAULT 15,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- User Table Preferences
CREATE TABLE user_table_preferences (
    user_id TEXT,
    table_key TEXT,
    visible_columns TEXT, -- JSON array of column IDs
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, table_key),
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX idx_subscribers_email ON subscribers(email);
CREATE INDEX idx_subscribers_status ON subscribers(status);
CREATE INDEX idx_subscribers_birth_date ON subscribers(birthDate);
CREATE INDEX idx_campaigns_status ON campaigns(status);
CREATE INDEX idx_campaigns_created_by ON campaigns(created_by);
CREATE INDEX idx_campaigns_campaign_type ON campaigns(campaign_type);
CREATE INDEX idx_campaign_subscribers_campaign ON campaign_subscribers(campaign_id);
CREATE INDEX idx_campaign_subscribers_subscriber ON campaign_subscribers(subscriber_id);
CREATE INDEX idx_campaign_subscribers_status ON campaign_subscribers(status);
CREATE INDEX idx_audit_log_timestamp ON audit_log(timestamp);
CREATE INDEX idx_audit_log_user_id ON audit_log(user_id);
CREATE INDEX idx_audit_log_action ON audit_log(action);

-- Create triggers for updated_at fields
CREATE TRIGGER trigger_users_updated_at 
AFTER UPDATE ON users
BEGIN
    UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE user_id = NEW.user_id;
END;

CREATE TRIGGER trigger_subscribers_updated_at 
AFTER UPDATE ON subscribers
BEGIN
    UPDATE subscribers SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER trigger_campaigns_updated_at 
AFTER UPDATE ON campaigns
BEGIN
    UPDATE campaigns SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER trigger_campaign_templates_updated_at 
AFTER UPDATE ON campaign_templates
BEGIN
    UPDATE campaign_templates SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER trigger_areas_of_interest_updated_at 
AFTER UPDATE ON areas_of_interest
BEGIN
    UPDATE areas_of_interest SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER trigger_message_signatures_updated_at 
AFTER UPDATE ON message_signatures
BEGIN
    UPDATE message_signatures SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER trigger_campaign_subscribers_updated_at 
AFTER UPDATE ON campaign_subscribers
BEGIN
    UPDATE campaign_subscribers SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER trigger_email_settings_updated_at 
AFTER UPDATE ON email_settings
BEGIN
    UPDATE email_settings SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER trigger_registration_details_updated_at 
AFTER UPDATE ON registration_details
BEGIN
    UPDATE registration_details SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER trigger_display_settings_updated_at 
AFTER UPDATE ON display_settings
BEGIN
    UPDATE display_settings SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER trigger_user_table_preferences_updated_at 
AFTER UPDATE ON user_table_preferences
BEGIN
    UPDATE user_table_preferences SET updated_at = CURRENT_TIMESTAMP WHERE user_id = NEW.user_id AND table_key = NEW.table_key;
END;
