import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom'; 
import Header from '../components/Header';
import Table, { Column } from '../components/Table';
import ColumnSelector from '../components/ColumnSelector';
import { User, UserRole, AuditActionType, UserTablePreferences } from '../types';
import { PlusIcon, EditIcon, DeleteIcon, EyeIcon } from '../components/icons';
import { useAuth } from '../contexts/AuthContextDB'; 
import ConfirmationModal from '../components/ConfirmationModal';
import { userService } from '../services/userService-API';
import { addAuditLog } from '../utils/auditUtilsDB';
import { browserDatabaseService } from '../services/BrowserDatabaseService';

const USERS_TABLE_KEY = 'usersListTable';

/**
 * Professional Users Management Page
 * Database-backed user management with comprehensive CRUD operations
 * 
 * Migration Status: ✅ MIGRATED to Database
 * - Removed localStorage dependencies
 * - Added professional API integration
 * - Enhanced error handling and user feedback
 * - Implemented real-time data synchronization
 */
const UsersPage: React.FC = () => {
  const navigate = useNavigate(); 
  const { currentUser } = useAuth(); 
  const [searchTerm, setSearchTerm] = useState('');
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [feedbackMessage, setFeedbackMessage] = useState<string | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);
  const [visibleColumnIds, setVisibleColumnIds] = useState<string[]>([]);

  const defaultVisibleColumnIds = useMemo(() => [
    'user_id',
    'name',
    'email', 
    'role',
    'is_active',
    'actions' // Always included by default
  ], []);

  /**
   * Professional data fetching with comprehensive error handling
   */
  const fetchUsers = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      console.log('🔄 [UsersPage] Fetching users from database...');
      const response = await userService.getAllUsers();
      console.log('DEBUG - Users data:', response); // Add debug log
      
      if (response) {
        setUsers(response);
        console.log(`✅ [UsersPage] Successfully loaded ${response.length} users from database`);
        
        // Log successful data access
        await addAuditLog(AuditActionType.VIEW, 'Users', {
          description: 'Users list accessed',
          userId: currentUser?.user_id,
          details: { count: response.length }
        });
      } else {
        throw new Error('Failed to fetch users');
      }
    } catch (error: any) {
      console.error('❌ [UsersPage] Error fetching users:', error);
      setError(`Failed to load users: ${error.message}`);
      
      // Log error for audit trail
      await addAuditLog(AuditActionType.ERROR, 'Users', {
        description: 'Failed to fetch users list',
        userId: currentUser?.user_id,
        details: { error: error.message }
      });
    } finally {
      setIsLoading(false);
    }
  }, [currentUser?.user_id]);

  /**
   * Initialize component with database data
   */
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        // Load table preferences first
        if (currentUser?.user_id) {
          const sql = `
            SELECT table_preferences 
            FROM users 
            WHERE user_id = ?
          `;
          const result = await browserDatabaseService.query(sql, [currentUser.user_id]);
          if (result.length > 0 && result[0].table_preferences) {
            const preferences = JSON.parse(result[0].table_preferences);
            if (preferences[USERS_TABLE_KEY]) {
              console.log('DEBUG - Loaded saved preferences:', preferences[USERS_TABLE_KEY]);
              // Ensure actions column exists in loaded preferences
              const loadedIds = preferences[USERS_TABLE_KEY];
              if (!loadedIds.includes('actions')) {
                console.warn('⚠️ Actions column missing in saved preferences - adding it');
                loadedIds.push('actions');
              }
              setVisibleColumnIds(loadedIds);
            } else {
              console.log('Using default column visibility');
              setVisibleColumnIds(defaultVisibleColumnIds);
            }
          } else {
            console.log('No saved preferences found - using defaults');
            setVisibleColumnIds(defaultVisibleColumnIds);
          }
        }

        // Then load users
        await fetchUsers();
      } catch (error) {
        console.error('Error loading initial data:', error);
        setError('Failed to load initial data');
      }
    };

    loadInitialData();
  }, [currentUser, fetchUsers, defaultVisibleColumnIds]);

  /**
   * Professional feedback system
   */
  const showFeedback = useCallback((message: string, type: 'success' | 'error' | 'warning' = 'success') => {
    setFeedbackMessage(message);
    setTimeout(() => {
      setFeedbackMessage(null);
    }, 5000);
  }, []);

  /**
   * Add new user with permission checking
   */
  const handleAddUser = useCallback(() => {
    if (currentUser?.role !== UserRole.ADMIN) {
      showFeedback("Access Denied: Only Admins can add new users.", 'error');
      return;
    }
    navigate('/users/add'); 
  }, [currentUser?.role, navigate, showFeedback]);

  /**
   * View user (read-only mode)
   */
  const handleViewUser = useCallback(async (userId: string) => {
    const user = users.find(u => u.user_id === userId);
    if (user) {
      // Log user view access
      await addAuditLog(AuditActionType.VIEW, 'User', {
        entityId: userId,
        entityName: user.name,
        userId: currentUser?.user_id
      });
    }
    navigate(`/users/view/${userId}`);
  }, [users, currentUser?.user_id, navigate]);

  /**
   * Edit user with audit logging
   */
  const handleEditUser = useCallback(async (userId: string) => {
    const user = users.find(u => u.user_id === userId);
    if (user) {
      // Log user edit access
      await addAuditLog(AuditActionType.VIEW, 'User', {
        entityId: userId,
        entityName: user.name,
        description: 'User edit form accessed',
        userId: currentUser?.user_id
      });
    }
    navigate(`/users/edit/${userId}`);
  }, [users, currentUser?.user_id, navigate]);

  /**
   * Professional user deletion with database operations
   */
  const confirmDeleteUser = useCallback(async () => {
    if (!userToDelete) return;
    
    try {
      console.log(`🗑️ [UsersPage] Deleting user: ${userToDelete.user_id}`);
      
      const response = await userService.deleteUser(userToDelete.user_id);
      
      // Update local state
      setUsers(prevUsers => prevUsers.filter(u => u.user_id !== userToDelete.user_id));
      
      showFeedback(`User "${userToDelete.name}" deleted successfully.`, 'success');
      
      // Log successful deletion
      await addAuditLog(AuditActionType.DELETE, 'User', {
        entityId: userToDelete.user_id,
        entityName: userToDelete.name,
        description: `User "${userToDelete.name}" deleted`,
        userId: currentUser?.user_id
      });
      
      console.log(`✅ [UsersPage] User ${userToDelete.user_id} deleted successfully`);
    } catch (error: any) {
      console.error('❌ [UsersPage] Error deleting user:', error);
      showFeedback(`Failed to delete user: ${error.message}`, 'error');
      
      // Log deletion failure
      await addAuditLog(AuditActionType.ERROR, 'User', {
        entityId: userToDelete.user_id,
        entityName: userToDelete.name,
        description: `Failed to delete user: ${error.message}`,
        userId: currentUser?.user_id
      });
    } finally {
      setShowDeleteModal(false);
      setUserToDelete(null);
    }
  }, [userToDelete, currentUser?.user_id, showFeedback]);

  /**
   * Initiate user deletion with permission checking
   */
  const handleDeleteUser = useCallback(async (user: User) => {
    if (currentUser?.role !== UserRole.ADMIN) {
      showFeedback("Access Denied: Only Admins can delete users.", 'error');
      return;
    }
    
    if (user.user_id === currentUser?.user_id) {
      showFeedback("Error: Cannot delete your own account.", 'error');
      return;
    }
    
    // Log deletion attempt
    await addAuditLog(AuditActionType.VIEW, 'User', {
      entityId: user.user_id,
      entityName: user.name,
      description: 'User deletion confirmation requested',
      userId: currentUser?.user_id
    });
    
    setUserToDelete(user);
    setShowDeleteModal(true);
  }, [currentUser?.role, currentUser?.user_id, showFeedback]);

  /**
   * Professional search and filtering
   */
  const filteredUsers = useMemo(() => {
    if (!searchTerm.trim()) {
      return users;
    }
    
    const lowercasedFilter = searchTerm.toLowerCase();
    return users.filter(user =>
      user.user_id.toLowerCase().includes(lowercasedFilter) ||
      user.name.toLowerCase().includes(lowercasedFilter) ||
      (user.email && user.email.toLowerCase().includes(lowercasedFilter)) ||
      user.role.toLowerCase().includes(lowercasedFilter) ||
      (user.default_sender_name && user.default_sender_name.toLowerCase().includes(lowercasedFilter)) ||
      (user.default_sender_email && user.default_sender_email.toLowerCase().includes(lowercasedFilter))
    );
  }, [users, searchTerm]);

  /**
   * Professional table configuration
   */
  const handleColumnVisibilityChange = async (newVisibleIds: string[]) => {
    console.log('DEBUG - New visible columns before processing:', newVisibleIds);
    
    // Ensure actions column is always included and last
    const filteredIds = newVisibleIds.filter(id => id !== 'actions');
    const finalVisibleIds = [...filteredIds, 'actions'];
    
    console.log('DEBUG - Final visible columns after processing:', finalVisibleIds);
    
    // Force include actions column if somehow missing
    if (!finalVisibleIds.includes('actions')) {
      console.warn('⚠️ Actions column was missing - forcing inclusion');
      finalVisibleIds.push('actions');
    }
    
    setVisibleColumnIds(finalVisibleIds);
    
    if (currentUser?.user_id) {
      try {
        const sql = `
          UPDATE users 
          SET table_preferences = json_set(
            COALESCE(table_preferences, '{}'),
            '$."${USERS_TABLE_KEY}"',
            json(?)
          )
          WHERE user_id = ?
        `;
        await browserDatabaseService.query(sql, [JSON.stringify(finalVisibleIds), currentUser.user_id]);
        console.log(`✅ Saved column preferences for table ${USERS_TABLE_KEY}`);
      } catch (error) {
        console.error(`❌ Failed to save column preferences for table ${USERS_TABLE_KEY}:`, error);
      }
    }
  };

  const allTableColumns: Column<User>[] = [
    { 
      id: 'user_id', 
      header: 'User ID (Login)', 
      accessor: 'user_id', 
      sortable: true,
      sortValue: item => item.user_id.toLowerCase()
    },
    { 
      id: 'name', 
      header: 'Full Name', 
      accessor: 'name', 
      sortable: true 
    },
    { 
      id: 'email', 
      header: 'Email Address', 
      accessor: 'email', 
      sortable: true 
    },
    { 
      id: 'role', 
      header: 'Role', 
      accessor: 'role', 
      sortable: true 
    },
    { 
      id: 'is_active', 
      header: 'Status', 
      accessor: (user: User) => user.is_active ? 'Active' : 'Inactive',
      sortable: true,
      render: (user: User) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          user.is_active 
            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
            : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
        }`}>
          {user.is_active ? 'Active' : 'Inactive'}
        </span>
      )
    },
    { 
      id: 'last_login', 
      header: 'Last Login', 
      accessor: (user: User) => user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never',
      sortable: true,
      sortValue: (user: User) => user.last_login || '1900-01-01'
    },
    {
      id: 'actions',
      header: 'Actions',
      accessor: () => '',
      render: (user: User) => (
        <div className="flex space-x-2">
          <button
            onClick={() => handleViewUser(user.user_id)}
            className="text-green-600 hover:text-green-800"
            title="View User"
          >
            <EyeIcon className="h-4 w-4" />
          </button>
          <button
            onClick={() => handleEditUser(user.user_id)}
            className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
            title="Edit User"
          >
            <EditIcon className="h-4 w-4" />
          </button>
          {currentUser?.role === UserRole.ADMIN && user.user_id !== currentUser?.user_id && (
            <button
              onClick={() => handleDeleteUser(user)}
              className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
              title="Delete User"
            >
              <DeleteIcon className="h-4 w-4" />
            </button>
          )}
        </div>
      ),
      isNonRemovable: true,
    },
  ];

  /**
   * Professional loading state
   */
  if (isLoading) {
    return (
      <div className="space-y-6">
        <Header 
          title="Users" 
          description="Manage system users and their permissions" 
        />
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading users from database...</p>
          </div>
        </div>
      </div>
    );
  }

  /**
   * Professional error state
   */
  if (error) {
    return (
      <div className="space-y-6">
        <Header 
          title="Users" 
          description="Manage system users and their permissions" 
        />
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                Error Loading Users
              </h3>
              <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                <p>{error}</p>
              </div>
              <div className="mt-4">
                <button
                  onClick={fetchUsers}
                  className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-4 py-2 rounded-md text-sm font-medium hover:bg-red-200 dark:hover:bg-red-800 transition-colors"
                >
                  Try Again
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Header 
        title="Users" 
        description="Manage system users and their permissions"
        badge={users.length > 0 ? `${users.length} users` : undefined}
      />

      {/* Professional Feedback System */}
      {feedbackMessage && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-800 dark:text-green-200 px-4 py-3 rounded-lg">
          <p className="text-sm">{feedbackMessage}</p>
        </div>
      )}

      {/* Professional Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex-1 max-w-md">
            <input
              type="text"
              placeholder="Search users by ID, name, email, or role..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none"
            />
        </div>
        <div className="flex items-center gap-2">
          <ColumnSelector
            allColumns={allTableColumns}
            visibleColumnIds={visibleColumnIds}
            onSave={handleColumnVisibilityChange}
            defaultVisibleColumnIds={defaultVisibleColumnIds}
            tableKey={USERS_TABLE_KEY}
            userId={currentUser?.user_id}
          />
          {currentUser?.role === UserRole.ADMIN && (
            <button
              onClick={handleAddUser}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Add New User
            </button>
          )}
        </div>
      </div>

      {/* Professional Data Display */}
      {console.log('DEBUG - filteredUsers:', filteredUsers)} {/* Add debug log */}
      {filteredUsers.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-500 dark:text-gray-400">
            {searchTerm ? 'No users found matching your search criteria.' : 'No users found.'}
          </div>
          {searchTerm && (
            <button
              onClick={() => setSearchTerm('')}
              className="mt-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
            >
              Clear search
            </button>
          )}
        </div>
      ) : (
        <Table 
          data={filteredUsers} 
          allColumns={allTableColumns}
          visibleColumnIds={visibleColumnIds}
          userId={currentUser?.user_id}
        />
      )}

      {/* Professional Delete Confirmation */}
      {showDeleteModal && userToDelete && (
        <ConfirmationModal
          isOpen={showDeleteModal}
          onClose={() => {
            setShowDeleteModal(false);
            setUserToDelete(null);
          }}
          onConfirm={confirmDeleteUser}
          title="Delete User"
          message={`Are you sure you want to delete the user "${userToDelete.name}"? This action cannot be undone.`}
          confirmButtonText="Delete User"
          confirmButtonColor="red"
        />
      )}
    </div>
  );
};

export default UsersPage;
