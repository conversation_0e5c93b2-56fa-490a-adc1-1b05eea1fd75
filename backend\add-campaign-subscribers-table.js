// Add campaign_subscribers table to existing database
import Database from 'better-sqlite3';

const db = new Database('../crm4ca.db');

try {
  console.log('📊 Adding campaign_subscribers table...');

  // Create campaign_subscribers table
  db.exec(`
    CREATE TABLE IF NOT EXISTS campaign_subscribers (
      id TEXT PRIMARY KEY,
      campaign_id TEXT NOT NULL,
      subscriber_id TEXT NOT NULL,
      status TEXT NOT NULL CHECK (status IN ('pending', 'sent', 'delivered', 'opened', 'clicked', 'failed', 'bounced')),
      personalized_subject TEXT,
      personalized_email_content TEXT,
      personalized_whatsapp_content TEXT,
      personalized_sms_content TEXT,
      sent_at DATETIME,
      delivered_at DATETIME,
      opened_at DATETIME,
      clicked_at DATETIME,
      error_message TEXT,
      retry_count INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (campaign_id) REFERENCES campaigns(id) ON DELETE CASCADE,
      FOREIGN KEY (subscriber_id) REFERENCES subscribers(id) ON DELETE CASCADE
    );
  `);

  // Create indexes
  db.exec(`
    CREATE INDEX IF NOT EXISTS idx_campaign_subscribers_campaign_id ON campaign_subscribers(campaign_id);
    CREATE INDEX IF NOT EXISTS idx_campaign_subscribers_subscriber_id ON campaign_subscribers(subscriber_id);
    CREATE INDEX IF NOT EXISTS idx_campaign_subscribers_status ON campaign_subscribers(status);
  `);

  // Create trigger
  db.exec(`
    CREATE TRIGGER IF NOT EXISTS trigger_campaign_subscribers_updated_at
    AFTER UPDATE ON campaign_subscribers
    BEGIN
        UPDATE campaign_subscribers SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;
  `);

  console.log('✅ campaign_subscribers table added successfully');

  // Check if table exists
  const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='campaign_subscribers'").all();
  console.log('📋 Table exists:', tables.length > 0);

  // Show table structure
  const columns = db.prepare("PRAGMA table_info(campaign_subscribers)").all();
  console.log('📊 Table structure:');
  columns.forEach(col => {
    console.log(`  ${col.name}: ${col.type}`);
  });

} catch (error) {
  console.error('❌ Error adding table:', error);
} finally {
  db.close();
}
