1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.crmsms"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="33" />
10
11    <uses-permission android:name="android.permission.SEND_SMS" />
11-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:4:5-67
11-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:4:22-64
12    <uses-permission android:name="android.permission.INTERNET" />
12-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:5:5-67
12-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:5:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:6:5-79
13-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:6:22-76
14    <uses-permission android:name="android.permission.WAKE_LOCK" />
14-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:7:5-68
14-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:7:22-65
15
16    <permission
16-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
17        android:name="com.crmsms.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
17-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
18        android:protectionLevel="signature" />
18-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
19
20    <uses-permission android:name="com.crmsms.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
20-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
20-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
21
22    <application
22-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:9:5-26:19
23        android:allowBackup="true"
23-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:10:9-35
24        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
24-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6325cad999e7e25eaa9301d33937688b\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
25        android:debuggable="true"
26        android:extractNativeLibs="true"
27        android:icon="@mipmap/ic_launcher"
27-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:11:9-43
28        android:label="CRM SMS Gateway"
28-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:12:9-40
29        android:testOnly="true"
30        android:theme="@style/AppTheme"
30-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:13:9-40
31        android:usesCleartextTraffic="true" >
31-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:14:9-44
32        <activity
32-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:16:9-24:20
33            android:name="com.crmsms.MainActivity"
33-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:17:13-41
34            android:exported="true"
34-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:18:13-36
35            android:screenOrientation="portrait" >
35-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:19:13-49
36            <intent-filter>
36-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:20:13-23:29
37                <action android:name="android.intent.action.MAIN" />
37-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:21:17-69
37-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:21:25-66
38
39                <category android:name="android.intent.category.LAUNCHER" />
39-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:22:17-77
39-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:22:27-74
40            </intent-filter>
41        </activity>
42
43        <provider
43-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c9717b849fcad4dd51df8a06563b3cf\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
44            android:name="androidx.startup.InitializationProvider"
44-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c9717b849fcad4dd51df8a06563b3cf\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
45            android:authorities="com.crmsms.androidx-startup"
45-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c9717b849fcad4dd51df8a06563b3cf\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
46            android:exported="false" >
46-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c9717b849fcad4dd51df8a06563b3cf\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
47            <meta-data
47-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c9717b849fcad4dd51df8a06563b3cf\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
48                android:name="androidx.emoji2.text.EmojiCompatInitializer"
48-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c9717b849fcad4dd51df8a06563b3cf\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
49                android:value="androidx.startup" />
49-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c9717b849fcad4dd51df8a06563b3cf\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
50            <meta-data
50-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\8071ebbd9825c349788e01e00e130481\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
51                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
51-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\8071ebbd9825c349788e01e00e130481\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
52                android:value="androidx.startup" />
52-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\8071ebbd9825c349788e01e00e130481\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
53        </provider>
54    </application>
55
56</manifest>
