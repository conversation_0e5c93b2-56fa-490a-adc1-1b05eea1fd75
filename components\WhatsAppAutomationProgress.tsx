/**
 * WhatsApp Automation Progress Component
 * Real-time progress tracking for nut.js WhatsApp automation
 * 
 * @fileoverview Progress tracking UI for automated WhatsApp campaigns
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import React, { useState, useEffect } from 'react';
import { whatsappAutomationIntegration, AutomationProgress } from '../services/whatsapp-automation/WhatsAppAutomationIntegrationService';

interface WhatsAppAutomationProgressProps {
  isVisible: boolean;
  onClose: () => void;
  onStop: () => void;
}

const WhatsAppAutomationProgress: React.FC<WhatsAppAutomationProgressProps> = ({
  isVisible,
  onClose,
  onStop
}) => {
  const [progress, setProgress] = useState<AutomationProgress | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    if (!isVisible) return;

    // Subscribe to progress updates
    const unsubscribe = whatsappAutomationIntegration.subscribeToProgress(setProgress);

    return () => {
      unsubscribe();
    };
  }, [isVisible]);

  if (!isVisible || !progress) return null;

  const formatTime = (milliseconds: number): string => {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  const formatEstimatedTime = (milliseconds?: number): string => {
    if (!milliseconds) return 'Calculating...';
    return formatTime(milliseconds);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'text-blue-600';
      case 'completed': return 'text-green-600';
      case 'cancelled': return 'text-yellow-600';
      case 'error': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'initializing':
        return (
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
        );
      case 'running':
        return (
          <div className="flex space-x-1">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" style={{animationDelay: '0.1s'}}></div>
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
          </div>
        );
      case 'completed':
        return (
          <svg className="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        );
      case 'cancelled':
        return (
          <svg className="w-5 h-5 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'error':
        return (
          <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      default:
        return null;
    }
  };

  const progressPercentage = progress.totalCount > 0 
    ? (progress.currentIndex / progress.totalCount) * 100 
    : 0;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            {getStatusIcon(progress.status)}
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                WhatsApp Automation
              </h3>
              <p className={`text-sm ${getStatusColor(progress.status)}`}>
                {progress.status.charAt(0).toUpperCase() + progress.status.slice(1)}
              </p>
            </div>
          </div>
          
          {progress.status === 'completed' && (
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>

        {/* Progress Content */}
        <div className="p-6">
          
          {/* Progress Bar */}
          <div className="mb-4">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>Progress</span>
              <span>{progress.currentIndex} / {progress.totalCount}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progressPercentage}%` }}
              ></div>
            </div>
            <div className="text-center text-sm text-gray-500 mt-1">
              {Math.round(progressPercentage)}% Complete
            </div>
          </div>

          {/* Statistics */}
          <div className="grid grid-cols-3 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{progress.successCount}</div>
              <div className="text-sm text-gray-600">Successful</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{progress.failureCount}</div>
              <div className="text-sm text-gray-600">Failed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-600">
                {progress.totalCount - progress.currentIndex}
              </div>
              <div className="text-sm text-gray-600">Remaining</div>
            </div>
          </div>

          {/* Current Contact */}
          {progress.currentContact && progress.status === 'running' && (
            <div className="bg-blue-50 border border-blue-200 rounded-md p-3 mb-4">
              <div className="text-sm font-medium text-blue-900">
                Currently sending to:
              </div>
              <div className="text-sm text-blue-700">
                {progress.currentContact.name} ({progress.currentContact.phone})
              </div>
              <div className="text-xs text-blue-600 mt-1">
                {progress.currentContact.message.substring(0, 60)}...
              </div>
            </div>
          )}

          {/* Time Information */}
          <div className="space-y-2 text-sm text-gray-600">
            <div className="flex justify-between">
              <span>Started:</span>
              <span>{progress.startTime.toLocaleTimeString()}</span>
            </div>
            {progress.estimatedTimeRemaining && progress.status === 'running' && (
              <div className="flex justify-between">
                <span>Estimated remaining:</span>
                <span>{formatEstimatedTime(progress.estimatedTimeRemaining)}</span>
              </div>
            )}
          </div>

          {/* Details Toggle */}
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="w-full mt-4 text-sm text-blue-600 hover:text-blue-700 border-t border-gray-200 pt-3"
          >
            {showDetails ? 'Hide Details' : 'Show Details'}
          </button>

          {/* Details Section */}
          {showDetails && (
            <div className="mt-4 space-y-2 text-sm text-gray-600 bg-gray-50 p-3 rounded-md">
              <div className="flex justify-between">
                <span>Session ID:</span>
                <span className="font-mono text-xs">{progress.status}</span>
              </div>
              <div className="flex justify-between">
                <span>Success Rate:</span>
                <span>
                  {progress.currentIndex > 0 
                    ? Math.round((progress.successCount / progress.currentIndex) * 100)
                    : 0}%
                </span>
              </div>
              <div className="flex justify-between">
                <span>Avg Time per Message:</span>
                <span>
                  {progress.currentIndex > 1 
                    ? formatTime((Date.now() - progress.startTime.getTime()) / progress.currentIndex)
                    : 'Calculating...'}
                </span>
              </div>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200">
          {progress.status === 'running' && (
            <button
              onClick={onStop}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
            >
              Stop Automation
            </button>
          )}
          
          {(progress.status === 'completed' || progress.status === 'cancelled' || progress.status === 'error') && (
            <button
              onClick={onClose}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Close
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default WhatsAppAutomationProgress;