// CRM4CA Development Configuration
// This file controls development-mode settings for easier testing and development

export const DEV_CONFIG = {
  // ADMIN DEFAULT ACCESS CONFIGURATION
  // Set to true to enable automatic admin login without authentication
  ENABLE_ADMIN_DEFAULT: true,
  
  // BYPASS CONFIGURATION
  // Set to true to skip registration and login requirements
  BYPASS_REGISTRATION: true,
  BYPASS_AUTHENTICATION: true,
  
  // DEFAULT ADMIN USER SETTINGS
  DEFAULT_ADMIN_USER_ID: 'admin',
  DEFAULT_ADMIN_NAME: 'Alice Wonderland',
  DEFAULT_ADMIN_EMAIL: '<EMAIL>',
  DEFAULT_ADMIN_PASSWORD: 'admin123',
  
  // DEVELOPMENT MODE INDICATORS
  SHOW_DEV_INDICATORS: false, // Set to true only when debugging is needed
  CONSOLE_LOG_AUTH: false,     // Set to true only when debugging authentication
  
  // INSTRUCTIONS FOR PRODUCTION
  PRODUCTION_NOTES: {
    registration: 'Change BYPASS_REGISTRATION to false and use: if (!isAppRegistered)',
    authentication: 'Change BYPASS_AUTHENTICATION to false and use: if (!isAuthenticated)',
    adminDefault: 'Change ENABLE_ADMIN_DEFAULT to false to require proper login',
    security: 'Update all development bypasses before production deployment'
  }
} as const;

// Development Mode Status Check
export const isDevelopmentMode = () => {
  return DEV_CONFIG.ENABLE_ADMIN_DEFAULT || 
         DEV_CONFIG.BYPASS_REGISTRATION || 
         DEV_CONFIG.BYPASS_AUTHENTICATION;
};

// Get Development Status Summary
export const getDevStatusSummary = () => {
  return {
    mode: isDevelopmentMode() ? 'DEVELOPMENT' : 'PRODUCTION',
    adminDefault: DEV_CONFIG.ENABLE_ADMIN_DEFAULT,
    bypassRegistration: DEV_CONFIG.BYPASS_REGISTRATION,
    bypassAuthentication: DEV_CONFIG.BYPASS_AUTHENTICATION,
    defaultUser: DEV_CONFIG.DEFAULT_ADMIN_USER_ID
  };
};

// Console logging for development mode
export const logDevStatus = () => {
  if (DEV_CONFIG.CONSOLE_LOG_AUTH && isDevelopmentMode()) {
    const status = getDevStatusSummary();
    console.group('🔧 CRM4CA Development Mode Status');
    console.log('📊 Mode:', status.mode);
    console.log('👤 Admin Default:', status.adminDefault ? '✅ Enabled' : '❌ Disabled');
    console.log('🚪 Registration Bypass:', status.bypassRegistration ? '✅ Active' : '❌ Inactive');
    console.log('🔐 Authentication Bypass:', status.bypassAuthentication ? '✅ Active' : '❌ Inactive');
    console.log('👑 Default User:', status.defaultUser);
    console.log('⚠️  Remember to disable development mode for production!');
    console.groupEnd();
  }
};

export default DEV_CONFIG;
