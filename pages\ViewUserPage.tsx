import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { User } from '../types';
import { userService } from '../services/userService';
import { ArrowLeftIcon } from '../components/icons';

const ViewUserPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadUser = async () => {
      if (!id) {
        setError('No user ID provided');
        setLoading(false);
        return;
      }

      try {
        const userData = await userService.getUserById(id);
        setUser(userData);
      } catch (err) {
        console.error('Error loading user:', err);
        setError('Failed to load user details');
      } finally {
        setLoading(false);
      }
    };

    loadUser();
  }, [id]);

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not set';
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return dateString;
    }
  };

  const getRoleBadge = (role: string) => {
    const roleColors = {
      'admin': 'bg-red-100 text-red-800',
      'user': 'bg-blue-100 text-blue-800',
      'viewer': 'bg-gray-100 text-gray-800'
    };
    
    return (
      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
        roleColors[role as keyof typeof roleColors] || 'bg-gray-100 text-gray-800'
      }`}>
        {role.charAt(0).toUpperCase() + role.slice(1)}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-textPrimary">Loading user details...</div>
      </div>
    );
  }

  if (error || !user) {
    return (
      <div className="min-h-screen bg-background p-6">
        <div className="max-w-4xl mx-auto">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h2 className="text-lg font-semibold text-red-800 mb-2">Error</h2>
            <p className="text-red-600">{error || 'User not found'}</p>
            <button
              onClick={() => navigate('/users')}
              className="mt-4 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
            >
              Back to Users
            </button>
          </div>
        </div>
      </div>
    );
  }

  const fieldClass = "block text-sm font-medium text-textPrimary mb-1";
  const valueClass = "w-full p-3 border border-border rounded-lg bg-gray-50 text-textPrimary";

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-4xl mx-auto p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate('/users')}
              className="flex items-center text-primary hover:text-opacity-80"
            >
              <ArrowLeftIcon className="h-5 w-5 mr-2" />
              Back to Users
            </button>
            <h1 className="text-2xl font-bold text-textPrimary">View User</h1>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => navigate(`/users/edit/${user.user_id}`)}
              className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-opacity-90"
            >
              Edit User
            </button>
          </div>
        </div>

        {/* User Details */}
        <div className="bg-cardBackground border border-border rounded-lg shadow-sm">
          <div className="p-6">
            <h2 className="text-lg font-semibold text-textPrimary mb-6">User Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Basic Information */}
              <div>
                <label className={fieldClass}>Full Name</label>
                <div className={valueClass}>{user.name}</div>
              </div>

              <div>
                <label className={fieldClass}>Email Address</label>
                <div className={valueClass}>{user.email}</div>
              </div>

              <div>
                <label className={fieldClass}>Username</label>
                <div className={valueClass}>{user.username}</div>
              </div>

              <div>
                <label className={fieldClass}>Role</label>
                <div className={valueClass}>{getRoleBadge(user.role)}</div>
              </div>

              <div>
                <label className={fieldClass}>Status</label>
                <div className={valueClass}>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    user.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {user.is_active ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </div>

              <div>
                <label className={fieldClass}>User ID</label>
                <div className={valueClass}>{user.user_id}</div>
              </div>

              {/* Timestamps */}
              <div>
                <label className={fieldClass}>Created At</label>
                <div className={valueClass}>{formatDate(user.created_at)}</div>
              </div>

              <div>
                <label className={fieldClass}>Last Updated</label>
                <div className={valueClass}>{formatDate(user.updated_at)}</div>
              </div>

              <div>
                <label className={fieldClass}>Last Login</label>
                <div className={valueClass}>{formatDate(user.last_login_at)}</div>
              </div>
            </div>

            {/* Permissions Section */}
            <div className="mt-6">
              <h3 className="text-lg font-semibold text-textPrimary mb-4">Permissions & Access</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-semibold text-blue-800 mb-2">Role Permissions</h4>
                  <div className="text-sm text-blue-700">
                    {user.role === 'admin' && (
                      <ul className="list-disc list-inside space-y-1">
                        <li>Full system access</li>
                        <li>User management</li>
                        <li>System configuration</li>
                        <li>All campaign operations</li>
                      </ul>
                    )}
                    {user.role === 'user' && (
                      <ul className="list-disc list-inside space-y-1">
                        <li>Create and manage campaigns</li>
                        <li>Manage subscribers</li>
                        <li>View reports</li>
                        <li>Limited system access</li>
                      </ul>
                    )}
                    {user.role === 'viewer' && (
                      <ul className="list-disc list-inside space-y-1">
                        <li>View-only access</li>
                        <li>Read reports</li>
                        <li>No editing permissions</li>
                      </ul>
                    )}
                  </div>
                </div>

                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <h4 className="font-semibold text-green-800 mb-2">Account Status</h4>
                  <div className="text-sm text-green-700">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className={`w-2 h-2 rounded-full ${user.is_active ? 'bg-green-500' : 'bg-red-500'}`}></span>
                      <span>{user.is_active ? 'Account Active' : 'Account Inactive'}</span>
                    </div>
                    <div className="text-xs text-green-600">
                      {user.is_active 
                        ? 'User can log in and access the system' 
                        : 'User account is disabled and cannot log in'
                      }
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Activity Summary */}
            <div className="mt-6">
              <h3 className="text-lg font-semibold text-textPrimary mb-4">Activity Summary</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                  <div className="text-2xl font-bold text-purple-800">
                    {user.last_login_at ? 'Recent' : 'Never'}
                  </div>
                  <div className="text-sm text-purple-600">Last Login</div>
                </div>
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                  <div className="text-2xl font-bold text-orange-800">{user.role}</div>
                  <div className="text-sm text-orange-600">Access Level</div>
                </div>
                <div className="bg-teal-50 border border-teal-200 rounded-lg p-4">
                  <div className="text-2xl font-bold text-teal-800">
                    {user.is_active ? 'Active' : 'Inactive'}
                  </div>
                  <div className="text-sm text-teal-600">Current Status</div>
                </div>
              </div>
            </div>

            {/* Security Information */}
            <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h3 className="text-sm font-semibold text-yellow-800 mb-2">Security Information</h3>
              <p className="text-sm text-yellow-700">
                Password and sensitive security information are not displayed for security reasons. 
                Use the Edit User function to update password or security settings.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewUserPage;
