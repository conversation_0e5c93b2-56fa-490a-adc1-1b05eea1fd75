
import React, { useState, useEffect } from 'react';
import { CheckCircleIcon, XCircleIcon, CogIcon, DevicePhoneMobileIcon } from '../components/icons';
import { SMSService, SMSConfig, initializeSMSService, loadSMSConfig, saveSMSConfig } from '../services/smsService';

interface SMSConfig {
  enabled: boolean;
  provider: 'myphoneexplorer' | 'twilio' | 'aws-sns' | 'textlocal' | 'msg91' | '';
  // MyPhoneExplorer configuration
  myPhoneExplorerPath: string;
  myPhoneExplorerConnected: boolean;
  myPhoneExplorerPhoneNumber: string;
  // Other providers
  twilioAccountSid: string;
  twilioAuthToken: string;
  twilioPhoneNumber: string;
  awsAccessKeyId: string;
  awsSecretAccessKey: string;
  awsRegion: string;
  textlocalApiKey: string;
  textlocalSender: string;
  msg91ApiKey: string;
  msg91SenderId: string;
  defaultCountryCode: string;
}

const smsProviders = [
  { value: 'myphoneexplorer', label: 'MyPhoneExplorer', description: 'Send SMS through your Android phone using MyPhoneExplorer software (Recommended)' },
  { value: 'twilio', label: 'Twilio', description: 'Global SMS service with excellent delivery rates' },
  { value: 'aws-sns', label: 'AWS SNS', description: 'Amazon Simple Notification Service' },
  { value: 'textlocal', label: 'Textlocal', description: 'UK and European SMS provider' },
  { value: 'msg91', label: 'MSG91', description: 'Indian SMS service provider' }
];

const SMSConfigurationPage: React.FC = () => {
  const [config, setConfig] = useState<SMSConfig>({
    provider: '',
    enabled: false,
    // MyPhoneExplorer defaults
    myPhoneExplorerPath: 'C:\\Program Files (x86)\\MyPhoneExplorer\\MyPhoneExplorer.exe',
    myPhoneExplorerConnected: false,
    myPhoneExplorerPhoneNumber: '',
    // Twilio defaults
    twilioAccountSid: '',
    twilioAuthToken: '',
    twilioPhoneNumber: '',
    // AWS SNS defaults
    awsAccessKeyId: '',
    awsSecretAccessKey: '',
    awsRegion: 'us-east-1',
    // Textlocal defaults
    textlocalApiKey: '',
    textlocalSender: '',
    // MSG91 defaults
    msg91ApiKey: '',
    msg91SenderId: '',
    defaultCountryCode: '91'
  });

  const [testMessage, setTestMessage] = useState({
    phone: '',
    message: 'Hello! This is a test message from CRM4CA.'
  });

  const [connectionStatus, setConnectionStatus] = useState<'idle' | 'testing' | 'success' | 'error'>('idle');
  const [statusMessage, setStatusMessage] = useState('');
  const [diagnosticsInfo, setDiagnosticsInfo] = useState<any>(null);
  const [showDiagnostics, setShowDiagnostics] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [globalSMSEnabled, setGlobalSMSEnabled] = useState<boolean>(true);
  const [showConfirmationModal, setShowConfirmationModal] = useState<boolean>(false);
  const [pendingToggleState, setPendingToggleState] = useState<boolean>(false);
  const [smsService, setSmsService] = useState<SMSService | null>(null);

  useEffect(() => {
    // Load existing configuration
    loadConfiguration();
  }, []);

  const loadConfiguration = async () => {
    try {
      // Load from service
      const savedConfig = loadSMSConfig();
      if (savedConfig) {
        setConfig(savedConfig);
        
        // Initialize SMS service if configuration is complete
        if (savedConfig.enabled && savedConfig.provider) {
          const service = initializeSMSService(savedConfig);
          setSmsService(service);
        }
      }
    } catch (error) {
      console.error('Error loading SMS configuration:', error);
      setStatusMessage('Error loading configuration');
    }
  };

  const handleConfigChange = (field: keyof SMSConfig, value: string | boolean) => {
    setConfig(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      // Save configuration using service
      saveSMSConfig(config);
      
      // Initialize SMS service if configuration is complete
      if (config.enabled && config.provider) {
        const service = initializeSMSService(config);
        setSmsService(service);
        
        // Test connection
        const isConnected = await service.testConnection();
        if (isConnected) {
          setStatusMessage('Configuration saved and connection successful!');
          setConnectionStatus('success');
        } else {
          setStatusMessage('Configuration saved but connection test failed');
          setConnectionStatus('error');
        }
      } else {
        setStatusMessage('Configuration saved successfully!');
        setSmsService(null);
      }
      
      setTimeout(() => {
        setStatusMessage('');
        if (connectionStatus !== 'error') {
          setConnectionStatus('idle');
        }
      }, 3000);
    } catch (error) {
      console.error('Error saving SMS configuration:', error);
      setStatusMessage('Error saving configuration');
      setConnectionStatus('error');
    } finally {
      setIsSaving(false);
    }
  };

  const handleSendTestMessage = async () => {
    if (!testMessage.phone || !testMessage.message) {
      setStatusMessage('Please enter phone number and message');
      return;
    }

    if (!config.enabled || !config.provider) {
      setStatusMessage('Please configure and save SMS provider first');
      return;
    }

    setConnectionStatus('testing');
    setStatusMessage('Sending test SMS...');

    try {
      // Create a fresh SMS service instance with current config
      const service = initializeSMSService(config);
      const result = await service.sendTestSMS(testMessage.phone, testMessage.message);

      if (result.success) {
        setConnectionStatus('success');
        setStatusMessage(`✅ Test SMS sent successfully! Message ID: ${result.messageId}`);
      } else {
        setConnectionStatus('error');
        setStatusMessage(`❌ Failed to send test SMS: ${result.error}`);
      }
    } catch (error) {
      setConnectionStatus('error');
      setStatusMessage(`❌ Error sending test SMS: ${error instanceof Error ? error.message : 'Unknown error'}`);
      console.error('❌ SMS service error:', error);
    }
  };

  const getRequiredFieldsForProvider = (provider: string): string[] => {
    switch (provider) {
      case 'myphoneexplorer':
        return ['myPhoneExplorerPath', 'myPhoneExplorerPhoneNumber'];
      case 'twilio':
        return ['twilioAccountSid', 'twilioAuthToken', 'twilioPhoneNumber'];
      case 'aws-sns':
        return ['awsAccessKeyId', 'awsSecretAccessKey', 'awsRegion'];
      case 'textlocal':
        return ['textlocalApiKey', 'textlocalSender'];
      case 'msg91':
        return ['msg91ApiKey', 'msg91SenderId'];
      default:
        return [];
    }
  };

  const renderProviderFields = () => {
    switch (config.provider) {
      case 'myphoneexplorer':
        return (
          <div className="space-y-4">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-800 mb-2">📞 MyPhoneExplorer Setup</h4>
              <div className="text-sm text-blue-700 space-y-1">
                <p>1. Install MyPhoneExplorer on your PC and Android phone</p>
                <p>2. Connect your Android phone via USB or WiFi</p>
                <p>3. Test SMS sending manually in MyPhoneExplorer first</p>
                <p>4. CRM will use command-line automation to send SMS</p>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-textPrimary mb-2">
                MyPhoneExplorer Path <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={config.myPhoneExplorerPath || ''}
                onChange={(e) => handleConfigChange('myPhoneExplorerPath', e.target.value)}
                className="w-full px-3 py-2 border border-default rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="C:\Program Files (x86)\MyPhoneExplorer\MyPhoneExplorer.exe"
              />
              <div className="text-xs text-textSecondary mt-1">
                Full path to MyPhoneExplorer.exe on your PC
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-textPrimary mb-2">
                Phone Number <span className="text-red-500">*</span>
              </label>
              <input
                type="tel"
                value={config.myPhoneExplorerPhoneNumber || ''}
                onChange={(e) => handleConfigChange('myPhoneExplorerPhoneNumber', e.target.value)}
                className="w-full px-3 py-2 border border-default rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="+919876543210"
              />
              <div className="text-xs text-textSecondary mt-1">
                Phone number of your Android device connected to MyPhoneExplorer
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-textPrimary mb-2">
                Connection Status
              </label>
              <div className={`px-3 py-2 rounded-lg text-sm ${
                config.myPhoneExplorerConnected
                  ? 'bg-green-50 text-green-700 border border-green-200'
                  : 'bg-yellow-50 text-yellow-700 border border-yellow-200'
              }`}>
                {config.myPhoneExplorerConnected
                  ? '✅ Phone connected to MyPhoneExplorer'
                  : '⚠️ Please connect your phone in MyPhoneExplorer'}
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-800 mb-2">📞 Download MyPhoneExplorer</h4>
              <div className="space-y-2">
                <div>
                  <a
                    href="https://www.fjsoft.at/en/downloads.php"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 underline font-medium"
                  >
                    📥 Download MyPhoneExplorer for PC
                  </a>
                  <div className="text-xs text-blue-600 mt-1">Free desktop software for Windows</div>
                </div>
                <div>
                  <a
                    href="https://play.google.com/store/apps/details?id=com.fjsoft.myphoneexplorer.client"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 underline font-medium"
                  >
                    📱 Download MyPhoneExplorer Client for Android
                  </a>
                  <div className="text-xs text-blue-600 mt-1">Free Android app from Google Play Store</div>
                </div>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-textPrimary mb-2">
                Default Country Code
              </label>
              <input
                type="text"
                value={config.defaultCountryCode || ''}
                onChange={(e) => handleConfigChange('defaultCountryCode', e.target.value)}
                className="w-full px-3 py-2 border border-default rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="91"
                maxLength={3}
              />
              <div className="text-xs text-textSecondary mt-1">
                Default country code for phone numbers (without +)
              </div>
            </div>
          </div>
        );
      case 'twilio':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-textPrimary mb-2">
                Account SID <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={config.twilioAccountSid}
                onChange={(e) => handleConfigChange('twilioAccountSid', e.target.value)}
                className="w-full px-3 py-2 border border-default rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="Enter your Twilio Account SID"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-textPrimary mb-2">
                Auth Token <span className="text-red-500">*</span>
              </label>
              <input
                type="password"
                value={config.twilioAuthToken}
                onChange={(e) => handleConfigChange('twilioAuthToken', e.target.value)}
                className="w-full px-3 py-2 border border-default rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="Enter your Twilio Auth Token"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-textPrimary mb-2">
                Phone Number <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={config.twilioPhoneNumber}
                onChange={(e) => handleConfigChange('twilioPhoneNumber', e.target.value)}
                className="w-full px-3 py-2 border border-default rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="e.g., +**********"
              />
            </div>
          </div>
        );
      // Add other provider cases here...
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <DevicePhoneMobileIcon className="h-8 w-8 text-blue-600" />
        <div>
          <h1 className="text-3xl font-bold text-textPrimary">SMS Configuration</h1>
          <p className="text-textSecondary mt-1">
            Configure SMS providers for sending text messages via mobile phone or cloud services
          </p>
        </div>
      </div>

      {/* Status Message */}
      {statusMessage && (
        <div className="p-4 rounded-lg flex items-center space-x-3 bg-blue-50 text-blue-800 border border-blue-200">
          <span>{statusMessage}</span>
        </div>
      )}

      {/* Global SMS Toggle */}
      <div className={`bg-surface rounded-lg shadow-sm border border-default p-6 ${globalSMSEnabled ? 'border-l-4 border-l-green-500' : 'border-l-4 border-l-red-500'}`}>
        <h3 className="text-xl font-semibold text-textPrimary mb-4">Global SMS Control</h3>
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <h4 className="text-lg font-medium text-textPrimary">SMS Channel Status</h4>
            <p className="text-sm text-textSecondary mt-1">
              {globalSMSEnabled
                ? "SMS sending is globally enabled. All campaigns and templates can send SMS messages."
                : "SMS sending is globally disabled. No SMS messages will be sent regardless of campaign/template settings."
              }
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <span className={`text-sm font-medium ${globalSMSEnabled ? 'text-green-600' : 'text-red-600'}`}>
              {globalSMSEnabled ? 'ENABLED' : 'DISABLED'}
            </span>
            <button
              type="button"
              onClick={() => {
                if (globalSMSEnabled) {
                  // Disabling - show confirmation
                  setPendingToggleState(false);
                  setShowConfirmationModal(true);
                } else {
                  // Enabling - no confirmation needed
                  setGlobalSMSEnabled(true);
                }
              }}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${
                globalSMSEnabled ? 'bg-green-600' : 'bg-gray-200'
              }`}
              aria-label={`Toggle SMS globally ${globalSMSEnabled ? 'off' : 'on'}`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  globalSMSEnabled ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Configuration Form */}
        <div className="bg-surface rounded-lg shadow-sm border border-default p-6 space-y-6">
          <h2 className="text-xl font-semibold text-textPrimary">SMS Provider Configuration</h2>

          {/* Enable SMS */}
          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="enabled"
              checked={config.enabled}
              onChange={(e) => handleConfigChange('enabled', e.target.checked)}
              className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
            />
            <label htmlFor="enabled" className="text-sm font-medium text-textPrimary">
              Enable SMS Integration
            </label>
          </div>

          {/* Provider Selection */}
          {config.enabled && (
            <div>
              <label className="block text-sm font-medium text-textPrimary mb-2">
                SMS Provider <span className="text-red-500">*</span>
              </label>
              <select
                value={config.provider}
                onChange={(e) => handleConfigChange('provider', e.target.value)}
                className="w-full px-3 py-2 border border-default rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="">Select a provider</option>
                {smsProviders.map(provider => (
                  <option key={provider.value} value={provider.value}>
                    {provider.label}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* Provider-specific fields */}
          {config.enabled && config.provider && renderProviderFields()}

          {/* Test Connection and Save Buttons */}
          {config.enabled && config.provider && (
            <div className="space-y-3">
              {/* Test Connection Button */}
              <button
                onClick={async () => {
                  if (!config.enabled || !config.provider) return;
                  
                  setConnectionStatus('testing');
                  setStatusMessage('Running comprehensive diagnostics...');
                  setShowDiagnostics(false);
                  
                  try {
                    const service = initializeSMSService(config);
                    
                    // Run detailed diagnostics
                    const diagnostics = await service.performDiagnostics();
                    setDiagnosticsInfo(diagnostics);
                    
                    if (diagnostics.success) {
                      setConnectionStatus('success');
                      setStatusMessage('✅ Connection test successful! All systems operational.');
                    } else {
                      setConnectionStatus('error');
                      const issueCount = diagnostics.issues.length;
                      setStatusMessage(`❌ Connection test failed with ${issueCount} issue${issueCount !== 1 ? 's' : ''}. Click "View Diagnostics" for details.`);
                      setShowDiagnostics(true);
                    }
                  } catch (error) {
                    setConnectionStatus('error');
                    setStatusMessage(`❌ Diagnostics failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
                    setDiagnosticsInfo({
                      success: false,
                      issues: ['Unexpected error during diagnostics'],
                      suggestions: ['Check console for detailed error information'],
                      details: { error: error instanceof Error ? error.message : 'Unknown error' }
                    });
                    setShowDiagnostics(true);
                  }
                }}
                disabled={connectionStatus === 'testing'}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {connectionStatus === 'testing' ? 'Running Diagnostics...' : 'Test Connection & Diagnose'}
              </button>
              
              {/* Save Button */}
              <button
                onClick={handleSave}
                disabled={isSaving}
                className="w-full px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSaving ? 'Saving...' : 'Save Configuration'}
              </button>
            </div>
          )}
        </div>

        {/* Test SMS */}
        <div className="bg-surface rounded-lg shadow-sm border border-default p-6 space-y-6">
          <h2 className="text-xl font-semibold text-textPrimary">Test SMS</h2>

          <div>
            <label className="block text-sm font-medium text-textPrimary mb-2">
              Phone Number <span className="text-red-500">*</span>
            </label>
            <input
              type="tel"
              value={testMessage.phone}
              onChange={(e) => setTestMessage(prev => ({ ...prev, phone: e.target.value }))}
              className="w-full px-3 py-2 border border-default rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="e.g., +**********"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-textPrimary mb-2">
              Message <span className="text-red-500">*</span>
            </label>
            <textarea
              value={testMessage.message}
              onChange={(e) => setTestMessage(prev => ({ ...prev, message: e.target.value }))}
              rows={3}
              className="w-full px-3 py-2 border border-default rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="Enter your test message"
            />
            <div className="text-xs text-textSecondary mt-1">
              {testMessage.message.length}/160 characters
            </div>
          </div>

          <div className="border-t border-default pt-4">
            <button
              onClick={handleSendTestMessage}
              disabled={connectionStatus === 'testing' || !config.enabled || !config.provider}
              className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {connectionStatus === 'testing' ? 'Sending...' : 'Send Test SMS'}
            </button>
          </div>

          {/* Diagnostics Display */}
          {showDiagnostics && diagnosticsInfo && (
            <div className="border-t border-default pt-4">
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-medium text-red-800">🔍 Connection Diagnostics</h3>
                  <button
                    onClick={() => setShowDiagnostics(false)}
                    className="text-red-500 hover:text-red-700"
                  >
                    ✕
                  </button>
                </div>

                {/* Issues */}
                {diagnosticsInfo.issues.length > 0 && (
                  <div className="mb-4">
                    <h4 className="font-medium text-red-800 mb-2">❌ Issues Found:</h4>
                    <ul className="list-disc pl-5 space-y-1">
                      {diagnosticsInfo.issues.map((issue: string, index: number) => (
                        <li key={index} className="text-red-700 text-sm">{issue}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Suggestions */}
                {diagnosticsInfo.suggestions.length > 0 && (
                  <div className="mb-4">
                    <h4 className="font-medium text-red-800 mb-2">💡 Suggested Solutions:</h4>
                    <ol className="list-decimal pl-5 space-y-1">
                      {diagnosticsInfo.suggestions.map((suggestion: string, index: number) => (
                        <li key={index} className="text-red-700 text-sm">{suggestion}</li>
                      ))}
                    </ol>
                  </div>
                )}

                {/* Technical Details */}
                {diagnosticsInfo.details && (
                  <div className="text-xs text-red-600 bg-red-100 p-3 rounded">
                    <strong>Technical Details:</strong>
                    <br />URL: {diagnosticsInfo.details.configuredUrl}
                    <br />Status: {diagnosticsInfo.details.connectionStatus}
                    {diagnosticsInfo.details.httpResponse && (
                      <>
                        <br />HTTP: {diagnosticsInfo.details.httpResponse.status} {diagnosticsInfo.details.httpResponse.statusText}
                      </>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Provider Comparison */}
          <div className="border-t border-default pt-4">
            <h3 className="text-lg font-medium text-textPrimary mb-3">Provider Comparison</h3>
            <div className="space-y-3">
              {smsProviders.map(provider => (
                <div key={provider.value} className="flex justify-between items-center p-3 border border-default rounded-lg">
                  <div>
                    <div className="font-medium text-textPrimary">{provider.label}</div>
                    <div className="text-sm text-textSecondary">{provider.description}</div>
                  </div>
                  <div className="text-sm text-textSecondary">
                    {provider.value === 'myphoneexplorer' && 'Android Phone'}
                    {provider.value === 'twilio' && 'Global'}
                    {provider.value === 'aws-sns' && 'AWS'}
                    {provider.value === 'textlocal' && 'UK/EU'}
                    {provider.value === 'msg91' && 'India'}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Integration Status */}
      <div className="bg-surface rounded-lg shadow-sm border border-default p-6">
        <h2 className="text-xl font-semibold text-textPrimary mb-4">Integration Status</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 border border-default rounded-lg">
            <div className={`inline-flex items-center justify-center w-8 h-8 rounded-full mb-2 ${
              config.enabled && config.provider ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'
            }`}>
              {config.enabled && config.provider ? <CheckCircleIcon className="h-5 w-5" /> : <XCircleIcon className="h-5 w-5" />}
            </div>
            <div className="text-sm font-medium text-textPrimary">Provider Selected</div>
            <div className="text-xs text-textSecondary">
              {config.enabled && config.provider ? config.provider.toUpperCase() : 'Not Selected'}
            </div>
          </div>

          <div className="text-center p-4 border border-default rounded-lg">
            <div className={`inline-flex items-center justify-center w-8 h-8 rounded-full mb-2 ${
              connectionStatus === 'success' ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'
            }`}>
              {connectionStatus === 'success' ? <CheckCircleIcon className="h-5 w-5" /> : <XCircleIcon className="h-5 w-5" />}
            </div>
            <div className="text-sm font-medium text-textPrimary">Connection Test</div>
            <div className="text-xs text-textSecondary">
              {connectionStatus === 'success' ? 'Successful' : 'Not Tested'}
            </div>
          </div>

          <div className="text-center p-4 border border-default rounded-lg">
            <div className={`inline-flex items-center justify-center w-8 h-8 rounded-full mb-2 ${
              config.enabled && getRequiredFieldsForProvider(config.provider).every(field => config[field as keyof SMSConfig]) 
                ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'
            }`}>
              {config.enabled && getRequiredFieldsForProvider(config.provider).every(field => config[field as keyof SMSConfig]) 
                ? <CheckCircleIcon className="h-5 w-5" /> : <XCircleIcon className="h-5 w-5" />}
            </div>
            <div className="text-sm font-medium text-textPrimary">Configuration</div>
            <div className="text-xs text-textSecondary">
              {config.enabled && getRequiredFieldsForProvider(config.provider).every(field => config[field as keyof SMSConfig]) 
                ? 'Complete' : 'Incomplete'}
            </div>
          </div>
        </div>
      </div>

      {/* MyPhoneExplorer Setup Helper */}
      {config.provider === 'myphoneexplorer' && config.enabled && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-6">
          <h3 className="font-medium text-green-800 mb-3">📞 MyPhoneExplorer Setup Guide</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="bg-white p-4 rounded-lg border">
              <h4 className="font-medium text-green-800 mb-2">1. Install Software</h4>
              <ul className="list-disc pl-5 space-y-1 text-green-700">
                <li>Download MyPhoneExplorer for PC</li>
                <li>Install MyPhoneExplorer Client on Android</li>
                <li>Grant SMS and phone permissions</li>
                <li>Launch both applications</li>
              </ul>
            </div>

            <div className="bg-white p-4 rounded-lg border">
              <h4 className="font-medium text-green-800 mb-2">2. Connect Phone</h4>
              <ul className="list-disc pl-5 space-y-1 text-green-700">
                <li>Connect via USB cable or WiFi</li>
                <li>Follow connection wizard in PC app</li>
                <li>Test connection in MyPhoneExplorer</li>
                <li>Verify SMS access is working</li>
              </ul>
            </div>

            <div className="bg-white p-4 rounded-lg border">
              <h4 className="font-medium text-green-800 mb-2">3. Test SMS Manually</h4>
              <ul className="list-disc pl-5 space-y-1 text-green-700">
                <li>Open SMS tab in MyPhoneExplorer</li>
                <li>Send a test SMS manually</li>
                <li>Verify message is sent successfully</li>
                <li>Check message appears in phone's sent folder</li>
              </ul>
            </div>

            <div className="bg-white p-4 rounded-lg border">
              <h4 className="font-medium text-green-800 mb-2">4. CRM Integration</h4>
              <ul className="list-disc pl-5 space-y-1 text-green-700">
                <li>Enter MyPhoneExplorer.exe path</li>
                <li>Enter your phone number</li>
                <li>Test connection in CRM</li>
                <li>Send test SMS from CRM</li>
              </ul>
            </div>
          </div>

          <div className="mt-4 p-3 bg-green-100 rounded-lg">
            <p className="text-green-800 text-sm">
              <strong>✅ Advantages of MyPhoneExplorer:</strong>
              <br />• Uses your existing phone and SIM card
              <br />• No network connectivity issues
              <br />• Command-line automation support
              <br />• Free software with reliable SMS sending
              <br />• Works with any Android phone
            </p>
          </div>
        </div>
      )}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="font-medium text-blue-800 mb-3">SMS Best Practices</h3>
          <ul className="text-sm text-blue-700 space-y-2">
            <li>• Keep messages under 160 characters to avoid splitting</li>
            <li>• Include opt-out instructions (e.g., "Reply STOP to unsubscribe")</li>
            <li>• Send during business hours (9 AM - 6 PM)</li>
            <li>• Use clear, professional language</li>
            <li>• Personalize messages when possible</li>
          </ul>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <h3 className="font-medium text-yellow-800 mb-3">Pricing Guidelines</h3>
          <ul className="text-sm text-yellow-700 space-y-2">
            <li>• MyPhoneExplorer: Cost of SMS from your mobile plan (usually free or very low cost)</li>
            <li>• Twilio: ~$0.0075 per SMS (varies by country)</li>
            <li>• AWS SNS: $0.00645 per SMS (US pricing)</li>
            <li>• Textlocal: £0.03-£0.05 per SMS (UK pricing)</li>
            <li>• MSG91: ₹0.15-₹0.25 per SMS (India pricing)</li>
            <li>• Rates vary by destination country</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default SMSConfigurationPage;