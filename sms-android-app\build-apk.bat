@echo off
echo ========================================
echo CRM SMS Gateway Android App Builder
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if React Native CLI is installed
npx react-native --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing React Native CLI...
    npm install -g react-native-cli
)

REM Check if Android SDK is available
if not exist "%ANDROID_HOME%" (
    echo WARNING: ANDROID_HOME environment variable not set
    echo Please install Android Studio and set ANDROID_HOME
    echo Example: set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
    echo.
)

echo Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo Choose build type:
echo 1. Debug APK (for testing)
echo 2. Release APK (optimized)
echo.
set /p choice="Enter your choice (1 or 2): "

if "%choice%"=="1" (
    echo Building Debug APK...
    cd android
    call gradlew assembleDebug
    if %errorlevel% equ 0 (
        echo.
        echo ========================================
        echo SUCCESS: Debug APK built successfully!
        echo ========================================
        echo Location: android\app\build\outputs\apk\debug\app-debug.apk
        echo.
        echo You can now install this APK on your Android device:
        echo 1. Enable "Install from Unknown Sources" in Android settings
        echo 2. Transfer the APK to your device
        echo 3. Install the APK
        echo.
    ) else (
        echo ERROR: Failed to build Debug APK
    )
) else if "%choice%"=="2" (
    echo Building Release APK...
    cd android
    call gradlew assembleRelease
    if %errorlevel% equ 0 (
        echo.
        echo ========================================
        echo SUCCESS: Release APK built successfully!
        echo ========================================
        echo Location: android\app\build\outputs\apk\release\app-release.apk
        echo.
        echo You can now install this APK on your Android device:
        echo 1. Enable "Install from Unknown Sources" in Android settings
        echo 2. Transfer the APK to your device
        echo 3. Install the APK
        echo.
    ) else (
        echo ERROR: Failed to build Release APK
    )
) else (
    echo Invalid choice. Please run the script again.
)

echo.
echo Build process completed.
pause
