import express from 'express';
import { database } from '../database/connection.js';

const router = express.Router();

// Get all subscribers
router.get('/', async (req, res) => {
  try {
    const subscribers = await database.all(`
      SELECT s.*, 
             GROUP_CONCAT(aoi.name) as area_names
      FROM subscribers s
      LEFT JOIN subscriber_areas_of_interest saoi ON s.id = saoi.subscriber_id
      LEFT JOIN areas_of_interest aoi ON saoi.area_of_interest_id = aoi.id
      GROUP BY s.id
      ORDER BY s.created_at DESC
    `);
    
    // Parse JSON fields and format response
    const parsedSubscribers = subscribers.map(subscriber => ({
      ...subscriber,
      customFields: subscriber.customFields ? JSON.parse(subscriber.customFields) : {},
      areasOfInterestIds: [], // Will be populated separately
      area_names: subscriber.area_names ? subscriber.area_names.split(',') : [],
      allowWhatsApp: <PERSON><PERSON><PERSON>(subscriber.allowWhatsApp),
      allowSms: <PERSON><PERSON><PERSON>(subscriber.allowSms),
      is_admin_only: <PERSON><PERSON><PERSON>(subscriber.is_admin_only)
    }));
    
    // Get areas of interest for each subscriber
    for (const subscriber of parsedSubscribers) {
      const areas = await database.all(`
        SELECT area_of_interest_id 
        FROM subscriber_areas_of_interest 
        WHERE subscriber_id = ?
      `, [subscriber.id]);
      subscriber.areasOfInterestIds = areas.map(a => a.area_of_interest_id);
    }
    
    res.json(parsedSubscribers);
  } catch (error) {
    console.error('Error fetching subscribers:', error);
    res.status(500).json({ error: 'Failed to fetch subscribers' });
  }
});

// Get subscribers by IDs (batch fetch)
router.post('/batch-get', async (req, res) => {
  try {
    const { ids } = req.body;
    
    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({ error: 'IDs array is required' });
    }
    
    // Create placeholders for the SQL query
    const placeholders = ids.map(() => '?').join(',');
    const subscribers = await database.all(`
      SELECT s.*, 
             GROUP_CONCAT(aoi.name) as area_names
      FROM subscribers s
      LEFT JOIN subscriber_areas_of_interest saoi ON s.id = saoi.subscriber_id
      LEFT JOIN areas_of_interest aoi ON saoi.area_of_interest_id = aoi.id
      WHERE s.id IN (${placeholders})
      GROUP BY s.id
      ORDER BY s.created_at DESC
    `, ids);
    
    // Parse JSON fields and format response
    const parsedSubscribers = subscribers.map(subscriber => ({
      ...subscriber,
      customFields: subscriber.customFields ? JSON.parse(subscriber.customFields) : {},
      areasOfInterestIds: [], // Will be populated separately
      area_names: subscriber.area_names ? subscriber.area_names.split(',') : [],
      allowWhatsApp: Boolean(subscriber.allowWhatsApp),
      allowSms: Boolean(subscriber.allowSms),
      is_admin_only: Boolean(subscriber.is_admin_only)
    }));
    
    // Get areas of interest for each subscriber
    for (const subscriber of parsedSubscribers) {
      const areas = await database.all(`
        SELECT area_of_interest_id 
        FROM subscriber_areas_of_interest 
        WHERE subscriber_id = ?
      `, [subscriber.id]);
      subscriber.areasOfInterestIds = areas.map(a => a.area_of_interest_id);
    }
    
    res.json(parsedSubscribers);
  } catch (error) {
    console.error('Error fetching subscribers by IDs:', error);
    res.status(500).json({ error: 'Failed to fetch subscribers by IDs' });
  }
});

// Get subscriber by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const subscriber = await database.get('SELECT * FROM subscribers WHERE id = ?', [id]);
    
    if (!subscriber) {
      return res.status(404).json({ error: 'Subscriber not found' });
    }
    
    // Get areas of interest
    const areas = await database.all(`
      SELECT area_of_interest_id 
      FROM subscriber_areas_of_interest 
      WHERE subscriber_id = ?
    `, [id]);
    
    const parsedSubscriber = {
      ...subscriber,
      customFields: subscriber.customFields ? JSON.parse(subscriber.customFields) : {},
      areasOfInterestIds: areas.map(a => a.area_of_interest_id),
      allowWhatsApp: Boolean(subscriber.allowWhatsApp),
      allowSms: Boolean(subscriber.allowSms),
      is_admin_only: Boolean(subscriber.is_admin_only)
    };
    
    res.json(parsedSubscriber);
  } catch (error) {
    console.error('Error fetching subscriber:', error);
    res.status(500).json({ error: 'Failed to fetch subscriber' });
  }
});

// Create new subscriber
router.post('/', async (req, res) => {
  try {
    const subscriberData = req.body;
    const now = new Date().toISOString();
    const subscriberId = `sub-${Date.now()}`;
    
    // Insert subscriber
    await database.run(`
      INSERT INTO subscribers (
        id, email, firstName, lastName, entityName, phone, birthDate,
        status, subscribed_at, customFields, allowWhatsApp, allowSms,
        created_at, updated_at, is_admin_only, owner_user_id
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      subscriberId,
      subscriberData.email,
      subscriberData.firstName || null,
      subscriberData.lastName || null,
      subscriberData.entityName || null,
      subscriberData.phone || null,
      subscriberData.birthDate || null,
      subscriberData.status || 'active',
      subscriberData.subscribed_at || now,
      JSON.stringify(subscriberData.customFields || {}),
      subscriberData.allowWhatsApp ? 1 : 0,
      subscriberData.allowSms ? 1 : 0,
      now,
      now,
      subscriberData.is_admin_only ? 1 : 0,
      subscriberData.owner_user_id || null
    ]);
    
    // Insert areas of interest relationships
    if (subscriberData.areasOfInterestIds && subscriberData.areasOfInterestIds.length > 0) {
      for (const areaId of subscriberData.areasOfInterestIds) {
        await database.run(`
          INSERT INTO subscriber_areas_of_interest (subscriber_id, area_of_interest_id)
          VALUES (?, ?)
        `, [subscriberId, areaId]);
      }
    }
    
    // Fetch the created subscriber
    const newSubscriber = await database.get('SELECT * FROM subscribers WHERE id = ?', [subscriberId]);
    
    const parsedSubscriber = {
      ...newSubscriber,
      customFields: newSubscriber.customFields ? JSON.parse(newSubscriber.customFields) : {},
      areasOfInterestIds: subscriberData.areasOfInterestIds || [],
      allowWhatsApp: Boolean(newSubscriber.allowWhatsApp),
      allowSms: Boolean(newSubscriber.allowSms),
      is_admin_only: Boolean(newSubscriber.is_admin_only)
    };
    
    res.status(201).json(parsedSubscriber);
  } catch (error) {
    console.error('Error creating subscriber:', error);
    if (error.code === 'SQLITE_CONSTRAINT_UNIQUE') {
      res.status(409).json({ error: 'Email already exists' });
    } else {
      res.status(500).json({ error: 'Failed to create subscriber' });
    }
  }
});

// Update subscriber
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const subscriberData = req.body;
    const now = new Date().toISOString();
    
    // Check if subscriber exists
    const existingSubscriber = await database.get('SELECT id FROM subscribers WHERE id = ?', [id]);
    if (!existingSubscriber) {
      return res.status(404).json({ error: 'Subscriber not found' });
    }
    
    // Update subscriber
    await database.run(`
      UPDATE subscribers SET
        email = ?, firstName = ?, lastName = ?, entityName = ?, phone = ?,
        birthDate = ?, status = ?, customFields = ?, allowWhatsApp = ?,
        allowSms = ?, is_admin_only = ?, owner_user_id = ?, updated_at = ?
      WHERE id = ?
    `, [
      subscriberData.email,
      subscriberData.firstName || null,
      subscriberData.lastName || null,
      subscriberData.entityName || null,
      subscriberData.phone || null,
      subscriberData.birthDate || null,
      subscriberData.status || 'active',
      JSON.stringify(subscriberData.customFields || {}),
      subscriberData.allowWhatsApp ? 1 : 0,
      subscriberData.allowSms ? 1 : 0,
      subscriberData.is_admin_only ? 1 : 0,
      subscriberData.owner_user_id || null,
      now,
      id
    ]);
    
    // Update areas of interest relationships
    await database.run('DELETE FROM subscriber_areas_of_interest WHERE subscriber_id = ?', [id]);
    
    if (subscriberData.areasOfInterestIds && subscriberData.areasOfInterestIds.length > 0) {
      for (const areaId of subscriberData.areasOfInterestIds) {
        await database.run(`
          INSERT INTO subscriber_areas_of_interest (subscriber_id, area_of_interest_id)
          VALUES (?, ?)
        `, [id, areaId]);
      }
    }
    
    // Fetch updated subscriber
    const updatedSubscriber = await database.get('SELECT * FROM subscribers WHERE id = ?', [id]);
    
    const parsedSubscriber = {
      ...updatedSubscriber,
      customFields: updatedSubscriber.customFields ? JSON.parse(updatedSubscriber.customFields) : {},
      areasOfInterestIds: subscriberData.areasOfInterestIds || [],
      allowWhatsApp: Boolean(updatedSubscriber.allowWhatsApp),
      allowSms: Boolean(updatedSubscriber.allowSms),
      is_admin_only: Boolean(updatedSubscriber.is_admin_only)
    };
    
    res.json(parsedSubscriber);
  } catch (error) {
    console.error('Error updating subscriber:', error);
    if (error.code === 'SQLITE_CONSTRAINT_UNIQUE') {
      res.status(409).json({ error: 'Email already exists' });
    } else {
      res.status(500).json({ error: 'Failed to update subscriber' });
    }
  }
});

// Batch create subscribers
router.post('/batch', async (req, res) => {
  try {
    const { subscribers: subscribersData } = req.body;
    
    if (!Array.isArray(subscribersData) || subscribersData.length === 0) {
      return res.status(400).json({ error: 'Invalid or empty subscribers array' });
    }
    
    const now = new Date().toISOString();
    const createdSubscribers = [];
    const errors = [];
    
    // Use transaction for atomic operation
    await database.run('BEGIN TRANSACTION');
    
    try {
      for (let i = 0; i < subscribersData.length; i++) {
        const subscriberData = subscribersData[i];
        const subscriberId = `sub-${Date.now()}-${i}`;
        
        try {
          // Insert subscriber
          await database.run(`
            INSERT INTO subscribers (
              id, email, firstName, lastName, entityName, phone, birthDate,
              status, subscribed_at, customFields, allowWhatsApp, allowSms,
              created_at, updated_at, is_admin_only, owner_user_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `, [
            subscriberId,
            subscriberData.email,
            subscriberData.firstName || null,
            subscriberData.lastName || null,
            subscriberData.entityName || null,
            subscriberData.phone || null,
            subscriberData.birthDate || null,
            subscriberData.status || 'active',
            subscriberData.subscribed_at || now,
            JSON.stringify(subscriberData.customFields || {}),
            subscriberData.allowWhatsApp ? 1 : 0,
            subscriberData.allowSms ? 1 : 0,
            now,
            now,
            subscriberData.is_admin_only ? 1 : 0,
            subscriberData.owner_user_id || null
          ]);
          
          // Insert areas of interest relationships
          if (subscriberData.areasOfInterestIds && subscriberData.areasOfInterestIds.length > 0) {
            for (const areaId of subscriberData.areasOfInterestIds) {
              await database.run(`
                INSERT INTO subscriber_areas_of_interest (subscriber_id, area_of_interest_id)
                VALUES (?, ?)
              `, [subscriberId, areaId]);
            }
          }
          
          createdSubscribers.push({
            id: subscriberId,
            email: subscriberData.email,
            index: i
          });
          
        } catch (error) {
          if (error.code === 'SQLITE_CONSTRAINT_UNIQUE') {
            errors.push({
              index: i,
              email: subscriberData.email,
              error: 'Email already exists'
            });
          } else {
            throw error; // Re-throw for transaction rollback
          }
        }
      }
      
      await database.run('COMMIT');
      
      res.status(201).json({
        message: 'Batch import completed',
        created: createdSubscribers.length,
        errors: errors.length,
        createdSubscribers,
        errorDetails: errors
      });
      
    } catch (error) {
      await database.run('ROLLBACK');
      throw error;
    }
    
  } catch (error) {
    console.error('Error in batch create subscribers:', error);
    console.error('Error details:', {
      message: error.message,
      code: error.code,
      stack: error.stack
    });
    res.status(500).json({
      error: 'Failed to batch create subscribers',
      details: error.message
    });
  }
});

// Batch update subscriber areas of interest
router.patch('/batch-areas', async (req, res) => {
  try {
    const { updates } = req.body;
    
    if (!Array.isArray(updates) || updates.length === 0) {
      return res.status(400).json({ error: 'Invalid or empty updates array' });
    }
    
    // Use transaction for atomic operation
    await database.run('BEGIN TRANSACTION');
    
    try {
      for (const update of updates) {
        const { id, areasOfInterestIds } = update;
        
        if (!id) {
          throw new Error('Subscriber ID is required for each update');
        }
        
        // Check if subscriber exists
        const existingSubscriber = await database.get('SELECT id FROM subscribers WHERE id = ?', [id]);
        if (!existingSubscriber) {
          throw new Error(`Subscriber with ID ${id} not found`);
        }
        
        // Delete existing areas of interest for this subscriber
        await database.run('DELETE FROM subscriber_areas_of_interest WHERE subscriber_id = ?', [id]);
        
        // Insert new areas of interest relationships
        if (areasOfInterestIds && areasOfInterestIds.length > 0) {
          for (const areaId of areasOfInterestIds) {
            await database.run(`
              INSERT INTO subscriber_areas_of_interest (subscriber_id, area_of_interest_id)
              VALUES (?, ?)
            `, [id, areaId]);
          }
        }
        
        // Update the updated_at timestamp
        const now = new Date().toISOString();
        await database.run('UPDATE subscribers SET updated_at = ? WHERE id = ?', [now, id]);
      }
      
      await database.run('COMMIT');
      
      res.json({ 
        message: 'Batch area updates completed successfully',
        updated: updates.length 
      });
      
    } catch (error) {
      await database.run('ROLLBACK');
      throw error;
    }
    
  } catch (error) {
    console.error('Error in batch update subscriber areas:', error);
    res.status(500).json({ error: `Failed to batch update subscriber areas: ${error.message}` });
  }
});

// Delete subscriber
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Check if subscriber exists
    const existingSubscriber = await database.get('SELECT id FROM subscribers WHERE id = ?', [id]);
    if (!existingSubscriber) {
      return res.status(404).json({ error: 'Subscriber not found' });
    }
    
    await database.run('DELETE FROM subscribers WHERE id = ?', [id]);
    res.json({ message: 'Subscriber deleted successfully' });
  } catch (error) {
    console.error('Error deleting subscriber:', error);
    res.status(500).json({ error: 'Failed to delete subscriber' });
  }
});

export default router;
export { router as subscriberRoutes };
