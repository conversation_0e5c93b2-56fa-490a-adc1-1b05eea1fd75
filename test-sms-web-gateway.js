// Test SMS Web Gateway functionality
const baseUrl = 'http://localhost:3001';

async function testSMSWebGateway() {
  console.log('🧪 Testing SMS Web Gateway...\n');

  try {
    // Test 1: Check if SMS web gateway loads
    console.log('1. Testing SMS Web Gateway accessibility...');
    const gatewayResponse = await fetch(`${baseUrl}/sms-web-gateway/index.html`);
    
    if (gatewayResponse.ok) {
      console.log('   ✅ SMS Web Gateway is accessible');
      
      // Check CSP headers
      const csp = gatewayResponse.headers.get('content-security-policy');
      if (csp && csp.includes('unsafe-inline')) {
        console.log('   ✅ CSP allows inline scripts');
      } else {
        console.log('   ⚠️ CSP might be restrictive');
      }
    } else {
      console.log('   ❌ SMS Web Gateway not accessible');
    }

    // Test 2: Check SMS API endpoints
    console.log('\n2. Testing SMS API endpoints...');
    
    const statusResponse = await fetch(`${baseUrl}/api/sms/status`);
    if (statusResponse.ok) {
      const status = await statusResponse.json();
      console.log('   ✅ SMS API Status:', status.status);
    }

    const pendingResponse = await fetch(`${baseUrl}/api/sms/pending`);
    if (pendingResponse.ok) {
      const pending = await pendingResponse.json();
      console.log('   ✅ Pending SMS messages:', pending.length);
    }

    const statsResponse = await fetch(`${baseUrl}/api/sms/stats`);
    if (statsResponse.ok) {
      const stats = await statsResponse.json();
      console.log('   ✅ SMS Statistics:', stats.overall);
    }

    // Test 3: Test Traccar configuration endpoint
    console.log('\n3. Testing Traccar endpoints...');
    
    const traccarTestResponse = await fetch(`${baseUrl}/api/sms/traccar/test`);
    if (traccarTestResponse.ok) {
      const traccarTest = await traccarTestResponse.json();
      console.log('   📱 Traccar status:', traccarTest.success ? 'Ready' : 'Not configured');
    }

    console.log('\n🎉 SMS Web Gateway testing complete!');
    console.log('\n📋 Next steps:');
    console.log('1. Open SMS Web Gateway in browser: http://localhost:3001/sms-web-gateway/index.html');
    console.log('2. Install Traccar SMS Gateway on Android device');
    console.log('3. Configure Traccar in web interface');
    console.log('4. Test SMS sending');

  } catch (error) {
    console.error('❌ Error testing SMS Web Gateway:', error);
  }
}

// Run the test
testSMSWebGateway();
