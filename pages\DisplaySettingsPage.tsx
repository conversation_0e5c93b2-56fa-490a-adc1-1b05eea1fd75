import React, { useState } from 'react';
import Header from '../components/Header';
import Button from '../components/Button';
import ToastNotification from '../components/ToastNotification';
import { useDisplaySettings } from '../contexts/DisplaySettingsContext';
import { useAuth } from '../contexts/AuthContextDB';
import { apiClient } from '../services/apiClient';
import {
  AdjustmentsHorizontalIcon,
  EyeIcon,
  SunIcon,
  ClockIcon,
} from '../components/icons';

const DisplaySettingsPage: React.FC = () => {
  const { displaySettings, toggleAutoResizeTableWidth, toggleShowWeatherReport, setCampaignQueueDays } =
    useDisplaySettings();
  const { currentUser } = useAuth();
  const [queueDaysInput, setQueueDaysInput] = useState(
    displaySettings?.campaignQueueDays?.toString() || '15'
  );
  const [isSchedulerLoading, setIsSchedulerLoading] = useState(false);
  const [notification, setNotification] = useState<{ type: 'success' | 'error'; message: string } | null>(null);

  // Class names for consistent styling
  const sectionClass = "p-6 bg-surface shadow-md rounded-lg border border-border";
  const sectionHeaderClass = "text-lg font-semibold leading-6 text-textPrimary mb-4 flex items-center";
  const labelClass = "block text-sm font-medium text-textPrimary";
  const helpTextClass = "mt-1 text-xs text-textSecondary";
  const toggleContainerClass =
    "flex items-center justify-between p-4 border border-border rounded-lg hover:bg-surface-hover transition-colors";
  const inputContainerClass =
    "flex items-center justify-between p-4 border border-border rounded-lg hover:bg-surface-hover transition-colors";
  const switchClass =
    "relative inline-flex items-center h-6 rounded-full w-11 transition-colors focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary";
  const switchSliderClass = "inline-block w-4 h-4 transform bg-white rounded-full transition-transform";
  const inputClass =
    "w-20 px-3 py-1 text-sm border border-border rounded bg-background text-textPrimary";

  const handleQueueDaysChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQueueDaysInput(value);

    const days = parseInt(value);
    if (!isNaN(days) && days >= 1 && days <= 365) {
      setCampaignQueueDays(days);
    }
  };

  const triggerScheduler = async () => {
    if (!currentUser) {
      setNotification({ type: 'error', message: 'Please login to run the scheduler' });
      return;
    }

    setIsSchedulerLoading(true);
    setNotification(null);

    try {
      const response = await apiClient.post('/trigger-scheduler');
      if (response.status === 200) {
        setNotification({
          type: 'success',
          message: response.data.message || 'Scheduler executed successfully',
        });
      } else {
        setNotification({
          type: 'error',
          message: response.data.message || 'Failed to execute scheduler',
        });
      }
    } catch (error: any) {
      let errorMessage = 'Network error while triggering scheduler';
      if (error.response) {
        switch (error.response.status) {
          case 404:
            errorMessage = 'Scheduler endpoint not found.';
            break;
          case 500:
            errorMessage = 'Server error. Please try again later.';
            break;
          case 401:
            errorMessage = 'Authentication required. Please login again.';
            break;
          default:
            errorMessage = error.response.data?.message || `Server error (${error.response.status})`;
        }
      } else if (error.request) {
        errorMessage = 'Cannot connect to server. Ensure backend is running.';
      }
      setNotification({ type: 'error', message: errorMessage });
    } finally {
      setIsSchedulerLoading(false);
    }
  };

  const autoResizeChecked = displaySettings.autoResizeTableWidth;
  const weatherReportChecked = displaySettings.showWeatherReport;

  return (
    <div className="space-y-8 text-textPrimary">
      <Header
        title="Display Settings"
        subtitle="Customize how information is displayed across the application."
      />

      {/* Table Display Options */}
      <div className={sectionClass}>
        <h3 className={sectionHeaderClass}>
          <AdjustmentsHorizontalIcon className="h-6 w-6 mr-3 text-primary" />
          Table Display Options
        </h3>

        <div className={toggleContainerClass}>
          <div>
            <label htmlFor="autoResizeTableWidth" className={labelClass}>
              Auto Resize Tables to Fit Page Width
            </label>
            <p className={helpTextClass}>
              When enabled, tables will attempt to fit all columns within the page width by wrapping text and adjusting column sizes automatically. Manual column resizing will be disabled.
            </p>
          </div>
          <button
            type="button"
            id="autoResizeTableWidth"
            className={`${switchClass} ${
              autoResizeChecked ? 'bg-primary' : 'bg-gray-300 dark:bg-gray-600'
            }`}
            onClick={toggleAutoResizeTableWidth}
            role="switch"
            aria-checked={autoResizeChecked}
            aria-label="Toggle auto resize table width"
          >
            <span className="sr-only">Auto Resize Tables to Fit Page Width</span>
            <span
              className={`${switchSliderClass} ${
                autoResizeChecked ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>
      </div>

      {/* Dashboard Settings */}
      <div className={sectionClass}>
        <h3 className={sectionHeaderClass}>
          <SunIcon className="h-6 w-6 mr-3 text-yellow-500" />
          Dashboard Settings
        </h3>

        <div className={toggleContainerClass}>
          <div>
            <label htmlFor="showWeatherReport" className={labelClass}>
              Show Weather Report on Dashboard
            </label>
            <p className={helpTextClass}>
              Display a weather report widget on the main dashboard. Weather is determined by your registered PIN code or city.
            </p>
          </div>
          <button
            type="button"
            id="showWeatherReport"
            className={`${switchClass} ${
              weatherReportChecked ? 'bg-primary' : 'bg-gray-300 dark:bg-gray-600'
            }`}
            onClick={toggleShowWeatherReport}
            role="switch"
            aria-checked={weatherReportChecked}
            aria-label="Toggle show weather report on dashboard"
          >
            <span className="sr-only">Show Weather Report on Dashboard</span>
            <span
              className={`${switchSliderClass} ${
                weatherReportChecked ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>
      </div>

      {/* Campaign Scheduling Settings */}
      <div className={sectionClass}>
        <h3 className={sectionHeaderClass}>
          <ClockIcon className="h-6 w-6 mr-3 text-primary" />
          Campaign Scheduling Settings
        </h3>

        <div className={inputContainerClass}>
          <div className="flex-1">
            <label htmlFor="campaignQueueDays" className={labelClass}>
              Campaign Queue & Birthday Advance Days
            </label>
            <p className={helpTextClass}>
              Number of days before the scheduled date to automatically queue recurring templates as campaigns and show upcoming birthdays on the dashboard. This gives you time to review and modify campaigns before they are sent. (Range: 1–365 days)
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <input
              type="number"
              id="campaignQueueDays"
              min="1"
              max="365"
              value={queueDaysInput}
              onChange={handleQueueDaysChange}
              className={inputClass}
              aria-label="Campaign queue days"
            />
            <span className="text-sm text-textSecondary">days</span>
          </div>
        </div>

        {/* Manual Scheduler Trigger */}
        {currentUser && (
          <div className="mt-6 p-4 border border-border rounded-lg bg-blue-50 dark:bg-blue-900/20">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h4 className="text-sm font-medium text-textPrimary mb-2">Manual Scheduler Trigger</h4>
                <p className="text-xs text-textSecondary mb-3">
                  Manually trigger the template scheduler to create campaigns from recurring templates.
                  This is useful for testing or when you need to force schedule processing outside the normal schedule.
                </p>
              </div>
              <Button
                onClick={triggerScheduler}
                disabled={isSchedulerLoading}
                className="ml-4 bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-md disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
              >
                {isSchedulerLoading ? 'Processing...' : 'Run Scheduler Now'}
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Notification */}
      {notification && (
        <ToastNotification
          type={notification.type}
          message={notification.message}
          onClose={() => setNotification(null)}
        />
      )}
    </div>
  );
};

export default DisplaySettingsPage;