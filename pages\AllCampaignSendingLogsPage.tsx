import React from 'react';
import { useNavigate } from 'react-router-dom';
import Header from '../components/Header';
import CampaignSendingLogs from '../components/CampaignSendingLogs';
import { ArrowLeftIcon } from '../components/icons';

/**
 * All Campaign Sending Logs Page
 * Dedicated page for viewing all campaign sending logs across all campaigns
 */
const AllCampaignSendingLogsPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/campaigns')}
                className="flex items-center text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
              >
                <ArrowLeftIcon className="h-5 w-5 mr-2" />
                Back to Campaigns
              </button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Campaign Sending Logs</h1>
                <p className="text-gray-600 dark:text-gray-300 mt-1">View all campaign sending logs and delivery status</p>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <CampaignSendingLogs
            className="border-0 rounded-lg bg-transparent"
            hideHeader={true}
          />
        </div>
      </div>
    </div>
  );
};

export default AllCampaignSendingLogsPage;
