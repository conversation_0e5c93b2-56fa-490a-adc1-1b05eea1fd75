import { database } from './backend/database/connection.js';

console.log('🔍 CHECKING DATABASE TABLES');
console.log('==========================\n');

async function checkTables() {
  try {
    await database.connect();
    console.log('✅ Connected to database');

    // Get all tables
    const tables = await database.all(`
      SELECT name FROM sqlite_master 
      WHERE type='table' 
      ORDER BY name
    `);

    console.log('\n📋 Available tables:');
    tables.forEach(table => {
      console.log(`  📁 ${table.name}`);
    });

    // Look for subscriber-related tables
    const subscriberTables = tables.filter(t => 
      t.name.toLowerCase().includes('subscriber') || 
      t.name.toLowerCase().includes('user') ||
      t.name.toLowerCase().includes('contact')
    );

    console.log('\n👥 Subscriber-related tables:');
    subscriberTables.forEach(table => {
      console.log(`  👤 ${table.name}`);
    });

    // Check the structure of likely subscriber table
    for (const table of subscriberTables) {
      console.log(`\n📋 Schema for ${table.name}:`);
      try {
        const schema = await database.all(`PRAGMA table_info(${table.name})`);
        schema.forEach(col => {
          console.log(`    ${col.name}: ${col.type}`);
        });
        
        // Show sample data
        const sampleData = await database.all(`SELECT * FROM ${table.name} LIMIT 3`);
        if (sampleData.length > 0) {
          console.log(`\n📊 Sample data from ${table.name}:`);
          sampleData.forEach((row, index) => {
            console.log(`  Record ${index + 1}:`, Object.keys(row).slice(0, 5).map(key => `${key}: ${row[key]}`).join(', '));
          });
        }
      } catch (error) {
        console.log(`    ❌ Error reading ${table.name}:`, error.message);
      }
    }

  } catch (error) {
    console.error('❌ Error:', error);
  }
  
  process.exit(0);
}

checkTables();
