@echo off
echo ========================================
echo CRM SMS Web Gateway
echo ========================================
echo.
echo Opening SMS Gateway Web Interface...
echo.
echo This will open the SMS gateway in your default browser.
echo You can then configure it to connect to your CRM server.
echo.
echo Server URL should be: http://localhost:3001
echo (or http://[YOUR_PC_IP]:3001 if accessing from another device)
echo.
pause

REM Try to open with default browser
start "" "index.html"

echo.
echo SMS Gateway web interface should now be open in your browser.
echo.
echo If it didn't open automatically, you can:
echo 1. Double-click the index.html file
echo 2. Drag index.html into your browser
echo 3. Right-click index.html and select "Open with" your browser
echo.
echo Next steps:
echo 1. Configure the server URL in the web interface
echo 2. Test the connection to your CRM
echo 3. Enable Auto Mode to monitor pending messages
echo.
pause
