// Test subscriber import functionality
const baseUrl = 'http://localhost:3001/api';

async function testSubscriberImport() {
  console.log('🧪 Testing Subscriber Import...\n');

  try {
    // Test data for import
    const testSubscribers = [
      {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User1',
        phone: '+1234567891',
        allowSms: true,
        allowWhatsApp: true,
        status: 'active'
      },
      {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User2',
        phone: '+1234567892',
        allowSms: true,
        allowWhatsApp: false,
        status: 'active'
      }
    ];

    console.log('1. Testing batch subscriber import...');
    const importResponse = await fetch(`${baseUrl}/subscribers/batch`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        subscribers: testSubscribers
      })
    });

    if (importResponse.ok) {
      const result = await importResponse.json();
      console.log('   ✅ Import successful:', result);
      console.log(`   📊 Created: ${result.created}, Errors: ${result.errors}`);
    } else {
      const error = await importResponse.text();
      console.log('   ❌ Import failed:', error);
    }

    console.log('\n2. Testing SMS endpoints with new data...');
    
    // Test pending messages
    const pendingResponse = await fetch(`${baseUrl}/sms/pending`);
    if (pendingResponse.ok) {
      const pending = await pendingResponse.json();
      console.log(`   📱 Pending SMS messages: ${pending.length}`);
      if (pending.length > 0) {
        console.log('   📋 First message:', pending[0]);
      }
    }

    // Test statistics
    const statsResponse = await fetch(`${baseUrl}/sms/stats`);
    if (statsResponse.ok) {
      const stats = await statsResponse.json();
      console.log('   📊 SMS Statistics:', stats.overall);
    }

    console.log('\n🎉 Subscriber import testing complete!');

  } catch (error) {
    console.error('❌ Error testing subscriber import:', error);
  }
}

// Run the test
testSubscriberImport();
