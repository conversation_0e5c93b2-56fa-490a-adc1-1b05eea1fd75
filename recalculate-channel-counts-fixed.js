import { database } from './backend/database/connection.js';

console.log('🔧 RECALCULATING CAMPAIGN CHANNEL COUNTS (FIXED)');
console.log('===============================================\n');

async function recalculateChannelCounts() {
  try {
    await database.connect();
    console.log('✅ Connected to database');

    // Get all campaigns that need count updates
    console.log('\n📋 Checking campaigns with zero channel counts...');
    const campaignsToUpdate = await database.all(`
      SELECT c.id, c.name, c.total_recipients, c.template_id, c.email_recipients_count, 
             c.whatsapp_recipients_count, c.sms_recipients_count,
             t.interest_area_id, t.campaign_type
      FROM campaigns c
      LEFT JOIN campaign_templates t ON c.template_id = t.id
      WHERE c.email_recipients_count = 0 OR c.whatsapp_recipients_count = 0 OR c.sms_recipients_count = 0
      ORDER BY c.created_at DESC
    `);

    console.log(`Found ${campaignsToUpdate.length} campaigns needing count updates`);

    if (campaignsToUpdate.length === 0) {
      console.log('✅ All campaigns already have correct channel counts');
      return;
    }

    // Get all active subscribers for calculations
    console.log('\n📊 Loading subscribers for calculation...');
    const allSubscribers = await database.all(`
      SELECT id, email, phone, allowWhatsApp, allowSms, status, birthDate, name, firstName, lastName
      FROM subscribers 
      WHERE status = 'active'
    `);

    console.log(`Found ${allSubscribers.length} active subscribers`);

    // Get subscriber-area mappings
    const subscriberAreas = await database.all(`
      SELECT subscriber_id, area_of_interest_id 
      FROM subscriber_areas_of_interest
    `);

    console.log(`Found ${subscriberAreas.length} subscriber-area mappings`);

    // Create a map of subscriber ID to their areas
    const subscriberAreasMap = {};
    subscriberAreas.forEach(mapping => {
      if (!subscriberAreasMap[mapping.subscriber_id]) {
        subscriberAreasMap[mapping.subscriber_id] = [];
      }
      subscriberAreasMap[mapping.subscriber_id].push(mapping.area_of_interest_id);
    });

    // Process each campaign
    for (const campaign of campaignsToUpdate) {
      console.log(`\n🔄 Processing: ${campaign.name} (ID: ${campaign.id})`);
      
      let targetSubscribers = [...allSubscribers];

      // Filter by template's interest area if applicable
      if (campaign.interest_area_id) {
        targetSubscribers = allSubscribers.filter(subscriber => {
          const subscriberAreas = subscriberAreasMap[subscriber.id] || [];
          return subscriberAreas.includes(campaign.interest_area_id);
        });
        console.log(`  🎯 Filtered by interest area ${campaign.interest_area_id}: ${targetSubscribers.length} subscribers`);
      }

      // Filter for birthday campaigns
      if (campaign.campaign_type === 'birthday_wish') {
        targetSubscribers = targetSubscribers.filter(subscriber =>
          subscriber.birthDate && subscriber.birthDate.trim() !== ''
        );
        console.log(`  🎂 Filtered by birth date: ${targetSubscribers.length} subscribers`);
      }

      // Calculate channel-specific counts
      const emailRecipients = targetSubscribers.filter(s => 
        s.email && s.email.trim() !== ''
      );

      const whatsappRecipients = targetSubscribers.filter(s => {
        const hasPhone = s.phone && s.phone.trim() !== '';
        const allowsWhatsApp = s.allowWhatsApp === 1 || s.allowWhatsApp === true;
        return hasPhone && allowsWhatsApp;
      });

      const smsRecipients = targetSubscribers.filter(s => {
        const hasPhone = s.phone && s.phone.trim() !== '';
        const allowsSms = s.allowSms === 1 || s.allowSms === true;
        return hasPhone && allowsSms;
      });

      const emailCount = emailRecipients.length;
      const whatsappCount = whatsappRecipients.length;
      const smsCount = smsRecipients.length;

      console.log(`  📧 Email: ${emailCount} (with valid emails)`);
      console.log(`  💬 WhatsApp: ${whatsappCount} (with phone + allowWhatsApp)`);
      console.log(`  📱 SMS: ${smsCount} (with phone + allowSms)`);

      // Calculate total unique recipients
      const uniqueRecipients = new Set([
        ...emailRecipients.map(s => s.id),
        ...whatsappRecipients.map(s => s.id),
        ...smsRecipients.map(s => s.id)
      ]);
      const totalCount = uniqueRecipients.size;

      console.log(`  📊 Total unique recipients: ${totalCount}`);

      // Update the campaign with correct counts
      await database.run(`
        UPDATE campaigns 
        SET 
          email_recipients_count = ?,
          whatsapp_recipients_count = ?,
          sms_recipients_count = ?,
          total_recipients = ?
        WHERE id = ?
      `, [emailCount, whatsappCount, smsCount, totalCount, campaign.id]);

      console.log(`  ✅ Updated campaign counts`);
    }

    console.log('\n📊 Final verification...');
    const updatedCampaigns = await database.all(`
      SELECT id, name, total_recipients, email_recipients_count, whatsapp_recipients_count, sms_recipients_count
      FROM campaigns 
      ORDER BY created_at DESC
      LIMIT 5
    `);

    console.log('\nUpdated campaign counts:');
    updatedCampaigns.forEach(campaign => {
      console.log(`  ${campaign.name}:`);
      console.log(`    📊 Total: ${campaign.total_recipients}`);
      console.log(`    📧 Email: ${campaign.email_recipients_count}`);
      console.log(`    💬 WhatsApp: ${campaign.whatsapp_recipients_count}`);
      console.log(`    📱 SMS: ${campaign.sms_recipients_count}`);
    });

    console.log('\n🎉 Channel count recalculation completed!');
    console.log('💡 All campaigns now have accurate channel counts');
    console.log('💡 Refresh your browser to see the updated counts');

  } catch (error) {
    console.error('❌ Error during recalculation:', error);
  }
  
  process.exit(0);
}

recalculateChannelCounts();
