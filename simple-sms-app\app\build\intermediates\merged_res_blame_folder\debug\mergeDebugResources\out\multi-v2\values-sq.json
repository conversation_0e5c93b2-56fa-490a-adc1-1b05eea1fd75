{"logs": [{"outputFile": "com.crmsms.app-mergeDebugResources-26:/values-sq/values-sq.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\6325cad999e7e25eaa9301d33937688b\\transformed\\core-1.9.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "96", "startColumns": "4", "startOffsets": "8106", "endColumns": "100", "endOffsets": "8202"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3de57968c40646ce2b4050186252d063\\transformed\\material-1.8.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,267,346,424,510,610,702,803,929,1012,1077,1177,1247,1306,1404,1466,1530,1589,1661,1724,1778,1895,1952,2014,2068,2140,2275,2358,2436,2577,2661,2743,2833,2886,2945,3011,3082,3161,3249,3325,3403,3475,3548,3637,3709,3803,3902,3976,4048,4149,4199,4265,4355,4444,4506,4570,4633,4749,4857,4966,5075,5132,5195", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,78,77,85,99,91,100,125,82,64,99,69,58,97,61,63,58,71,62,53,116,56,61,53,71,134,82,77,140,83,81,89,52,58,65,70,78,87,75,77,71,72,88,71,93,98,73,71,100,49,65,89,88,61,63,62,115,107,108,108,56,62,82", "endOffsets": "262,341,419,505,605,697,798,924,1007,1072,1172,1242,1301,1399,1461,1525,1584,1656,1719,1773,1890,1947,2009,2063,2135,2270,2353,2431,2572,2656,2738,2828,2881,2940,3006,3077,3156,3244,3320,3398,3470,3543,3632,3704,3798,3897,3971,4043,4144,4194,4260,4350,4439,4501,4565,4628,4744,4852,4961,5070,5127,5190,5273"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3013,3092,3170,3256,3356,3448,3549,3675,3758,3823,3923,3993,4052,4150,4212,4276,4335,4407,4470,4524,4641,4698,4760,4814,4886,5021,5104,5182,5323,5407,5489,5579,5632,5691,5757,5828,5907,5995,6071,6149,6221,6294,6383,6455,6549,6648,6722,6794,6895,6945,7011,7101,7190,7252,7316,7379,7495,7603,7712,7821,7878,7941", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "endColumns": "12,78,77,85,99,91,100,125,82,64,99,69,58,97,61,63,58,71,62,53,116,56,61,53,71,134,82,77,140,83,81,89,52,58,65,70,78,87,75,77,71,72,88,71,93,98,73,71,100,49,65,89,88,61,63,62,115,107,108,108,56,62,82", "endOffsets": "312,3087,3165,3251,3351,3443,3544,3670,3753,3818,3918,3988,4047,4145,4207,4271,4330,4402,4465,4519,4636,4693,4755,4809,4881,5016,5099,5177,5318,5402,5484,5574,5627,5686,5752,5823,5902,5990,6066,6144,6216,6289,6378,6450,6544,6643,6717,6789,6890,6940,7006,7096,7185,7247,7311,7374,7490,7598,7707,7816,7873,7936,8019"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c49fe9f01bdf72f5546a74fa012334ab\\transformed\\appcompat-1.6.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,431,517,623,746,828,906,997,1090,1185,1279,1380,1473,1568,1665,1756,1849,1930,2036,2140,2238,2344,2448,2550,2704,2801", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "214,314,426,512,618,741,823,901,992,1085,1180,1274,1375,1468,1563,1660,1751,1844,1925,2031,2135,2233,2339,2443,2545,2699,2796,2878"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "317,431,531,643,729,835,958,1040,1118,1209,1302,1397,1491,1592,1685,1780,1877,1968,2061,2142,2248,2352,2450,2556,2660,2762,2916,8024", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "426,526,638,724,830,953,1035,1113,1204,1297,1392,1486,1587,1680,1775,1872,1963,2056,2137,2243,2347,2445,2551,2655,2757,2911,3008,8101"}}]}]}