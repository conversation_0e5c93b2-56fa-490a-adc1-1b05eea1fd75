/**
 * <PERSON><PERSON><PERSON> to fix existing campaign channel counts
 * This script recalculates and updates the channel-specific recipient counts for all existing campaigns
 */

import { campaignService } from '../services/CampaignService-API';
import { subscriberService } from '../services/SubscriberService-API';
import { templateService } from '../services/TemplateService-API';
import { SubscriberProfileStatus, CampaignType } from '../types';

interface ChannelCounts {
  email: number;
  whatsapp: number;
  sms: number;
  total: number;
}

/**
 * Calculate channel-specific recipient counts for a campaign
 */
async function calculateChannelRecipients(campaign: any): Promise<ChannelCounts> {
  console.log(`🔄 Calculating channel counts for campaign: ${campaign.name}`);
  
  try {
    // Get the template for this campaign
    const template = await templateService.getTemplateById(campaign.template_id);
    if (!template) {
      console.log(`❌ Template not found for campaign: ${campaign.name}`);
      return { email: 0, whatsapp: 0, sms: 0, total: 0 };
    }

    // Get all active subscribers
    const allSubscribers = await subscriberService.getAllSubscribers();
    let targetSubscribers = allSubscribers.filter(s => s.status === SubscriberProfileStatus.ACTIVE);

    console.log(`📊 Total active subscribers: ${targetSubscribers.length}`);

    // Filter by template's interest area if applicable
    if (template.interest_area_id) {
      targetSubscribers = targetSubscribers.filter(subscriber => {
        const areasArray = subscriber.areasOfInterestIds || [];
        return Array.isArray(areasArray) && areasArray.includes(template.interest_area_id!);
      });
      console.log(`🎯 Filtered by interest area: ${targetSubscribers.length}`);
    }

    // For birthday campaigns, filter by birth date
    if (template.campaign_type === CampaignType.BIRTHDAY_WISH) {
      targetSubscribers = targetSubscribers.filter(subscriber =>
        subscriber.birthDate && subscriber.birthDate.trim() !== ''
      );
      console.log(`🎂 Filtered by birth date: ${targetSubscribers.length}`);
    }

    // Calculate channel-specific counts
    const emailRecipients = targetSubscribers.filter(s => 
      s.email && s.email.trim() !== ''
    );

    const whatsappRecipients = targetSubscribers.filter(s => 
      s.phone && s.phone.trim() !== '' && s.allowWhatsApp === true
    );

    const smsRecipients = targetSubscribers.filter(s => 
      s.phone && s.phone.trim() !== '' && s.allowSms === true
    );

    // Calculate total unique recipients (avoid double counting)
    const allRecipientIds = new Set([
      ...emailRecipients.map(s => s.id),
      ...whatsappRecipients.map(s => s.id),
      ...smsRecipients.map(s => s.id)
    ]);

    const result = {
      email: emailRecipients.length,
      whatsapp: whatsappRecipients.length,
      sms: smsRecipients.length,
      total: allRecipientIds.size
    };

    console.log(`✅ Channel counts for ${campaign.name}:`, result);
    return result;

  } catch (error) {
    console.error(`❌ Error calculating channel counts for ${campaign.name}:`, error);
    return { email: 0, whatsapp: 0, sms: 0, total: 0 };
  }
}

/**
 * Update a campaign with correct channel counts
 */
async function updateCampaignChannelCounts(campaign: any, channelCounts: ChannelCounts): Promise<boolean> {
  try {
    const updatedCampaign = {
      ...campaign,
      email_recipients_count: channelCounts.email,
      whatsapp_recipients_count: channelCounts.whatsapp,
      sms_recipients_count: channelCounts.sms,
      total_recipients: channelCounts.total
    };

    await campaignService.updateCampaign(campaign.id, updatedCampaign);
    console.log(`✅ Updated campaign: ${campaign.name}`);
    return true;
  } catch (error) {
    console.error(`❌ Failed to update campaign ${campaign.name}:`, error);
    return false;
  }
}

/**
 * Main function to fix all campaigns
 */
export async function fixAllCampaignChannelCounts(): Promise<{
  total: number;
  updated: number;
  failed: number;
  results: Array<{
    name: string;
    success: boolean;
    channelCounts?: ChannelCounts;
    error?: string;
  }>;
}> {
  console.log('🚀 Starting campaign channel counts fix...');
  
  const results: Array<{
    name: string;
    success: boolean;
    channelCounts?: ChannelCounts;
    error?: string;
  }> = [];

  try {
    // Get all campaigns
    const campaigns = await campaignService.getAllCampaigns();
    console.log(`📋 Found ${campaigns.length} campaigns to process`);

    let updated = 0;
    let failed = 0;

    for (const campaign of campaigns) {
      try {
        console.log(`\n🔄 Processing campaign: ${campaign.name}`);
        
        // Calculate correct channel counts
        const channelCounts = await calculateChannelRecipients(campaign);
        
        // Update the campaign
        const success = await updateCampaignChannelCounts(campaign, channelCounts);
        
        if (success) {
          updated++;
          results.push({
            name: campaign.name,
            success: true,
            channelCounts
          });
        } else {
          failed++;
          results.push({
            name: campaign.name,
            success: false,
            error: 'Failed to update campaign'
          });
        }
        
        // Small delay to avoid overwhelming the system
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        failed++;
        console.error(`❌ Error processing campaign ${campaign.name}:`, error);
        results.push({
          name: campaign.name,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    console.log('\n🎉 Campaign channel counts fix completed!');
    console.log(`📊 Total: ${campaigns.length}, Updated: ${updated}, Failed: ${failed}`);

    return {
      total: campaigns.length,
      updated,
      failed,
      results
    };

  } catch (error) {
    console.error('❌ Fatal error during campaign fix:', error);
    throw error;
  }
}

/**
 * Run the fix if this script is executed directly
 */
if (typeof window !== 'undefined') {
  // Browser environment - expose function globally for manual execution
  // Only expose in development mode to avoid production console noise
  const isDev = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
  
  if (isDev) {
    (window as any).fixAllCampaignChannelCounts = fixAllCampaignChannelCounts;
    console.log('🔧 Campaign channel counts fix function available as: fixAllCampaignChannelCounts()');
  }
}
