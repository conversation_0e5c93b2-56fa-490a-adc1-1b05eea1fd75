import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import Header from '../components/Header';
import CampaignSendingLogs from '../components/CampaignSendingLogs';
import { ArrowLeftIcon } from '../components/icons';

/**
 * Campaign Sending Logs Page
 * Dedicated page for viewing campaign sending status and logs for a specific campaign
 */
const CampaignSendingLogsPage: React.FC = () => {
  const { campaignId } = useParams<{ campaignId: string }>();
  const navigate = useNavigate();

  if (!campaignId) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
            <div className="flex items-center">
              <div>
                <h3 className="text-lg font-medium text-red-800 dark:text-red-200">Campaign Not Found</h3>
                <p className="text-red-600 dark:text-red-300 mt-1">No campaign ID provided in the URL.</p>
              </div>
            </div>
            <div className="mt-4">
              <button
                onClick={() => navigate('/campaigns')}
                className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors"
              >
                Back to Campaigns
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/campaigns')}
                className="flex items-center text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
              >
                <ArrowLeftIcon className="h-5 w-5 mr-2" />
                Back to Campaigns
              </button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Campaign Sending Logs</h1>
                <p className="text-gray-600 dark:text-gray-300 mt-1">View sending logs and delivery status for this campaign</p>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <CampaignSendingLogs
            campaignId={campaignId}
            className="border-0 rounded-lg bg-transparent"
            hideHeader={true}
          />
        </div>
      </div>
    </div>
  );
};

export default CampaignSendingLogsPage;
