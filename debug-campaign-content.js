import { database } from './backend/database/connection.js';

console.log('🔍 DEBUGGING CAMPAIGN CONTENT LOADING');
console.log('===================================\n');

async function debugCampaignContent() {
  try {
    await database.connect();
    console.log('✅ Connected to database');

    // Get the most recent campaigns
    console.log('\n📋 Checking recent campaign content in database...');
    const campaigns = await database.all(`
      SELECT id, name, subject, email_content, email_html_content, whatsapp_content, sms_content,
             sender_name, sender_email, sender_phone, scheduled_date,
             email_recipients_count, whatsapp_recipients_count, sms_recipients_count,
             created_at
      FROM campaigns 
      ORDER BY created_at DESC
      LIMIT 5
    `);

    console.log(`Found ${campaigns.length} recent campaigns\n`);

    campaigns.forEach((campaign, index) => {
      console.log(`Campaign ${index + 1}: ${campaign.name} (${campaign.id})`);
      console.log(`  📧 Email content: ${campaign.email_content ? campaign.email_content.length + ' chars' : 'NULL/EMPTY'}`);
      console.log(`  📧 Email HTML: ${campaign.email_html_content ? campaign.email_html_content.length + ' chars' : 'NULL/EMPTY'}`);
      console.log(`  💬 WhatsApp: ${campaign.whatsapp_content ? campaign.whatsapp_content.length + ' chars' : 'NULL/EMPTY'}`);
      console.log(`  📱 SMS: ${campaign.sms_content ? campaign.sms_content.length + ' chars' : 'NULL/EMPTY'}`);
      console.log(`  👤 Sender: ${campaign.sender_name || 'NULL'} <${campaign.sender_email || 'NULL'}>`);
      console.log(`  📅 Scheduled: ${campaign.scheduled_date || 'NULL'}`);
      console.log(`  📊 Recipients: E:${campaign.email_recipients_count} W:${campaign.whatsapp_recipients_count} S:${campaign.sms_recipients_count}`);
      
      // Show content samples (first 100 chars)
      if (campaign.email_content) {
        console.log(`  📧 Email sample: "${campaign.email_content.substring(0, 100)}..."`);
      }
      if (campaign.whatsapp_content) {
        console.log(`  💬 WhatsApp sample: "${campaign.whatsapp_content.substring(0, 100)}..."`);
      }
      if (campaign.sms_content) {
        console.log(`  📱 SMS sample: "${campaign.sms_content.substring(0, 100)}..."`);
      }
      console.log('  ---');
    });

    // Check if there are campaigns with empty content that should have content
    const campaignsWithEmptyContent = campaigns.filter(c => 
      !c.email_content || !c.whatsapp_content || !c.sms_content
    );

    if (campaignsWithEmptyContent.length > 0) {
      console.log('\n⚠️ Campaigns with missing content:');
      campaignsWithEmptyContent.forEach(campaign => {
        console.log(`  ${campaign.name}: Email=${!!campaign.email_content} WhatsApp=${!!campaign.whatsapp_content} SMS=${!!campaign.sms_content}`);
      });
    } else {
      console.log('\n✅ All campaigns have content in all channels');
    }

  } catch (error) {
    console.error('❌ Error during debug:', error);
  }
  
  process.exit(0);
}

debugCampaignContent();
