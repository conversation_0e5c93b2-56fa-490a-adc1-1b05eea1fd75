const Database = require('better-sqlite3');

try {
  const db = new Database('../crm4ca.db');
  
  // Check if database exists and has tables
  const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all();
  console.log('Tables in database:', tables.map(t => t.name));
  
  // Check subscribers table specifically
  if (tables.some(t => t.name === 'subscribers')) {
    const schema = db.prepare('PRAGMA table_info(subscribers)').all();
    console.log('\nSubscribers table schema:');
    schema.forEach(col => {
      console.log(`  ${col.name}: ${col.type} ${col.notnull ? 'NOT NULL' : ''} ${col.dflt_value ? `DEFAULT ${col.dflt_value}` : ''}`);
    });
    
    // Check if remarks column exists
    const hasRemarks = schema.some(col => col.name === 'remarks');
    console.log(`\nRemarks column exists: ${hasRemarks}`);
  } else {
    console.log('\nSubscribers table does not exist!');
  }
  
  db.close();
} catch (error) {
  console.error('Error:', error.message);
}
