{"name": "crm4ca-static-server", "version": "1.0.0", "description": "Static server for CRM4CA with network access", "main": "static-server.js", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "start": "node static-server.js", "build-and-serve": "npm run build && node static-server.js", "setup": "cd backend && npm install && cd .. && npm install && cd whatsapp-progress-app && npm install && cd ..", "setup-electron": "cd whatsapp-progress-app && npm install", "backend": "cd backend && npm start", "frontend": "vite", "electron": "cd whatsapp-progress-app && npm start", "electron-dev": "cd whatsapp-progress-app && npm start", "build-electron": "cd whatsapp-progress-app && npm run build", "build-electron-win": "cd whatsapp-progress-app && npm run build-win", "build-electron-mac": "cd whatsapp-progress-app && npm run build-mac", "build-electron-linux": "cd whatsapp-progress-app && npm run build-linux", "build-all": "npm run build && npm run build-electron", "health": "curl http://localhost:3001/health", "health-electron": "curl http://localhost:3001/api/progress/status"}, "dependencies": {"@nut-tree-fork/nut-js": "^4.2.6", "axios": "^1.9.0", "express": "^4.21.2", "http-proxy-middleware": "^2.0.9", "luxon": "^3.6.1", "node-cron": "^4.1.0", "react-quill": "^2.0.0", "react-router-dom": "^6.26.2", "robotjs": "^0.6.0", "sql.js": "^1.13.0", "sqlite3": "^5.1.7", "xlsx": "^0.18.5"}, "keywords": ["crm", "static-server", "proxy"], "author": "Your Organization", "license": "MIT", "devDependencies": {"@types/axios": "^0.9.36", "@types/react": "^18.2.79", "@types/react-dom": "^18.2.25", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "postcss": "^8.4.38", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.4.1", "typescript": "^5.2.2", "vite": "^5.1.0"}}