/**
 * Professional Database Service for CRM4CA Application
 * Replaces localStorage with SQLite Database Operations
 * Author: Professional Database Migration Tool
 * Date: June 2, 2025
 */

import { 
    User, Campaign, CampaignTemplate, Subscriber, AreaOfInterest, 
    MessageSignature, CampaignSubscriber, PlaceholderManager, 
    EmailSettings, RegistrationDetails, DisplaySettings, 
    AuditLogEntry, AuditActionType, DatabaseUserRow, DatabaseCampaignRow,
    UserRole, CampaignStatus, CampaignType, HolidayHandlingRule
} from '../types';

/**
 * Professional Database Service Class
 * Provides enterprise-grade database operations for CRM application
 */
export class DatabaseService {
    private static instance: DatabaseService;
    private db: any = null;
    private isInitialized: boolean = false;

    private constructor() {
        // Private constructor for singleton pattern
    }

    /**
     * Get singleton instance of DatabaseService
     */
    public static getInstance(): DatabaseService {
        if (!DatabaseService.instance) {
            DatabaseService.instance = new DatabaseService();
        }
        return DatabaseService.instance;
    }

    /**
     * Check if database is initialized
     */
    private ensureInitialized(): void {
        if (!this.isInitialized || !this.db) {
            throw new Error('Database not initialized. Call initialize() first.');
        }
    }

    /**
     * Initialize database connection and create tables
     */
    public async initialize(): Promise<void> {
        try {
            if (this.isInitialized) return;

            if (this.isNodeEnvironment()) {
                try {
                    // Dynamic import for Node.js environment
                    // Completely opaque dynamic import that Vite can't analyze
                    const { default: Database } = await import(`${'better-'}sqlite3`);
                    this.db = new Database('./crm4ca.db', {
                        verbose: console.log
                    });
                } catch (err) {
                    console.error('Failed to initialize database:', err);
                    throw new Error('Database unavailable');
                }
            } else {
                console.warn('Running in browser - using mock database');
                this.db = {
                    prepare: () => ({ 
                        run: () => {}, 
                        all: () => [], 
                        get: () => null 
                    }),
                    exec: () => {},
                    pragma: () => {},
                    close: () => {}
                };
            }

            // Initialize database structure
            this.db.pragma('foreign_keys = ON');
            await this.createTables();
            await this.createIndexes();
            await this.insertDefaults();
            
            this.isInitialized = true;
            console.log('✅ Database initialized successfully');
        } catch (error) {
            console.error('❌ Database initialization failed:', error);
            throw new Error(`Database initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    private isNodeEnvironment(): boolean {
        return typeof process !== 'undefined' && 
               !!process.versions?.node &&
               !process.versions.electron;
    }

    private async createTables(): Promise<void> {
        try {
            if (this.isNodeEnvironment()) {
                const fs = require('fs');
                const path = require('path');
                const schemaPath = path.join(__dirname, 'database_schema.sql');
                const schema = fs.readFileSync(schemaPath, 'utf8');
                this.db.exec(schema);
            }
            console.log('✅ Database tables created successfully');
        } catch (error) {
            console.error('❌ Error creating tables:', error);
            throw error;
        }
    }

    private async createIndexes(): Promise<void> {
        try {
            if (this.isNodeEnvironment()) {
                // Only use require in Node.js environment
                try {
                    const fs = await import('fs');
                    const path = await import('path');
                    const indexPath = path.join(__dirname, 'database_indexes.sql');
                    const indexes = fs.readFileSync(indexPath, 'utf8');
                    this.db.exec(indexes);
                } catch (importError) {
                    console.log('📝 Skipping file-based index creation in browser environment');
                }
            }
            console.log('✅ Database indexes created successfully');
        } catch (error) {
            console.error('❌ Error creating indexes:', error);
            // Don't throw error for browser environment
            if (this.isNodeEnvironment()) {
                throw error;
            }
        }
    }

    private async insertDefaults(): Promise<void> {
        try {
            if (this.isNodeEnvironment()) {
                // Only use require in Node.js environment
                try {
                    const fs = await import('fs');
                    const path = await import('path');
                    const defaultsPath = path.join(__dirname, 'database_defaults.sql');
                    const defaults = fs.readFileSync(defaultsPath, 'utf8');
                    this.db.exec(defaults);
                } catch (importError) {
                    console.log('📝 Skipping file-based defaults insertion in browser environment');
                }
            }
            console.log('✅ Default data inserted successfully');
        } catch (error) {
            console.error('❌ Error inserting defaults:', error);
            // Don't throw error for browser environment
            if (this.isNodeEnvironment()) {
                throw error;
            }
        }
    }

    // ===== USER OPERATIONS =====
    
    /**
     * Get all users
     */
    public async getAllUsers(): Promise<User[]> {
        this.ensureInitialized();
        try {
            const stmt = this.db.prepare(`
                SELECT * FROM users 
                ORDER BY created_at DESC
            `);
            const rows = stmt.all();
            return rows.map(row => this.parseUserRow(row));
        } catch (error) {
            console.error('Error fetching users:', error);
            throw error;
        }
    }

    /**
     * Get user by ID
     */
    public async getUserById(userId: string): Promise<User | null> {
        this.ensureInitialized();
        try {
            const stmt = this.db.prepare(`
                SELECT * FROM users WHERE user_id = ?
            `);
            const row = stmt.get(userId);
            return row ? this.parseUserRow(row) : null;
        } catch (error) {
            console.error('Error fetching user:', error);
            throw error;
        }
    }

    /**
     * Create new user
     */
    public async createUser(userData: Omit<User, 'created_at' | 'updated_at'>): Promise<User> {
        this.ensureInitialized();
        try {
            const now = new Date().toISOString();
            const stmt = this.db.prepare(`
                INSERT INTO users (
                    user_id, name, email, password, role, permissions, 
                    created_at, updated_at, is_active, is_email_verified, 
                    last_login, can_manage_campaigns, can_edit_subscribers, 
                    can_view_analytics, default_sender_name, default_sender_email, 
                    default_sender_phone, table_preferences, is_admin_only, owner_user_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `);
            
            stmt.run(
                userData.user_id, userData.name, userData.email, userData.password,
                userData.role, JSON.stringify(userData.permissions || []), now, now,
                userData.is_active ? 1 : 0, userData.is_email_verified ? 1 : 0,
                userData.last_login, userData.can_manage_campaigns ? 1 : 0,
                userData.can_edit_subscribers ? 1 : 0, userData.can_view_analytics ? 1 : 0,
                userData.default_sender_name, userData.default_sender_email,
                userData.default_sender_phone, JSON.stringify(userData.table_preferences || {}),
                userData.is_admin_only ? 1 : 0, userData.owner_user_id
            );
            
            return this.getUserById(userData.user_id) as Promise<User>;
        } catch (error) {
            console.error('Error creating user:', error);
            throw error;
        }
    }

    /**
     * Update user
     */
    public async updateUser(userId: string, userData: Partial<User>): Promise<User> {
        this.ensureInitialized();
        try {
            const stmt = this.db.prepare(`
                UPDATE users SET 
                    name = COALESCE(?, name),
                    email = COALESCE(?, email),
                    password = COALESCE(?, password),
                    role = COALESCE(?, role),
                    permissions = COALESCE(?, permissions),
                    is_active = COALESCE(?, is_active),
                    is_email_verified = COALESCE(?, is_email_verified),
                    last_login = COALESCE(?, last_login),
                    can_manage_campaigns = COALESCE(?, can_manage_campaigns),
                    can_edit_subscribers = COALESCE(?, can_edit_subscribers),
                    can_view_analytics = COALESCE(?, can_view_analytics),
                    default_sender_name = COALESCE(?, default_sender_name),
                    default_sender_email = COALESCE(?, default_sender_email),
                    default_sender_phone = COALESCE(?, default_sender_phone),
                    table_preferences = COALESCE(?, table_preferences),
                    updated_at = datetime('now')
                WHERE user_id = ?
            `);
            
            stmt.run(
                userData.name, userData.email, userData.password, userData.role,
                userData.permissions ? JSON.stringify(userData.permissions) : null,
                userData.is_active !== undefined ? (userData.is_active ? 1 : 0) : null,
                userData.is_email_verified !== undefined ? (userData.is_email_verified ? 1 : 0) : null,
                userData.last_login,
                userData.can_manage_campaigns !== undefined ? (userData.can_manage_campaigns ? 1 : 0) : null,
                userData.can_edit_subscribers !== undefined ? (userData.can_edit_subscribers ? 1 : 0) : null,
                userData.can_view_analytics !== undefined ? (userData.can_view_analytics ? 1 : 0) : null,
                userData.default_sender_name, userData.default_sender_email, userData.default_sender_phone,
                userData.table_preferences ? JSON.stringify(userData.table_preferences) : null,
                userId
            );
            
            return this.getUserById(userId) as Promise<User>;
        } catch (error) {
            console.error('Error updating user:', error);
            throw error;
        }
    }

    /**
     * Delete user
     */
    public async deleteUser(userId: string): Promise<void> {
        this.ensureInitialized();
        try {
            const stmt = this.db.prepare('DELETE FROM users WHERE user_id = ?');
            stmt.run(userId);
        } catch (error) {
            console.error('Error deleting user:', error);
            throw error;
        }
    }

    /**
     * Get active session token from database
     */
    public async getActiveSessionToken(): Promise<string | null> {
        this.ensureInitialized();
        try {
            const stmt = this.db.prepare(`
                SELECT auth_token 
                FROM sessions
                WHERE user_id = (
                    SELECT user_id FROM user_sessions 
                    ORDER BY last_activity DESC 
                    LIMIT 1
                )
                AND datetime(expires_at) > datetime('now')
                ORDER BY created_at DESC
                LIMIT 1
            `);
            const result = stmt.get();
            return result?.auth_token || null;
        } catch (error) {
            console.error('Error getting active session token:', error);
            return null;
        }
    }

    // ===== CAMPAIGN OPERATIONS =====
    
    /**
     * Get all campaigns
     */
    public async getAllCampaigns(): Promise<Campaign[]> {
        this.ensureInitialized();
        try {
            const stmt = this.db.prepare(`
                SELECT * FROM campaigns 
                ORDER BY created_at DESC
            `);
            const rows = stmt.all();
            return rows.map(row => this.parseCampaignRow(row));
        } catch (error) {
            console.error('Error fetching campaigns:', error);
            throw error;
        }
    }

    /**
     * Helper method to parse User row
     */
    private parseUserRow(row: DatabaseUserRow): User {
        return {
            user_id: row.user_id,
            name: row.name,
            email: row.email,
            password: row.password,
            role: row.role as UserRole,
            permissions: row.permissions ? JSON.parse(row.permissions) : [],
            created_at: row.created_at,
            updated_at: row.updated_at,
            is_active: Boolean(row.is_active),
            is_email_verified: Boolean(row.is_email_verified),
            last_login: row.last_login,
            can_manage_campaigns: Boolean(row.can_manage_campaigns),
            can_edit_subscribers: Boolean(row.can_edit_subscribers),
            can_view_analytics: Boolean(row.can_view_analytics),
            default_sender_name: row.default_sender_name,
            default_sender_email: row.default_sender_email,
            default_sender_phone: row.default_sender_phone,
            table_preferences: row.table_preferences ? JSON.parse(row.table_preferences) : {},
            is_admin_only: Boolean(row.is_admin_only),
            owner_user_id: row.owner_user_id
        };
    }

    /**
     * Helper method to parse Campaign row
     */
    private parseCampaignRow(row: DatabaseCampaignRow): Campaign {
        return {
            id: row.id,
            name: row.name,
            subject: row.subject,
            template_id: row.template_id,
            email_content: row.email_content,
            email_html_content: row.email_html_content,
            whatsapp_content: row.whatsapp_content,
            sms_content: row.sms_content,
            email_signature_id: row.email_signature_id,
            whatsapp_signature_id: row.whatsapp_signature_id,
            sms_signature_id: row.sms_signature_id,
            status: row.status as CampaignStatus,
            campaign_type: row.campaign_type as CampaignType,
            uses_placeholders: Boolean(row.uses_placeholders),
            available_placeholders: row.available_placeholders ? JSON.parse(row.available_placeholders) : [],
            campaign_specific_placeholder_values: row.campaign_specific_placeholder_values ? JSON.parse(row.campaign_specific_placeholder_values) : {},
            created_by: row.created_by,
            created_at: row.created_at,
            updated_at: row.updated_at,
            scheduled_date: row.scheduled_date,
            sent_date: row.sent_date,
            total_recipients: row.total_recipients || 0,
            opened: row.opened || 0,
            clicked: row.clicked || 0,
            bounced: row.bounced || 0,
            birthday_send_offset_days: row.birthday_send_offset_days,
            birthday_send_time: row.birthday_send_time,
            holiday_handling_rule: row.holiday_handling_rule as HolidayHandlingRule,
            triggered_from_template_recurrence_date: row.triggered_from_template_recurrence_date,
            processed_recipients_count: row.processed_recipients_count || 0,
            last_batch_sent_date: row.last_batch_sent_date,
            sent_in_current_hour_count: row.sent_in_current_hour_count || 0,
            current_hour_window_start_date: row.current_hour_window_start_date,
            sent_in_current_day_count: row.sent_in_current_day_count || 0,
            current_day_window_start_date: row.current_day_window_start_date,
            next_batch_eligible_at: row.next_batch_eligible_at,
            is_admin_only: Boolean(row.is_admin_only),
            owner_user_id: row.owner_user_id
        };
    }

    /**
     * Close database connection
     */
    public close(): void {
        if (this.db) {
            this.db.close();
            this.isInitialized = false;
            console.log('Database connection closed');
        }
    }
}

// Export singleton instance
export const databaseService = DatabaseService.getInstance();
