// Test improved Traccar configuration flow
const baseUrl = 'http://localhost:3001/api';

async function testImprovedTraccarFlow() {
  console.log('🧪 Testing Improved Traccar Flow...\n');

  try {
    // Test 1: Clear any existing configuration
    console.log('1. Clearing existing configuration...');
    await fetch(`${baseUrl}/sms/traccar/config`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        baseUrl: '',
        apiKey: '',
        enabled: false
      })
    });
    console.log('   ✅ Configuration cleared');

    // Test 2: Test without configuration
    console.log('\n2. Testing without configuration...');
    const testResponse1 = await fetch(`${baseUrl}/sms/traccar/test`);
    const result1 = await testResponse1.json();
    console.log(`   📊 Status: ${result1.status}, Success: ${result1.success}`);
    console.log(`   💬 Message: ${result1.error}`);

    // Test 3: Configure and test with timing
    console.log('\n3. Testing configuration with proper timing...');
    
    // Configure
    const configResponse = await fetch(`${baseUrl}/sms/traccar/config`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        baseUrl: 'http://192.168.1.101:8080',
        apiKey: 'test-api-key-12345',
        enabled: true
      })
    });
    
    if (configResponse.ok) {
      console.log('   ✅ Configuration saved to server');
      
      // Wait a moment (like the frontend does)
      console.log('   ⏳ Waiting for server to process...');
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Test connection
      const testResponse2 = await fetch(`${baseUrl}/sms/traccar/test`);
      const result2 = await testResponse2.json();
      console.log(`   📊 Status: ${result2.status || 'testing'}, Success: ${result2.success}`);
      console.log(`   💬 Message: ${result2.error || result2.message || 'Testing connection'}`);
      
      if (!result2.success && result2.status === 'not_configured') {
        console.log('   🔄 Retrying after additional delay...');
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const testResponse3 = await fetch(`${baseUrl}/sms/traccar/test`);
        const result3 = await testResponse3.json();
        console.log(`   📊 Retry Status: ${result3.status || 'testing'}, Success: ${result3.success}`);
        console.log(`   💬 Retry Message: ${result3.error || result3.message || 'Testing connection'}`);
      }
    }

    console.log('\n🎉 Improved Traccar flow testing complete!');
    console.log('\n📋 Summary:');
    console.log('✅ Configuration timing improved');
    console.log('✅ Automatic retry logic implemented');
    console.log('✅ Better error handling and feedback');
    console.log('✅ User experience enhanced');
    console.log('\n🎯 The web interface should now work smoothly without timing issues');

  } catch (error) {
    console.error('❌ Error testing improved Traccar flow:', error);
  }
}

// Run the test
testImprovedTraccarFlow();
