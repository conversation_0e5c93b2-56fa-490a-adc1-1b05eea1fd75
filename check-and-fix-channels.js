import { database } from './backend/database/connection.js';

console.log('🔧 CHECKING AND FIXING CAMPAIGN CHANNEL COLUMNS');
console.log('==============================================\n');

async function checkAndFixChannelColumns() {
  try {
    await database.connect();
    console.log('✅ Connected to database');

    // Check current schema
    console.log('\n📋 Checking campaigns table schema...');
    const tableInfo = await database.all('PRAGMA table_info(campaigns)');
    const existingColumns = tableInfo.map(col => col.name);
    
    console.log(`Found ${existingColumns.length} columns in campaigns table`);

    // Check for required channel columns
    const requiredColumns = [
      'email_recipients_count',
      'whatsapp_recipients_count', 
      'sms_recipients_count',
      'email_enabled',
      'whatsapp_enabled',
      'sms_enabled'
    ];

    console.log('\n🔍 Channel Column Analysis:');
    const missingColumns = [];
    
    for (const column of requiredColumns) {
      const exists = existingColumns.includes(column);
      console.log(`${exists ? '✅' : '❌'} ${column}`);
      if (!exists) {
        missingColumns.push(column);
      }
    }

    if (missingColumns.length === 0) {
      console.log('\n🎉 All channel columns already exist!');
      
      // Show some existing campaign data
      const campaigns = await database.all(`
        SELECT id, name, total_recipients, email_recipients_count, whatsapp_recipients_count, sms_recipients_count
        FROM campaigns 
        ORDER BY created_at DESC
        LIMIT 3
      `);
      
      console.log('\n📊 Sample campaign data:');
      campaigns.forEach(campaign => {
        console.log(`  ${campaign.name}:`);
        console.log(`    Total: ${campaign.total_recipients}`);
        console.log(`    Email: ${campaign.email_recipients_count}`);
        console.log(`    WhatsApp: ${campaign.whatsapp_recipients_count}`);
        console.log(`    SMS: ${campaign.sms_recipients_count}`);
      });
      
      return;
    }

    console.log(`\n🔧 Adding ${missingColumns.length} missing columns...`);

    // Add missing columns
    for (const column of missingColumns) {
      try {
        if (column.includes('recipients_count')) {
          await database.run(`ALTER TABLE campaigns ADD COLUMN ${column} INTEGER DEFAULT 0`);
        } else {
          await database.run(`ALTER TABLE campaigns ADD COLUMN ${column} BOOLEAN DEFAULT 1`);
        }
        console.log(`✅ Added column: ${column}`);
      } catch (error) {
        if (error.message.includes('duplicate column name')) {
          console.log(`⚠️ Column ${column} already exists`);
        } else {
          console.log(`❌ Error adding ${column}:`, error.message);
        }
      }
    }

    // Update existing campaigns with default values
    console.log('\n🔄 Updating existing campaigns with default values...');
    
    try {
      const updateResult = await database.run(`
        UPDATE campaigns 
        SET 
          email_recipients_count = COALESCE(email_recipients_count, total_recipients),
          whatsapp_recipients_count = COALESCE(whatsapp_recipients_count, 0),
          sms_recipients_count = COALESCE(sms_recipients_count, 0),
          email_enabled = COALESCE(email_enabled, 1),
          whatsapp_enabled = COALESCE(whatsapp_enabled, 1),
          sms_enabled = COALESCE(sms_enabled, 1)
        WHERE 
          email_recipients_count IS NULL OR
          whatsapp_recipients_count IS NULL OR
          sms_recipients_count IS NULL OR
          email_enabled IS NULL OR
          whatsapp_enabled IS NULL OR
          sms_enabled IS NULL
      `);
      
      console.log(`✅ Updated ${updateResult.changes} existing campaigns`);
    } catch (error) {
      console.log('⚠️ Error updating existing campaigns:', error.message);
    }

    // Verify the final schema
    console.log('\n📋 Final schema verification...');
    const finalTableInfo = await database.all('PRAGMA table_info(campaigns)');
    const channelColumns = finalTableInfo.filter(col => 
      col.name.includes('recipients_count') || 
      (col.name.includes('_enabled') && ['email_enabled', 'whatsapp_enabled', 'sms_enabled'].includes(col.name))
    );
    
    console.log('\nChannel-related columns:');
    channelColumns.forEach(col => {
      console.log(`✅ ${col.name}: ${col.type}`);
    });

    // Test with sample data
    console.log('\n📊 Sample updated campaign data:');
    const updatedCampaigns = await database.all(`
      SELECT id, name, total_recipients, email_recipients_count, whatsapp_recipients_count, sms_recipients_count,
             email_enabled, whatsapp_enabled, sms_enabled
      FROM campaigns 
      ORDER BY created_at DESC
      LIMIT 3
    `);
    
    updatedCampaigns.forEach(campaign => {
      console.log(`  ${campaign.name}:`);
      console.log(`    Total: ${campaign.total_recipients}`);
      console.log(`    Email: ${campaign.email_recipients_count} (enabled: ${campaign.email_enabled})`);
      console.log(`    WhatsApp: ${campaign.whatsapp_recipients_count} (enabled: ${campaign.whatsapp_enabled})`);
      console.log(`    SMS: ${campaign.sms_recipients_count} (enabled: ${campaign.sms_enabled})`);
    });

    console.log('\n🎉 Channel column migration completed successfully!');
    console.log('💡 Backend routes can now save channel-specific data');
    console.log('💡 Frontend channel calculations will work properly');

  } catch (error) {
    console.error('❌ Error during migration:', error);
  }
  
  process.exit(0);
}

checkAndFixChannelColumns();
