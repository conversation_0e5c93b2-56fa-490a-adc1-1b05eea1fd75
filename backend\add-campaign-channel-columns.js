💡 No migration needed.');
      process.exit(0);
    }
    
    console.log(`\n🔧 Adding ${missingColumns.length} missing columns...`);
    
    // Add columns one by one with error handling
    const columnDefinitions = {
      'email_recipients_count': 'INTEGER DEFAULT 0',
      'whatsapp_recipients_count': 'INTEGER DEFAULT 0', 
      'sms_recipients_count': 'INTEGER DEFAULT 0',
      'email_enabled': 'BOOLEAN DEFAULT 1',
      'whatsapp_enabled': 'BOOLEAN DEFAULT 1',
      'sms_enabled': 'BOOLEAN DEFAULT 1'
    };
    
    for (const column of missingColumns) {
      try {
        await database.run(`ALTER TABLE campaigns ADD COLUMN ${column} ${columnDefinitions[column]}`);
        console.log(`✅ Added: ${column}`);
      } catch (error) {
        if (error.message.includes('duplicate column name')) {
          console.log(`⚠️ Column ${column} already exists`);
        } else {
          console.log(`❌ Error adding ${column}:`, error.message);
        }
      }
    }
    
    // Update existing campaigns with default values
    console.log('\n🔄 Updating existing campaigns with default values...');
    
    try {
      const updateResult = await database.run(`
        UPDATE campaigns 
        SET 
          email_recipients_count = COALESCE(email_recipients_count, total_recipients),
          whatsapp_recipients_count = COALESCE(whatsapp_recipients_count, 0),
          sms_recipients_count = COALESCE(sms_recipients_count, 0),
          email_enabled = COALESCE(email_enabled, 1),
          whatsapp_enabled = COALESCE(whatsapp_enabled, 1),
          sms_enabled = COALESCE(sms_enabled, 1)
        WHERE 
          email_recipients_count IS NULL OR
          whatsapp_recipients_count IS NULL OR
          sms_recipients_count IS NULL OR
          email_enabled IS NULL OR
          whatsapp_enabled IS NULL OR
          sms_enabled IS NULL
      `);
      
      console.log(`✅ Updated ${updateResult.changes} existing campaigns`);
    } catch (error) {
      console.log('⚠️ Error updating existing campaigns:', error.message);
    }
    
    // Verify final schema
    console.log('\n📋 Final verification:');
    const finalTableInfo = await database.all('PRAGMA table_info(campaigns)');
    const channelColumns = finalTableInfo.filter(col => 
      col.name.includes('recipients_count') || 
      (col.name.includes('_enabled') && ['email_enabled', 'whatsapp_enabled', 'sms_enabled'].includes(col.name))
    );
    
    console.log('Channel-related columns:');
    channelColumns.forEach(col => {
      console.log(`✅ ${col.name}: ${col.type}`);
    });
    
    console.log('\n🎉 Campaign channel columns migration completed!');
    console.log('💡 Frontend can now send channel-specific recipient counts and toggles.');
    console.log('💡 Backend will save these values to the database.');
    
  } catch (error) {
    console.error('❌ Error during migration:', error);
    process.exit(1);
  }
  
  process.exit(0);
}

addCampaignChannelColumns();
