{"logs": [{"outputFile": "com.crmsms.app-mergeDebugResources-27:/values-ca/values-ca.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\23842c59899d74c6854e6a450173091d\\transformed\\material-1.8.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,274,355,435,523,626,718,819,947,1031,1096,1193,1273,1338,1433,1497,1569,1631,1707,1770,1827,1948,2006,2067,2124,2204,2341,2428,2512,2651,2729,2808,2897,2954,3010,3076,3154,3235,3323,3401,3478,3552,3631,3721,3813,3905,4006,4080,4162,4263,4313,4379,4471,4558,4620,4684,4747,4870,4973,5077,5183,5244,5304", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,80,79,87,102,91,100,127,83,64,96,79,64,94,63,71,61,75,62,56,120,57,60,56,79,136,86,83,138,77,78,88,56,55,65,77,80,87,77,76,73,78,89,91,91,100,73,81,100,49,65,91,86,61,63,62,122,102,103,105,60,59,85", "endOffsets": "269,350,430,518,621,713,814,942,1026,1091,1188,1268,1333,1428,1492,1564,1626,1702,1765,1822,1943,2001,2062,2119,2199,2336,2423,2507,2646,2724,2803,2892,2949,3005,3071,3149,3230,3318,3396,3473,3547,3626,3716,3808,3900,4001,4075,4157,4258,4308,4374,4466,4553,4615,4679,4742,4865,4968,5072,5178,5239,5299,5385"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3049,3130,3210,3298,3401,3493,3594,3722,3806,3871,3968,4048,4113,4208,4272,4344,4406,4482,4545,4602,4723,4781,4842,4899,4979,5116,5203,5287,5426,5504,5583,5672,5729,5785,5851,5929,6010,6098,6176,6253,6327,6406,6496,6588,6680,6781,6855,6937,7038,7088,7154,7246,7333,7395,7459,7522,7645,7748,7852,7958,8019,8079", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "endColumns": "12,80,79,87,102,91,100,127,83,64,96,79,64,94,63,71,61,75,62,56,120,57,60,56,79,136,86,83,138,77,78,88,56,55,65,77,80,87,77,76,73,78,89,91,91,100,73,81,100,49,65,91,86,61,63,62,122,102,103,105,60,59,85", "endOffsets": "319,3125,3205,3293,3396,3488,3589,3717,3801,3866,3963,4043,4108,4203,4267,4339,4401,4477,4540,4597,4718,4776,4837,4894,4974,5111,5198,5282,5421,5499,5578,5667,5724,5780,5846,5924,6005,6093,6171,6248,6322,6401,6491,6583,6675,6776,6850,6932,7033,7083,7149,7241,7328,7390,7454,7517,7640,7743,7847,7953,8014,8074,8160"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\94b9c9510706c6dc301febc3dfcf0a64\\transformed\\appcompat-1.6.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,333,440,523,629,755,839,918,1009,1102,1195,1290,1388,1481,1574,1668,1759,1850,1931,2042,2150,2248,2358,2463,2571,2731,2830", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "223,328,435,518,624,750,834,913,1004,1097,1190,1285,1383,1476,1569,1663,1754,1845,1926,2037,2145,2243,2353,2458,2566,2726,2825,2907"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "324,447,552,659,742,848,974,1058,1137,1228,1321,1414,1509,1607,1700,1793,1887,1978,2069,2150,2261,2369,2467,2577,2682,2790,2950,8165", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "442,547,654,737,843,969,1053,1132,1223,1316,1409,1504,1602,1695,1788,1882,1973,2064,2145,2256,2364,2462,2572,2677,2785,2945,3044,8242"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7da3e27adb8c02368eb46247c342376b\\transformed\\core-1.9.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "96", "startColumns": "4", "startOffsets": "8247", "endColumns": "100", "endOffsets": "8343"}}]}]}