# SMS Configuration Step-by-Step Guide - MyPhoneExplorer

## 🎯 **Complete Setup Process**

Follow these exact steps to configure MyPhoneExplorer SMS integration successfully.

## 📋 **Prerequisites**

### **1. Download & Install Software**
- **PC Software:** Download from https://www.fjsoft.at/en/downloads.php
- **Android App:** Download from https://play.google.com/store/apps/details?id=com.fjsoft.myphoneexplorer.client

### **2. Setup MyPhoneExplorer Connection**
1. **Install MyPhoneExplorer** on your Windows PC
2. **Install MyPhoneExplorer Client** on your Android phone
3. **Connect your phone** to PC via USB or WiFi
4. **Test SMS manually** in MyPhoneExplorer to ensure it works

## 🔧 **CRM Configuration Steps**

### **Step 1: Open SMS Configuration**
1. Navigate to: `http://localhost:5176/#/settings/sms`
2. You should see the SMS Configuration page

### **Step 2: Enable SMS Integration**
1. **Check the "Enable SMS Integration" checkbox** ✅
2. This will show the provider selection dropdown

### **Step 3: Select MyPhoneExplorer Provider**
1. **Select "MyPhoneExplorer"** from the SMS Provider dropdown
2. This will show MyPhoneExplorer-specific configuration fields

### **Step 4: Configure MyPhoneExplorer Settings**
1. **MyPhoneExplorer Path:** Enter the full path to MyPhoneExplorer.exe
   - Default: `C:\Program Files (x86)\MyPhoneExplorer\MyPhoneExplorer.exe`
   - Alternative: `C:\Program Files\MyPhoneExplorer\MyPhoneExplorer.exe`
   - **Verify this path exists on your PC!**

2. **Phone Number:** Enter your Android phone number with country code
   - Example: `+**********` (US)
   - Example: `+************` (India)

3. **Default Country Code:** Enter your country code (optional)
   - Example: `1` (US), `91` (India)

### **Step 5: Save Configuration**
1. **Click "Save Configuration" button** 💾
2. **Wait for confirmation message:** "✅ Configuration saved successfully!"
3. **Check browser console** (F12) for any errors

### **Step 6: Test Connection & Diagnose**
1. **Click "Test Connection & Diagnose" button** 🔍
2. **Review diagnostic results:**
   - ✅ **Success:** All systems operational
   - ❌ **Issues Found:** Follow suggested solutions

### **Step 7: Send Test SMS**
1. **Enter a test phone number** in the Test SMS section
2. **Enter a test message**
3. **Click "Send Test SMS" button** 📱
4. **Check your phone** for the received SMS

## 🔍 **Troubleshooting Common Issues**

### **Issue: "MyPhoneExplorer path not configured"**
**Solution:**
1. Ensure you've **saved the configuration** first
2. Verify the MyPhoneExplorer.exe path is correct
3. Check if the file exists at the specified location

### **Issue: "Phone not connected to MyPhoneExplorer"**
**Solution:**
1. Open MyPhoneExplorer on your PC
2. Connect your Android phone via USB or WiFi
3. Test SMS sending manually in MyPhoneExplorer
4. Ensure phone permissions are granted

### **Issue: "MyPhoneExplorer endpoint not responding correctly"**
**Solution:**
1. Check if CRM backend server is running on port 3001
2. Restart the backend server if needed
3. Verify no firewall is blocking the connection

### **Issue: "Backend connection timeout"**
**Solution:**
1. Ensure backend server is running: `node backend/server.js`
2. Check if port 3001 is available
3. Restart the backend server

## 📊 **Expected Diagnostic Results**

### **Successful Configuration:**
```
✅ Connection test successful! All systems operational.
```

### **Typical Issues (Before Setup):**
```
❌ Issues Found:
• MyPhoneExplorer path not configured
• Phone number not configured  
• Phone not connected to MyPhoneExplorer

💡 Suggested Solutions:
• Enter the full path to MyPhoneExplorer.exe
• Enter the phone number of your Android device
• Open MyPhoneExplorer on PC and connect your phone
```

## 🎯 **Success Checklist**

- [ ] MyPhoneExplorer installed on PC and Android
- [ ] Phone connected to MyPhoneExplorer via USB/WiFi
- [ ] SMS sending tested manually in MyPhoneExplorer
- [ ] CRM SMS Integration enabled ✅
- [ ] MyPhoneExplorer provider selected
- [ ] Correct MyPhoneExplorer.exe path entered
- [ ] Phone number with country code entered
- [ ] Configuration saved successfully 💾
- [ ] Diagnostics show "All systems operational" ✅
- [ ] Test SMS sent and received 📱

## 🚨 **Important Notes**

1. **Save First:** Always save configuration before running diagnostics
2. **Path Verification:** Ensure MyPhoneExplorer.exe path is correct
3. **Phone Connection:** MyPhoneExplorer must be connected to your phone
4. **Manual Test:** Test SMS manually in MyPhoneExplorer first
5. **Backend Running:** Ensure CRM backend server is running

## 🎉 **Success!**

Once all steps are completed successfully, you can:
- Send SMS from CRM campaigns
- Use bulk SMS functionality
- Monitor SMS sending status
- Access comprehensive SMS logs

Your MyPhoneExplorer SMS integration is now fully operational! 🎉
