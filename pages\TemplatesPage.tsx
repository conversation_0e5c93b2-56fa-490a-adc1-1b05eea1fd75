import React, { useState, useMemo, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Header from '../components/Header';
import Table, { Column } from '../components/Table';
import ColumnSelector from '../components/ColumnSelector';
import { CampaignTemplate, TemplateStatus, CampaignType, AreaOfInterest, RecurrenceType, AuditActionType, UserRole, UserTablePreferences } from '../types';
import { PlusIcon, EditIcon, DeleteIcon } from '../components/icons';
import { addAuditLog } from '../utils/auditUtilsDB';
import { useAuth } from '../contexts/AuthContextDB';
import { canUserViewItem, canUserEditDeleteItem, AccessibleItem } from '../utils/accessControlUtils';
import ConfirmationModal from '../components/ConfirmationModal';
import { templateService } from '../services/TemplateService-API';
import { areaOfInterestService } from '../services/AreaOfInterestService-API';
import { subscriberService } from '../services/SubscriberService-API';
import { browserDatabaseService } from '../services/BrowserDatabaseService';
import { SubscriberProfileStatus, SubscriberProfile } from '../types';

const initialTemplatesSeed: CampaignTemplate[] = [
  {
    id: 'tmpl1', template_name: 'monthly_newsletter_v1', display_name: 'Monthly Company Newsletter', description: 'Template for the general monthly newsletter.',
    campaign_type: CampaignType.NEWSLETTER, status: TemplateStatus.ACTIVE,
    subject_template: 'Compliance for the {{period}}', 
    email_content_template: 'Hello {{ subscriber_name }},\n\nHere is your monthly update for {{period}}...', email_html_template: '<p>Hello {{ subscriber_name }},</p><p>Here is your monthly update for {{period}}...</p>',
    whatsapp_content_template: 'Hi {{ subscriber_name }}! Check out our latest news for {{period}}: {{ news_link }}',
    email_signature_id: 'sig1', requires_approval: false, sender_name: 'CRM4CA Team', sender_email: '<EMAIL>',
    merge_fields: { subscriber_name: 'Subscriber Name', news_link: 'Link to News Article', period: 'Current Period Name' }, target_segments: ['all_subscribers', 'active_users'],
    interest_area_id: 'aoi1',
    is_active: true, is_public: true, version: 2, uses_placeholders: true, available_placeholders: ['subscriber_name', 'unsubscribe_link', 'period'],
    created_by: 'user1', created_at: '2023-04-01T00:00:00Z', updated_at: '2023-05-01T00:00:00Z',
    is_recurring: false, is_admin_only: false, owner_user_id: undefined,
  },
  {
    id: 'tmpl2', template_name: 'tax_advisory_q3', display_name: 'Q3 Tax Advisory Update', description: 'Important tax updates for the third quarter.',
    campaign_type: CampaignType.TAX_ADVISORY, status: TemplateStatus.ACTIVE,
    subject_template: 'Important Q3 Tax Information',
    email_content_template: 'Dear {{ client_name }},\n\nPlease find attached your Q3 tax advisory...',
    sms_content_template: 'CRM4CA Alert: New tax advisory for Q3 available. Check your email.',
    email_signature_id: 'sig1', requires_approval: true, sender_name: 'Tax Department', sender_email: '<EMAIL>',
    merge_fields: { client_name: 'Client Full Name' }, target_segments: ['clients_finance', 'business_owners'],
    interest_area_id: 'aoi2',
    is_active: true, is_public: false, version: 1, uses_placeholders: true, available_placeholders: ['client_name', 'tax_advisor_name'],
    created_by: 'user2', created_at: '2023-06-15T00:00:00Z', updated_at: '2023-06-20T00:00:00Z',
    is_recurring: true, recurrence_type: RecurrenceType.MONTHLY, recurrence_days_of_month: [10], recurrence_months_of_year: [1,4,7,10],
    is_admin_only: true, owner_user_id: undefined, // Example: Admin Only
  },
  {
    id: 'tmpl3', template_name: 'old_promo_archived', display_name: 'Old Promo (Archived)', description: 'An old promotion, kept for reference.',
    campaign_type: CampaignType.PROMOTION, status: TemplateStatus.ARCHIVED, 
    subject_template: 'Old Promo',
    email_content_template: 'This was an old promo.',
    is_active: false, 
    created_by: 'user1', created_at: '2022-01-01T00:00:00Z', updated_at: '2022-02-01T00:00:00Z',
    sender_name: 'Archive Dept', sender_email: '<EMAIL>', merge_fields: {}, target_segments: [], version: 1, uses_placeholders: false, available_placeholders: [],
    requires_approval: false, 
    is_public: false, 
    is_recurring: false, 
  },
  {
    id: 'tmpl_bday_default', 
    template_name: 'default_birthday_greeting_v1', 
    display_name: 'Default Birthday Greeting', 
    description: 'A friendly, general-purpose birthday wish template.',
    campaign_type: CampaignType.BIRTHDAY_WISH, 
    status: TemplateStatus.ACTIVE,
    subject_template: 'Happy Birthday, {{subscriber_firstName}}!',
    email_content_template: "Hi {{subscriber_firstName}},\n\nWishing you a very Happy Birthday and a wonderful year ahead!\n\nBest,\n{{sender_name}}",
    email_html_template: "<p>Hi {{subscriber_firstName}},</p><p>Wishing you a very <strong>Happy Birthday</strong> and a wonderful year ahead!</p><p>Best,<br>{{sender_name}}</p>",
    whatsapp_content_template: "Happy Birthday, {{subscriber_firstName}}! Hope you have a great day! From {{sender_name}}.",
    sms_content_template: "Happy Birthday, {{subscriber_firstName}}! - {{sender_name}}",
    sender_name: 'Our Team', 
    sender_email: '<EMAIL>', 
    merge_fields: { "subscriber_firstName": "Subscriber's First Name", "sender_name": "Sender's Name" },
    target_segments: [],
    uses_placeholders: true, 
    available_placeholders: ['subscriber_firstName', 'subscriber_lastName', 'sender_name'],
    created_by: 'system', 
    created_at: '2023-01-01T00:00:00Z', 
    updated_at: '2023-01-01T00:00:00Z',
    is_active: true, 
    is_public: true, 
    version: 1,
    requires_approval: false,
    is_recurring: false,
  }
];

const initialAreasOfInterestSeed: AreaOfInterest[] = [ 
  { id: 'aoi2', name: 'Tax Advisory', description: 'Information related to tax laws and advice.', created_at: '2023-01-01T00:00:00Z', updated_at: '2023-01-01T00:00:00Z' },
  { id: 'aoi1', name: 'General Updates', description: 'General company news and updates.', created_at: '2023-01-01T00:00:00Z', updated_at: '2023-01-01T00:00:00Z' },
];

const TEMPLATES_TABLE_KEY = 'templatesListTable';

const TemplatesPage: React.FC = () => {
  const navigate = useNavigate(); 
  const { currentUser } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [templates, setTemplates] = useState<CampaignTemplate[]>([]);
  const [areasOfInterest, setAreasOfInterest] = useState<AreaOfInterest[]>([]);
  const [subscriberCounts, setSubscriberCounts] = useState<Record<string, number>>({});
  const [feedbackMessage, setFeedbackMessage] = useState<string | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false); // Modal state
  const [templateToDelete, setTemplateToDelete] = useState<CampaignTemplate | null>(null); // Item to delete
  const [showSubscriberModal, setShowSubscriberModal] = useState(false);
  const [selectedTemplateForSubscribers, setSelectedTemplateForSubscribers] = useState<CampaignTemplate | null>(null);
  const [allSubscribers, setAllSubscribers] = useState<SubscriberProfile[]>([]);
  const [templateSubscribers, setTemplateSubscribers] = useState<SubscriberProfile[]>([]);
  const [availableSubscribers, setAvailableSubscribers] = useState<SubscriberProfile[]>([]);

  // Single common filtering state for subscriber modal
  const [searchFilter, setSearchFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  
  // Column visibility state
  const defaultVisibleColumnIds = ['display_name', 'template_name', 'campaign_type', 'area_of_interest', 'subscribers', 'status', 'actions'];
  const [visibleColumns, setVisibleColumns] = useState<string[]>(defaultVisibleColumnIds);

  // Function to calculate subscriber count for a template
  const calculateSubscriberCount = async (template: CampaignTemplate): Promise<number> => {
    try {
      const allSubscribers = await subscriberService.getAllSubscribers();
      const activeSubscribers = allSubscribers.filter(s => s.status === SubscriberProfileStatus.ACTIVE);

      // If template has specific interest area, filter by that
      if (template.interest_area_id) {
        const targetSubscribers = activeSubscribers.filter(subscriber => {
          const areasArray = subscriber.areasOfInterestIds || [];
          if (!Array.isArray(areasArray)) {
            return false;
          }
          return areasArray.includes(template.interest_area_id!);
        });
        return targetSubscribers.length;
      }

      // For birthday campaigns, count subscribers with birth dates
      if (template.campaign_type === CampaignType.BIRTHDAY_WISH) {
        const birthdaySubscribers = activeSubscribers.filter(subscriber =>
          subscriber.birthDate && subscriber.birthDate.trim() !== ''
        );
        return birthdaySubscribers.length;
      }

      // For other campaigns without specific segments, return all active subscribers
      return activeSubscribers.length;
    } catch (error) {
      console.error('Error calculating subscriber count for template:', template.id, error);
      return 0;
    }
  };

  useEffect(() => {
    const loadData = async () => {
      try {
        // Load templates from API
        const allTemplates = await templateService.getAllTemplates();
        const visibleTemplates = allTemplates.filter(t => canUserViewItem(t as AccessibleItem, currentUser));
        setTemplates(visibleTemplates);

        // Load areas of interest from API
        const allAreas = await areaOfInterestService.getAllAreasOfInterest();
        setAreasOfInterest(allAreas.filter(aoi => canUserViewItem(aoi as AccessibleItem, currentUser)));

        // Calculate subscriber counts for all templates
        const counts: Record<string, number> = {};
        for (const template of visibleTemplates) {
          counts[template.id] = await calculateSubscriberCount(template);
        }
        setSubscriberCounts(counts);

        // Load saved column preferences if user is logged in
        if (currentUser?.user_id) {
          const sql = `
            SELECT table_preferences
            FROM users
            WHERE user_id = ?
          `;
          const result = await browserDatabaseService.query(sql, [currentUser.user_id]);
          if (result.length > 0 && result[0].table_preferences) {
            const preferences = JSON.parse(result[0].table_preferences);
            if (preferences[TEMPLATES_TABLE_KEY]) {
              setVisibleColumns(preferences[TEMPLATES_TABLE_KEY]);
              console.log('✅ Loaded saved column preferences:', preferences[TEMPLATES_TABLE_KEY]);
            }
          }
        }
      } catch (error) {
        console.error('Error loading templates data:', error);
        showFeedback('Failed to load templates data. Please refresh the page.');
      }
    };

    loadData();
  }, [currentUser]);

  const showFeedback = (message: string) => {
    setFeedbackMessage(message);
    setTimeout(() => {
      setFeedbackMessage(null);
    }, 3000);
  };

  const handleColumnVisibilityChange = async (newVisibleColumnIds: string[]) => {
    console.log('Saving new columns:', newVisibleColumnIds);
    setVisibleColumns(newVisibleColumnIds);
    
    if (currentUser?.user_id) {
      try {
        const sql = `
          UPDATE users 
          SET table_preferences = json_set(
            COALESCE(table_preferences, '{}'),
            '$."${TEMPLATES_TABLE_KEY}"',
            json(?)
          )
          WHERE user_id = ?
        `;
        await browserDatabaseService.query(sql, [JSON.stringify(newVisibleColumnIds), currentUser.user_id]);
        console.log(`✅ Saved column preferences for table ${TEMPLATES_TABLE_KEY}`);
      } catch (error) {
        console.error(`❌ Failed to save column preferences for table ${TEMPLATES_TABLE_KEY}:`, error);
      }
    }
  };

  const handleAddTemplate = () => {
    if (currentUser?.role === UserRole.VIEWER) {
        showFeedback("Access Denied: Viewers cannot add new templates.");
        return;
    }
    navigate('/templates/add');
  };

  const handleEditTemplate = (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
     if (template && !canUserEditDeleteItem(template as AccessibleItem, currentUser)) {
        showFeedback("Access Denied: You do not have permission to edit this template.");
        return;
    }
    navigate(`/templates/edit/${templateId}`);
  };

  const confirmDeleteTemplate = async () => {
    if (!templateToDelete) return;
    
    try {
      await templateService.deleteTemplate(templateToDelete.id);
      setTemplates(prevTemplates => prevTemplates.filter(t => t.id !== templateToDelete.id));
      addAuditLog(
        AuditActionType.DELETE, 
        'Template', 
        `Deleted template "${templateToDelete.display_name}"`,
        {
          entityId: templateToDelete.id,
          userId: currentUser?.user_id,
          metadata: {
            templateName: templateToDelete.display_name
          }
        }
      );
      showFeedback(`Template "${templateToDelete.display_name}" deleted successfully.`);
    } catch (error) {
      console.error('Error deleting template:', error);
      showFeedback('Failed to delete template. Please try again.');
    } finally {
      setShowDeleteModal(false);
      setTemplateToDelete(null);
    }
  };

  const handleDeleteTemplate = (template: CampaignTemplate) => {
    if (!canUserEditDeleteItem(template as AccessibleItem, currentUser)) {
        showFeedback("Access Denied: You do not have permission to delete this template.");
        return;
    }
    setTemplateToDelete(template);
    setShowDeleteModal(true);
  };

  const handleManageSubscribers = async (template: CampaignTemplate) => {
    setSelectedTemplateForSubscribers(template);

    try {
      // Load all subscribers
      const subscribers = await subscriberService.getAllSubscribers();
      const activeSubscribers = subscribers.filter(s => s.status === SubscriberProfileStatus.ACTIVE);
      setAllSubscribers(activeSubscribers);

      // Filter subscribers based on template's interest area
      if (template.interest_area_id) {
        const currentTemplateSubscribers = activeSubscribers.filter(subscriber => {
          const areasArray = subscriber.areasOfInterestIds || [];
          return Array.isArray(areasArray) && areasArray.includes(template.interest_area_id!);
        });

        const availableToAdd = activeSubscribers.filter(subscriber => {
          const areasArray = subscriber.areasOfInterestIds || [];
          return !Array.isArray(areasArray) || !areasArray.includes(template.interest_area_id!);
        });

        setTemplateSubscribers(currentTemplateSubscribers);
        setAvailableSubscribers(availableToAdd);
      } else {
        // For templates without specific interest area, all subscribers are "available"
        setTemplateSubscribers([]);
        setAvailableSubscribers(activeSubscribers);
      }

      // Reset filters when opening modal
      setSearchFilter('');
      setStatusFilter('all');

      setShowSubscriberModal(true);
    } catch (error) {
      console.error('Error loading subscribers for template management:', error);
      showFeedback('Failed to load subscribers. Please try again.');
    }
  };

  // Filter functions for subscriber lists
  const filterSubscribers = (subscribers: SubscriberProfile[], searchFilter: string, statusFilter: string) => {
    return subscribers.filter(subscriber => {
      // Search filter
      const matchesSearch = !searchFilter.trim() ||
        subscriber.email.toLowerCase().includes(searchFilter.toLowerCase()) ||
        `${subscriber.firstName} ${subscriber.lastName}`.toLowerCase().includes(searchFilter.toLowerCase()) ||
        subscriber.organization?.toLowerCase().includes(searchFilter.toLowerCase());

      // Status filter
      const matchesStatus = statusFilter === 'all' || subscriber.status === statusFilter;

      return matchesSearch && matchesStatus;
    });
  };

  const filteredCurrentSubscribers = useMemo(() =>
    filterSubscribers(templateSubscribers, searchFilter, statusFilter),
    [templateSubscribers, searchFilter, statusFilter]
  );

  const filteredAvailableSubscribers = useMemo(() =>
    filterSubscribers(availableSubscribers, searchFilter, statusFilter),
    [availableSubscribers, searchFilter, statusFilter]
  );

  const handleAddSubscriberToTemplate = async (subscriber: SubscriberProfile) => {
    if (!selectedTemplateForSubscribers?.interest_area_id) {
      showFeedback('Template must have an area of interest to manage subscribers.');
      return;
    }

    try {
      // Add the template's interest area to the subscriber's areas
      const currentAreas = subscriber.areasOfInterestIds || [];
      const updatedAreas = [...currentAreas, selectedTemplateForSubscribers.interest_area_id];

      const updatedSubscriber = {
        ...subscriber,
        areasOfInterestIds: updatedAreas
      };

      await subscriberService.updateSubscriber(subscriber.id, updatedSubscriber);

      // Update local state
      setTemplateSubscribers(prev => [...prev, subscriber]);
      setAvailableSubscribers(prev => prev.filter(s => s.id !== subscriber.id));

      // Recalculate subscriber count for this template
      const newCount = templateSubscribers.length + 1;
      setSubscriberCounts(prev => ({
        ...prev,
        [selectedTemplateForSubscribers.id]: newCount
      }));

      showFeedback(`Added ${subscriber.email} to ${selectedTemplateForSubscribers.display_name}`);
    } catch (error) {
      console.error('Error adding subscriber to template:', error);
      showFeedback('Failed to add subscriber. Please try again.');
    }
  };

  const handleRemoveSubscriberFromTemplate = async (subscriber: SubscriberProfile) => {
    if (!selectedTemplateForSubscribers?.interest_area_id) {
      showFeedback('Template must have an area of interest to manage subscribers.');
      return;
    }

    try {
      // Remove the template's interest area from the subscriber's areas
      const currentAreas = subscriber.areasOfInterestIds || [];
      const updatedAreas = currentAreas.filter(areaId => areaId !== selectedTemplateForSubscribers.interest_area_id);

      const updatedSubscriber = {
        ...subscriber,
        areasOfInterestIds: updatedAreas
      };

      await subscriberService.updateSubscriber(subscriber.id, updatedSubscriber);

      // Update local state
      setTemplateSubscribers(prev => prev.filter(s => s.id !== subscriber.id));
      setAvailableSubscribers(prev => [...prev, subscriber]);

      // Recalculate subscriber count for this template
      const newCount = templateSubscribers.length - 1;
      setSubscriberCounts(prev => ({
        ...prev,
        [selectedTemplateForSubscribers.id]: newCount
      }));

      showFeedback(`Removed ${subscriber.email} from ${selectedTemplateForSubscribers.display_name}`);
    } catch (error) {
      console.error('Error removing subscriber from template:', error);
      showFeedback('Failed to remove subscriber. Please try again.');
    }
  };

  const getAreaOfInterestName = (aoiId?: string): string => {
    if (!aoiId) return 'N/A';
    const area = areasOfInterest.find(aoi => aoi.id === aoiId);
    return area ? area.name : 'Unknown';
  };

  const formatRecurrenceInfo = (template: CampaignTemplate): string => {
    if (!template.is_recurring) {
      return 'Non-recurring';
    }
    switch (template.recurrence_type) {
      case RecurrenceType.MONTHLY:
        let monthlyInfo = `Monthly: Day(s) ${template.recurrence_days_of_month?.join(', ') || 'N/A'}`;
        if (template.recurrence_months_of_year && template.recurrence_months_of_year.length > 0) {
          const monthNames = template.recurrence_months_of_year.map(m => 
            new Date(0, m - 1).toLocaleString('default', { month: 'short' })
          );
          monthlyInfo += ` (Months: ${monthNames.join(', ')})`;
        } else {
          monthlyInfo += ' (Every Month)';
        }
        return monthlyInfo;
      case RecurrenceType.SPECIFIC_DATES:
        return `Specific Dates: ${template.recurrence_specific_dates?.join(', ') || 'N/A'}`;
      default:
        return 'Recurring (Unknown Type)';
    }
  };

  const filteredTemplates = useMemo(() => {
    // Start with already permission-filtered templates
    if (!searchTerm.trim()) {
      return templates;
    }
    const lowercasedFilter = searchTerm.toLowerCase();
    return templates.filter(template => {
      const areaOfInterestName = getAreaOfInterestName(template.interest_area_id).toLowerCase();
      const recurrenceInfo = formatRecurrenceInfo(template).toLowerCase();
      return (
        template.display_name.toLowerCase().includes(lowercasedFilter) ||
        template.template_name.toLowerCase().includes(lowercasedFilter) ||
        template.campaign_type.toLowerCase().includes(lowercasedFilter) ||
        template.status.toLowerCase().includes(lowercasedFilter) ||
        areaOfInterestName.includes(lowercasedFilter) ||
        recurrenceInfo.includes(lowercasedFilter)
      );
    });
  }, [templates, searchTerm, areasOfInterest]);
  
  const allTableColumns: Column<CampaignTemplate>[] = [
    { 
      id: 'display_name', 
      header: 'Display Name', 
      accessor: 'display_name', 
      className: 'font-medium text-primary',
      sortable: true,
      sortValue: item => item.display_name.toLowerCase()
    },
    { 
      id: 'template_name',
      header: 'Template Name', 
      accessor: 'template_name', 
      sortable: true,
      sortValue: item => item.template_name.toLowerCase()
    },
    { 
      id: 'campaign_type',
      header: 'Type', 
      accessor: 'campaign_type',
      sortable: true,
      sortValue: item => item.campaign_type
    },
    {
      id: 'area_of_interest',
      header: 'Area of Interest',
      accessor: (item) => getAreaOfInterestName(item.interest_area_id),
      sortable: true,
      sortValue: item => getAreaOfInterestName(item.interest_area_id).toLowerCase()
    },
    {
      id: 'subscribers',
      header: 'Subscribers',
      accessor: (item) => subscriberCounts[item.id] ?? 'Loading...',
      render: (item) => (
        <button
          type="button"
          onClick={() => handleManageSubscribers(item)}
          className="text-center font-medium text-blue-600 hover:text-blue-800 hover:underline cursor-pointer"
          title={`Manage subscribers for ${item.display_name}`}
        >
          {subscriberCounts[item.id] !== undefined ? subscriberCounts[item.id] : 'Loading...'}
        </button>
      ),
      sortable: true,
      sortValue: item => subscriberCounts[item.id] ?? 0
    },
    {
      id: 'status',
      header: 'Status',
      accessor: 'status',
      render: (item) => (
        <span className={`px-2 py-1 text-xs font-semibold leading-tight rounded-full whitespace-nowrap
          ${item.status === TemplateStatus.ACTIVE ? 'bg-green-100 text-green-700 dark:bg-green-700 dark:text-green-100' :
            item.status === TemplateStatus.ARCHIVED ? 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-200' :
            'bg-yellow-100 text-yellow-700 dark:bg-yellow-700 dark:text-yellow-100'}`}>
          {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
        </span>
      ),
      sortable: true,
      sortValue: item => item.status
    },
    { 
      id: 'recurrence',
      header: 'Recurrence', 
      accessor: (item) => formatRecurrenceInfo(item), 
      className: 'text-xs',
      sortable: true,
      sortValue: item => formatRecurrenceInfo(item).toLowerCase()
    },
    { 
      id: 'is_active',
      header: 'In Use (Active Flag)', 
      accessor: 'is_active',
      sortable: true
    },
    { 
      id: 'created_at',
      header: 'Created At', 
      accessor: 'created_at', 
      render: (item) => new Date(item.created_at).toLocaleDateString(),
      sortable: true,
      sortValue: item => new Date(item.created_at).getTime()
    },
    {
      id: 'actions',
      header: 'Actions',
      accessor: 'id',
      render: (template) => (
        <div className="flex space-x-2">
          <button 
            onClick={() => handleEditTemplate(template.id)} 
            disabled={!canUserEditDeleteItem(template as AccessibleItem, currentUser)}
            className={`text-primary hover:text-opacity-80 p-1 ${!canUserEditDeleteItem(template as AccessibleItem, currentUser) ? 'opacity-50 cursor-not-allowed' : ''}`}
            title={canUserEditDeleteItem(template as AccessibleItem, currentUser) ? `Edit ${template.display_name}` : "Permission Denied"}
            aria-label={`Edit ${template.display_name}`}
          >
            <EditIcon className="h-5 w-5" />
          </button>
          <button 
            onClick={() => handleDeleteTemplate(template)} 
            disabled={!canUserEditDeleteItem(template as AccessibleItem, currentUser)}
            className={`text-red-500 hover:text-red-700 p-1 ${!canUserEditDeleteItem(template as AccessibleItem, currentUser) ? 'opacity-50 cursor-not-allowed' : ''}`}
            title={canUserEditDeleteItem(template as AccessibleItem, currentUser) ? `Delete ${template.display_name}` : "Permission Denied"}
            aria-label={`Delete ${template.display_name}`}
          >
            <DeleteIcon className="h-5 w-5" />
          </button>
        </div>
      ),
    },
  ];

  return (
    <div className="text-textPrimary">
      <Header title="Campaign Templates" subtitle="Create and manage reusable campaign templates." />
      {feedbackMessage && (
        <div className="mb-4 p-3 bg-green-100 text-green-700 border border-green-300 rounded-md shadow-sm text-sm dark:bg-green-700 dark:text-green-100 dark:border-green-500" role="alert">
          {feedbackMessage}
        </div>
      )}
      <div className="mb-6 flex flex-col sm:flex-row justify-between items-center gap-4">
        <input
          type="text"
          placeholder="Search templates (name, type, status, area, recurrence)..."
          className="w-full sm:w-auto px-4 py-2 border border-border bg-surface rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          aria-label="Search campaign templates"
        />
        <div className="flex items-center gap-2 flex-wrap">
          <ColumnSelector
            allColumns={allTableColumns}
            visibleColumnIds={visibleColumns}
            onSave={handleColumnVisibilityChange}
            defaultVisibleColumnIds={defaultVisibleColumnIds}
            tableKey={TEMPLATES_TABLE_KEY}
            userId={currentUser?.user_id}
          />
          <button
            onClick={handleAddTemplate}
            disabled={currentUser?.role === UserRole.VIEWER}
            className={`bg-primary hover:bg-opacity-80 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transition duration-150 ease-in-out flex items-center justify-center ${currentUser?.role === UserRole.VIEWER ? 'opacity-50 cursor-not-allowed' : ''}`}
            aria-label="Add new campaign template"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            Add New Template
          </button>
        </div>
      </div>
      <Table<CampaignTemplate> 
        allColumns={allTableColumns} 
        visibleColumnIds={visibleColumns}
        data={filteredTemplates} 
        caption="List of Campaign Templates" 
        rowKey="id"
        onRowDoubleClick={(template) => handleEditTemplate(template.id)}
        userId={currentUser?.user_id}
      />
      {showDeleteModal && templateToDelete && (
        <ConfirmationModal
          isOpen={showDeleteModal}
          title="Confirm Deletion"
          message={<>Are you sure you want to delete template: <strong>{templateToDelete.display_name}</strong>?</>}
          onConfirm={confirmDeleteTemplate}
          onCancel={() => { setShowDeleteModal(false); setTemplateToDelete(null); }}
          confirmText="Delete"
        />
      )}

      {showSubscriberModal && selectedTemplateForSubscribers && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <h3 className="text-lg font-semibold mb-4">
              Manage Subscribers for "{selectedTemplateForSubscribers.display_name}"
            </h3>

            {selectedTemplateForSubscribers.interest_area_id ? (
              <>
                {/* Common Filters */}
                <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Filter Subscribers</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <input
                      type="text"
                      placeholder="Search by name, email, or organization..."
                      value={searchFilter}
                      onChange={(e) => setSearchFilter(e.target.value)}
                      className="px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <select
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                      className="px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      aria-label="Filter subscribers by status"
                    >
                      <option value="all">All Statuses</option>
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                      <option value="unsubscribed">Unsubscribed</option>
                    </select>
                  </div>
                  <p className="text-xs text-gray-600 mt-2">
                    This filter applies to both Current and Available subscriber lists below.
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Current Subscribers */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">
                    Current Subscribers ({filteredCurrentSubscribers.length} of {templateSubscribers.length})
                  </h4>

                  <div className="border rounded-lg max-h-60 overflow-y-auto">
                    {filteredCurrentSubscribers.length > 0 ? (
                      filteredCurrentSubscribers.map(subscriber => (
                        <div key={subscriber.id} className="flex items-center justify-between p-3 border-b last:border-b-0">
                          <div className="flex-1 min-w-0">
                            <div className="font-medium text-sm truncate">{subscriber.email}</div>
                            <div className="text-xs text-gray-500 truncate">
                              {subscriber.firstName} {subscriber.lastName}
                              {subscriber.organization && ` • ${subscriber.organization}`}
                            </div>
                            <div className="text-xs">
                              <span className={`px-2 py-1 rounded-full text-xs ${
                                subscriber.status === 'active' ? 'bg-green-100 text-green-800' :
                                subscriber.status === 'unsubscribed' ? 'bg-red-100 text-red-800' :
                                'bg-gray-100 text-gray-800'
                              }`}>
                                {subscriber.status}
                              </span>
                            </div>
                          </div>
                          <button
                            type="button"
                            onClick={() => handleRemoveSubscriberFromTemplate(subscriber)}
                            className="text-red-600 hover:text-red-800 text-sm px-2 py-1 rounded hover:bg-red-50 ml-2"
                          >
                            Remove
                          </button>
                        </div>
                      ))
                    ) : (
                      <div className="p-4 text-center text-gray-500 text-sm">
                        {templateSubscribers.length === 0
                          ? "No subscribers assigned to this template"
                          : "No subscribers match the current filters"
                        }
                      </div>
                    )}
                  </div>
                </div>

                {/* Available Subscribers */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">
                    Available Subscribers ({filteredAvailableSubscribers.length} of {availableSubscribers.length})
                  </h4>

                  <div className="border rounded-lg max-h-60 overflow-y-auto">
                    {filteredAvailableSubscribers.length > 0 ? (
                      filteredAvailableSubscribers.map(subscriber => (
                        <div key={subscriber.id} className="flex items-center justify-between p-3 border-b last:border-b-0">
                          <div className="flex-1 min-w-0">
                            <div className="font-medium text-sm truncate">{subscriber.email}</div>
                            <div className="text-xs text-gray-500 truncate">
                              {subscriber.firstName} {subscriber.lastName}
                              {subscriber.organization && ` • ${subscriber.organization}`}
                            </div>
                            <div className="text-xs">
                              <span className={`px-2 py-1 rounded-full text-xs ${
                                subscriber.status === 'active' ? 'bg-green-100 text-green-800' :
                                subscriber.status === 'unsubscribed' ? 'bg-red-100 text-red-800' :
                                'bg-gray-100 text-gray-800'
                              }`}>
                                {subscriber.status}
                              </span>
                            </div>
                          </div>
                          <button
                            type="button"
                            onClick={() => handleAddSubscriberToTemplate(subscriber)}
                            className="text-blue-600 hover:text-blue-800 text-sm px-2 py-1 rounded hover:bg-blue-50 ml-2"
                          >
                            Add
                          </button>
                        </div>
                      ))
                    ) : (
                      <div className="p-4 text-center text-gray-500 text-sm">
                        {availableSubscribers.length === 0
                          ? "All subscribers are already assigned"
                          : "No subscribers match the current filters"
                        }
                      </div>
                    )}
                  </div>
                </div>
              </div>
              </>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-600 mb-4">
                  This template doesn't have a specific area of interest assigned.
                </p>
                <p className="text-sm text-gray-500">
                  To manage subscribers, please edit the template and assign an area of interest.
                </p>
              </div>
            )}

            <div className="flex justify-end space-x-3 mt-6 pt-4 border-t">
              <button
                type="button"
                onClick={() => {
                  setShowSubscriberModal(false);
                  setSelectedTemplateForSubscribers(null);
                  setTemplateSubscribers([]);
                  setAvailableSubscribers([]);
                }}
                className="px-4 py-2 text-gray-600 bg-gray-100 rounded hover:bg-gray-200"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TemplatesPage;
