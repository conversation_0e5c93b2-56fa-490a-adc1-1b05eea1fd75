// WhatsApp API Routes
// backend/routes/whatsapp.js

const express = require('express');
const router = express.Router();
const puppeteer = require('puppeteer');

// WhatsApp Business API Configuration
const WHATSAPP_API_URL = 'https://graph.facebook.com/v17.0';

// Send single message via WhatsApp Business API
router.post('/send-message', async (req, res) => {
  try {
    const { to, message, phoneNumberId, accessToken } = req.body;

    if (!to || !message || !phoneNumberId || !accessToken) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: to, message, phoneNumberId, accessToken'
      });
    }

    // Send message via WhatsApp Business API
    const response = await fetch(`${WHATSAPP_API_URL}/${phoneNumberId}/messages`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        messaging_product: 'whatsapp',
        to: to,
        type: 'text',
        text: {
          body: message
        }
      })
    });

    const result = await response.json();

    if (response.ok && result.messages && result.messages[0]) {
      res.json({
        success: true,
        messageId: result.messages[0].id,
        status: 'sent'
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error?.message || 'Failed to send message'
      });
    }
  } catch (error) {
    console.error('WhatsApp API send error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Test WhatsApp Business API connection
router.post('/test-connection', async (req, res) => {
  try {
    const { phoneNumberId, accessToken } = req.body;

    if (!phoneNumberId || !accessToken) {
      return res.status(400).json({
        success: false,
        error: 'Missing phoneNumberId or accessToken'
      });
    }

    // Test API connection by getting phone number info
    const response = await fetch(`${WHATSAPP_API_URL}/${phoneNumberId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });

    const result = await response.json();

    if (response.ok) {
      res.json({
        success: true,
        message: `Connected to WhatsApp Business API. Phone: ${result.display_phone_number || 'Unknown'}`,
        phoneInfo: result
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error?.message || 'API connection failed'
      });
    }
  } catch (error) {
    console.error('WhatsApp API test error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Test desktop WhatsApp automation
router.post('/test-desktop', async (req, res) => {
  try {
    // Simple test - just check if we can launch browser
    const browser = await puppeteer.launch({
      headless: false,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    await page.goto('https://web.whatsapp.com', { waitUntil: 'networkidle0' });
    
    // Close browser after a moment
    setTimeout(async () => {
      await browser.close();
    }, 3000);

    res.json({
      success: true,
      message: 'Desktop automation test successful. WhatsApp Web opened in browser.'
    });
  } catch (error) {
    console.error('Desktop test error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Send bulk messages via desktop automation
router.post('/send-bulk-desktop', async (req, res) => {
  try {
    const { messages, config } = req.body;

    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No messages provided'
      });
    }

    const results = {
      total: messages.length,
      successful: 0,
      failed: 0,
      results: []
    };

    // Launch browser for automation
    const browser = await puppeteer.launch({
      headless: false,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor'
      ]
    });

    const page = await browser.newPage();
    await page.setViewport({ width: 1366, height: 768 });

    try {
      // Navigate to WhatsApp Web
      await page.goto('https://web.whatsapp.com', { waitUntil: 'networkidle0' });

      // Wait for QR code or main interface
      await page.waitForSelector('[data-testid="qr-code"], [data-testid="chat-list"]', { timeout: 30000 });

      // Check if logged in
      const isLoggedIn = await page.$('[data-testid="chat-list"]');
      
      if (!isLoggedIn) {
        await browser.close();
        return res.json({
          success: false,
          error: 'Please scan QR code in WhatsApp Web to login first',
          results: messages.map(msg => ({
            phone: msg.to,
            success: false,
            error: 'Not logged in to WhatsApp Web'
          }))
        });
      }

      // Process messages in batches
      const batchSize = config?.maxBatchSize || 50;
      const delay = (config?.batchDelay || 3) * 1000;

      for (let i = 0; i < messages.length; i += batchSize) {
        const batch = messages.slice(i, i + batchSize);
        
        for (const message of batch) {
          try {
            // Create new chat URL
            const chatUrl = `https://web.whatsapp.com/send?phone=${message.to}&text=${encodeURIComponent(message.message)}`;
            
            // Navigate to chat
            await page.goto(chatUrl, { waitUntil: 'networkidle0' });
            
            // Wait for send button and click it
            await page.waitForSelector('[data-testid="send"]', { timeout: 10000 });
            await page.click('[data-testid="send"]');
            
            // Wait for message to be sent
            await page.waitForTimeout(2000);

            results.results.push({
              phone: message.to,
              success: true,
              messageId: `desktop_${Date.now()}_${i}`
            });
            results.successful++;

            // Delay between messages
            if (delay > 0) {
              await page.waitForTimeout(delay);
            }
          } catch (error) {
            console.error(`Failed to send to ${message.to}:`, error);
            results.results.push({
              phone: message.to,
              success: false,
              error: error.message
            });
            results.failed++;
          }
        }
      }

      // Close browser if configured
      if (config?.autoClose) {
        await browser.close();
      }

    } catch (error) {
      await browser.close();
      throw error;
    }

    res.json({
      success: true,
      ...results
    });

  } catch (error) {
    console.error('Bulk desktop sending error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      results: req.body.messages?.map(msg => ({
        phone: msg.to,
        success: false,
        error: 'Automation failed'
      })) || []
    });
  }
});

module.exports = router;
