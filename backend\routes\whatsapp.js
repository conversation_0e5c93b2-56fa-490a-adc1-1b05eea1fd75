// WhatsApp API Routes
// backend/routes/whatsapp.js

import express from 'express';
import whatsappDesktopAutomation from '../services/WhatsAppDesktopAutomation.js';

const router = express.Router();

// WhatsApp Business API Configuration
const WHATSAPP_API_URL = 'https://graph.facebook.com/v17.0';

// Send single message via WhatsApp Business API
router.post('/send-message', async (req, res) => {
  try {
    const { to, message, phoneNumberId, accessToken } = req.body;

    if (!to || !message || !phoneNumberId || !accessToken) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: to, message, phoneNumberId, accessToken'
      });
    }

    // Send message via WhatsApp Business API
    const response = await fetch(`${WHATSAPP_API_URL}/${phoneNumberId}/messages`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        messaging_product: 'whatsapp',
        to: to,
        type: 'text',
        text: {
          body: message
        }
      })
    });

    const result = await response.json();

    if (response.ok && result.messages && result.messages[0]) {
      res.json({
        success: true,
        messageId: result.messages[0].id,
        status: 'sent'
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error?.message || 'Failed to send message'
      });
    }
  } catch (error) {
    console.error('WhatsApp API send error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Test WhatsApp Business API connection
router.post('/test-connection', async (req, res) => {
  try {
    const { phoneNumberId, accessToken } = req.body;

    if (!phoneNumberId || !accessToken) {
      return res.status(400).json({
        success: false,
        error: 'Missing phoneNumberId or accessToken'
      });
    }

    // Test API connection by getting phone number info
    const response = await fetch(`${WHATSAPP_API_URL}/${phoneNumberId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });

    const result = await response.json();

    if (response.ok) {
      res.json({
        success: true,
        message: `Connected to WhatsApp Business API. Phone: ${result.display_phone_number || 'Unknown'}`,
        phoneInfo: result
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error?.message || 'API connection failed'
      });
    }
  } catch (error) {
    console.error('WhatsApp API test error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Test desktop WhatsApp automation
router.post('/test-desktop', async (req, res) => {
  try {
    console.log('🧪 Testing WhatsApp Desktop automation...');

    const result = await whatsappDesktopAutomation.testAutomation();

    if (result.success) {
      res.json({
        success: true,
        message: result.message
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    console.error('Desktop test error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Send bulk messages via desktop automation
router.post('/send-bulk-desktop', async (req, res) => {
  try {
    const { messages, config } = req.body;

    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No messages provided'
      });
    }

    console.log(`📱 Starting bulk WhatsApp Desktop automation for ${messages.length} messages`);

    // Use the desktop automation service
    const result = await whatsappDesktopAutomation.sendBulkMessages(messages, config);

    res.json({
      success: true,
      ...result
    });

  } catch (error) {
    console.error('Bulk desktop sending error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      results: req.body.messages?.map(msg => ({
        phone: msg.to,
        success: false,
        error: 'Desktop automation failed'
      })) || []
    });
  }
});

// Get WhatsApp Desktop automation status
router.get('/automation-status', async (req, res) => {
  try {
    const status = whatsappDesktopAutomation.getStatus();
    const isRunning = await whatsappDesktopAutomation.isWhatsAppRunning();

    res.json({
      success: true,
      status: {
        ...status,
        isWhatsAppRunning: isRunning
      }
    });
  } catch (error) {
    console.error('Status check error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Launch WhatsApp Desktop
router.post('/launch-desktop', async (req, res) => {
  try {
    const result = await whatsappDesktopAutomation.launchWhatsApp();

    if (result.success) {
      res.json(result);
    } else {
      res.status(400).json(result);
    }
  } catch (error) {
    console.error('Launch error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

export default router;
