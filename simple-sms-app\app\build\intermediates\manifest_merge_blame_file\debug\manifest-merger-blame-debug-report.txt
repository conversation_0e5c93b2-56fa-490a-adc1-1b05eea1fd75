1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.crmsms"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
8-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="33" />
9-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.SEND_SMS" />
11-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:5:5-67
11-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.INTERNET" />
12-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:6:5-67
12-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:7:5-79
13-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.WAKE_LOCK" />
14-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:8:5-68
14-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:8:22-65
15
16    <permission
16-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7da3e27adb8c02368eb46247c342376b\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
17        android:name="com.crmsms.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
17-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7da3e27adb8c02368eb46247c342376b\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
18        android:protectionLevel="signature" />
18-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7da3e27adb8c02368eb46247c342376b\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
19
20    <uses-permission android:name="com.crmsms.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
20-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7da3e27adb8c02368eb46247c342376b\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
20-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7da3e27adb8c02368eb46247c342376b\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
21
22    <application
22-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:10:5-27:19
23        android:allowBackup="true"
23-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:11:9-35
24        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
24-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7da3e27adb8c02368eb46247c342376b\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
25        android:debuggable="true"
26        android:icon="@mipmap/ic_launcher"
26-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:12:9-43
27        android:label="CRM SMS Gateway"
27-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:13:9-40
28        android:testOnly="true"
29        android:theme="@style/AppTheme"
29-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:14:9-40
30        android:usesCleartextTraffic="true" >
30-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:15:9-44
31        <activity
31-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:17:9-25:20
32            android:name="com.crmsms.MainActivity"
32-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:18:13-41
33            android:exported="true"
33-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:19:13-36
34            android:screenOrientation="portrait" >
34-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:20:13-49
35            <intent-filter>
35-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:21:13-24:29
36                <action android:name="android.intent.action.MAIN" />
36-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:22:17-69
36-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:22:25-66
37
38                <category android:name="android.intent.category.LAUNCHER" />
38-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:23:17-77
38-->E:\Projects\CRM-AIstudio\simple-sms-app\app\src\main\AndroidManifest.xml:23:27-74
39            </intent-filter>
40        </activity>
41
42        <provider
42-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eae2308cc30532fc3480dd892068243b\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
43            android:name="androidx.startup.InitializationProvider"
43-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eae2308cc30532fc3480dd892068243b\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
44            android:authorities="com.crmsms.androidx-startup"
44-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eae2308cc30532fc3480dd892068243b\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
45            android:exported="false" >
45-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eae2308cc30532fc3480dd892068243b\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
46            <meta-data
46-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eae2308cc30532fc3480dd892068243b\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
47                android:name="androidx.emoji2.text.EmojiCompatInitializer"
47-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eae2308cc30532fc3480dd892068243b\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
48                android:value="androidx.startup" />
48-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eae2308cc30532fc3480dd892068243b\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
49            <meta-data
49-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e1771c521a17bca8ec996acad578e4d\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
50                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
50-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e1771c521a17bca8ec996acad578e4d\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
51                android:value="androidx.startup" />
51-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e1771c521a17bca8ec996acad578e4d\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
52        </provider>
53    </application>
54
55</manifest>
