<!DOCTYPE html>
<html>
<head>
    <title>Campaign Database Schema Test</title>
</head>
<body>
    <h1>Campaign Channel Database Test</h1>
    <div id="output"></div>
    
    <script>
        const output = document.getElementById('output');
        
        async function testDatabaseSchema() {
            try {
                output.innerHTML += '<p>🔧 Testing database schema...</p>';
                
                // Test 1: Try to create a campaign with channel data
                const testCampaignData = {
                    name: 'Schema Test Campaign',
                    subject: 'Test Subject',
                    email_content: 'Test email content',
                    sender_name: 'Test Sender',
                    sender_email: '<EMAIL>',
                    status: 'draft',
                    campaign_type: 'newsletter',
                    template_id: null,
                    created_by: 'schema-test',
                    total_recipients: 10,
                    // Channel-specific data
                    email_recipients_count: 8,
                    whatsapp_recipients_count: 5,
                    sms_recipients_count: 3,
                    email_enabled: true,
                    whatsapp_enabled: true,
                    sms_enabled: true
                };
                
                output.innerHTML += '<p>📝 Attempting to create test campaign with channel data...</p>';
                
                const response = await fetch('http://localhost:5177/api/campaigns', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testCampaignData)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    output.innerHTML += '<p>✅ Campaign created successfully!</p>';
                    output.innerHTML += '<p>📊 Campaign ID: ' + result.id + '</p>';
                    
                    // Check if channel data was saved
                    if ('email_recipients_count' in result) {
                        output.innerHTML += '<p>✅ email_recipients_count: ' + result.email_recipients_count + '</p>';
                    } else {
                        output.innerHTML += '<p>❌ email_recipients_count: MISSING</p>';
                    }
                    
                    if ('whatsapp_recipients_count' in result) {
                        output.innerHTML += '<p>✅ whatsapp_recipients_count: ' + result.whatsapp_recipients_count + '</p>';
                    } else {
                        output.innerHTML += '<p>❌ whatsapp_recipients_count: MISSING</p>';
                    }
                    
                    if ('sms_recipients_count' in result) {
                        output.innerHTML += '<p>✅ sms_recipients_count: ' + result.sms_recipients_count + '</p>';
                    } else {
                        output.innerHTML += '<p>❌ sms_recipients_count: MISSING</p>';
                    }
                    
                    if ('email_enabled' in result) {
                        output.innerHTML += '<p>✅ email_enabled: ' + result.email_enabled + '</p>';
                    } else {
                        output.innerHTML += '<p>❌ email_enabled: MISSING</p>';
                    }
                    
                    if ('whatsapp_enabled' in result) {
                        output.innerHTML += '<p>✅ whatsapp_enabled: ' + result.whatsapp_enabled + '</p>';
                    } else {
                        output.innerHTML += '<p>❌ whatsapp_enabled: MISSING</p>';
                    }
                    
                    if ('sms_enabled' in result) {
                        output.innerHTML += '<p>✅ sms_enabled: ' + result.sms_enabled + '</p>';
                    } else {
                        output.innerHTML += '<p>❌ sms_enabled: MISSING</p>';
                    }
                    
                    // Clean up - delete the test campaign
                    output.innerHTML += '<p>🧹 Cleaning up test campaign...</p>';
                    await fetch(`http://localhost:5177/api/campaigns/${result.id}`, {
                        method: 'DELETE'
                    });
                    output.innerHTML += '<p>✅ Test campaign deleted</p>';
                    
                } else {
                    output.innerHTML += '<p>❌ Campaign creation failed: ' + JSON.stringify(result) + '</p>';
                    
                    if (result.details && result.details.includes('no such column')) {
                        output.innerHTML += '<p>🔍 DIAGNOSIS: Database columns are missing!</p>';
                        output.innerHTML += '<p>📋 The error indicates the database schema needs to be updated.</p>';
                    }
                }
                
            } catch (error) {
                output.innerHTML += '<p>❌ Error: ' + error.message + '</p>';
            }
        }
        
        // Auto-run the test
        testDatabaseSchema();
    </script>
</body>
</html>
