import React, { useState } from 'react';
import { whatsappService, WhatsAppMessage } from '../services/WhatsAppService';

interface WhatsAppActionButtonProps {
  phone: string;
  subscriberName?: string;
  subscriberId?: string;
  customMessage?: string;
  variant?: 'button' | 'icon';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  disabled?: boolean;
  onSent?: (result: { success: boolean; messageId?: string; error?: string }) => void;
}

const WhatsAppActionButton: React.FC<WhatsAppActionButtonProps> = ({
  phone,
  subscriberName = '',
  subscriberId,
  customMessage,
  variant = 'button',
  size = 'md',
  className = '',
  disabled = false,
  onSent
}) => {
  const [isLoading, setIsLoading] = useState(false);

  // Check if phone number is valid
  const isValidPhone = whatsappService.isValidPhoneNumber(phone);
  const isGloballyEnabled = whatsappService.isGloballyEnabled();

  // Generate default message if none provided
  const getDefaultMessage = (): string => {
    const greeting = subscriberName ? `Hello ${subscriberName}!` : 'Hello!';
    return `${greeting} This is a message from CRM4CA.`;
  };

  const handleWhatsAppClick = async () => {
    if (!isValidPhone || disabled || isLoading || !isGloballyEnabled) return;

    setIsLoading(true);

    try {
      const message: WhatsAppMessage = {
        to: phone,
        message: customMessage || getDefaultMessage(),
        subscriberId,
        subscriberName
      };

      const result = await whatsappService.sendMessage(message);
      
      if (onSent) {
        onSent(result);
      }

      if (!result.success) {
        console.error('WhatsApp send failed:', result.error);
      }
    } catch (error) {
      console.error('WhatsApp action error:', error);
      if (onSent) {
        onSent({
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Don't render if globally disabled
  if (!isGloballyEnabled) {
    return null;
  }

  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  };

  const buttonSizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-1.5 text-sm',
    lg: 'px-4 py-2 text-base'
  };

  if (variant === 'icon') {
    return (
      <button
        type="button"
        onClick={handleWhatsAppClick}
        disabled={!isValidPhone || disabled || isLoading}
        className={`text-green-600 hover:text-green-800 disabled:text-gray-400 disabled:cursor-not-allowed ${className}`}
        title={
          !isValidPhone ? 'Invalid phone number' :
          isLoading ? 'Sending...' :
          'Send WhatsApp Message'
        }
      >
        {isLoading ? (
          <div className={`animate-spin rounded-full border-2 border-green-600 border-t-transparent ${sizeClasses[size]}`} />
        ) : (
          <svg className={sizeClasses[size]} fill="currentColor" viewBox="0 0 24 24">
            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.595z"/>
          </svg>
        )}
      </button>
    );
  }

  return (
    <button
      type="button"
      onClick={handleWhatsAppClick}
      disabled={!isValidPhone || disabled || isLoading}
      className={`inline-flex items-center gap-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed ${buttonSizeClasses[size]} ${className}`}
    >
      {isLoading ? (
        <div className={`animate-spin rounded-full border-2 border-white border-t-transparent ${sizeClasses[size]}`} />
      ) : (
        <svg className={sizeClasses[size]} fill="currentColor" viewBox="0 0 24 24">
          <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.595z"/>
        </svg>
      )}
      {isLoading ? 'Sending...' : 'WhatsApp'}
    </button>
  );
};

export default WhatsAppActionButton;
