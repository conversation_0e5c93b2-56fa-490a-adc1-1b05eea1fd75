{"name": "CRMSMSApp", "version": "1.0.0", "description": "SMS sending app for CRM system", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint .", "build-android": "cd android && ./gradlew assembleRelease", "build-debug": "cd android && ./gradlew assembleDebug"}, "dependencies": {"react": "18.2.0", "react-native": "0.72.6", "react-native-sms": "^1.9.0", "react-native-permissions": "^3.10.1", "react-native-device-info": "^10.11.0", "react-native-network-info": "^5.2.1", "react-native-vector-icons": "^10.0.0", "@react-native-async-storage/async-storage": "^1.19.3", "react-native-background-service": "^1.0.0", "react-native-background-job": "^1.0.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}}