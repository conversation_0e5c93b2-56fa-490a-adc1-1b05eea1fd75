-- Migration: Add remarks column to campaigns table
-- Date: 2025-06-19
-- Description: Add a remarks/notes field to campaigns for user annotations

-- Check if the column already exists before adding it
-- SQLite doesn't have IF NOT EXISTS for ALTER TABLE, so we'll use a more complex approach

-- First, check if the column exists by querying the table schema
-- If it doesn't exist, add it

-- Add remarks column to campaigns table
ALTER TABLE campaigns ADD COLUMN remarks TEXT;

-- Update any existing campaigns to have empty remarks by default
UPDATE campaigns SET remarks = '' WHERE remarks IS NULL;

-- Create index for better performance on remarks searches (optional)
-- CREATE INDEX idx_campaigns_remarks ON campaigns(remarks);

-- Migration completed successfully
-- The remarks column is now available for storing campaign notes and annotations
