import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Campaign } from '../types';
import { campaignService } from '../services/campaignService-API';
import { ArrowLeftIcon } from '../components/icons';

const ViewCampaignPage: React.FC = () => {
  const { campaignId } = useParams<{ campaignId: string }>();
  const navigate = useNavigate();
  const [campaign, setCampaign] = useState<Campaign | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadCampaign = async () => {
      if (!campaignId) {
        setError('No campaign ID provided');
        setLoading(false);
        return;
      }

      try {
        const campaignData = await campaignService.getCampaignById(campaignId);
        setCampaign(campaignData);
      } catch (err) {
        console.error('Error loading campaign:', err);
        setError('Failed to load campaign details');
      } finally {
        setLoading(false);
      }
    };

    loadCampaign();
  }, [campaignId]);

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not set';
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return dateString;
    }
  };

  const getStatusBadge = (status: string) => {
    const statusColors = {
      'draft': 'bg-gray-100 text-gray-800',
      'scheduled': 'bg-blue-100 text-blue-800',
      'sending': 'bg-yellow-100 text-yellow-800',
      'sent': 'bg-green-100 text-green-800',
      'failed': 'bg-red-100 text-red-800',
      'cancelled': 'bg-red-100 text-red-800'
    };
    
    return (
      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
        statusColors[status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'
      }`}>
        {status}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-textPrimary">Loading campaign details...</div>
      </div>
    );
  }

  if (error || !campaign) {
    return (
      <div className="min-h-screen bg-background p-6">
        <div className="max-w-4xl mx-auto">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h2 className="text-lg font-semibold text-red-800 mb-2">Error</h2>
            <p className="text-red-600">{error || 'Campaign not found'}</p>
            <button
              onClick={() => navigate('/campaigns')}
              className="mt-4 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
            >
              Back to Campaigns
            </button>
          </div>
        </div>
      </div>
    );
  }

  const fieldClass = "block text-sm font-medium text-textPrimary mb-1";
  const valueClass = "w-full p-3 border border-border rounded-lg bg-gray-50 text-textPrimary";

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-4xl mx-auto p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate('/campaigns')}
              className="flex items-center text-primary hover:text-opacity-80"
            >
              <ArrowLeftIcon className="h-5 w-5 mr-2" />
              Back to Campaigns
            </button>
            <h1 className="text-2xl font-bold text-textPrimary">View Campaign</h1>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => navigate(`/campaigns/edit/${campaign.id}`)}
              className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-opacity-90"
            >
              Edit Campaign
            </button>
          </div>
        </div>

        {/* Campaign Details */}
        <div className="bg-cardBackground border border-border rounded-lg shadow-sm">
          <div className="p-6">
            <h2 className="text-lg font-semibold text-textPrimary mb-6">Campaign Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Basic Information */}
              <div>
                <label className={fieldClass}>Campaign Name</label>
                <div className={valueClass}>{campaign.name}</div>
              </div>

              <div>
                <label className={fieldClass}>Status</label>
                <div className={valueClass}>{getStatusBadge(campaign.status)}</div>
              </div>

              <div>
                <label className={fieldClass}>Campaign Type</label>
                <div className={valueClass}>{campaign.is_ad_hoc_campaign ? 'Ad-hoc' : 'Template-based'}</div>
              </div>

              <div>
                <label className={fieldClass}>Created By</label>
                <div className={valueClass}>{campaign.created_by || 'Not specified'}</div>
              </div>

              {/* Scheduling */}
              <div>
                <label className={fieldClass}>Scheduled Date & Time</label>
                <div className={valueClass}>{formatDate(campaign.scheduled_date)}</div>
              </div>

              <div>
                <label className={fieldClass}>Created At</label>
                <div className={valueClass}>{formatDate(campaign.created_at)}</div>
              </div>

              {/* Channel Settings */}
              <div>
                <label className={fieldClass}>Email Enabled</label>
                <div className={valueClass}>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    campaign.email_enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  }`}>
                    {campaign.email_enabled ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>

              <div>
                <label className={fieldClass}>WhatsApp Enabled</label>
                <div className={valueClass}>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    campaign.whatsapp_enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  }`}>
                    {campaign.whatsapp_enabled ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>

              <div>
                <label className={fieldClass}>SMS Enabled</label>
                <div className={valueClass}>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    campaign.sms_enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  }`}>
                    {campaign.sms_enabled ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>
            </div>

            {/* Content Sections */}
            {campaign.subject && (
              <div className="mt-6">
                <label className={fieldClass}>Email Subject</label>
                <div className={valueClass}>{campaign.subject}</div>
              </div>
            )}

            {campaign.email_content && (
              <div className="mt-6">
                <label className={fieldClass}>Email Content</label>
                <div className="w-full p-3 border border-border rounded-lg bg-gray-50 text-textPrimary min-h-[200px]">
                  <div dangerouslySetInnerHTML={{ __html: campaign.email_content }} />
                </div>
              </div>
            )}

            {campaign.whatsapp_content && (
              <div className="mt-6">
                <label className={fieldClass}>WhatsApp Content</label>
                <div className="w-full p-3 border border-border rounded-lg bg-gray-50 text-textPrimary min-h-[100px] whitespace-pre-wrap">
                  {campaign.whatsapp_content}
                </div>
              </div>
            )}

            {campaign.sms_content && (
              <div className="mt-6">
                <label className={fieldClass}>SMS Content</label>
                <div className="w-full p-3 border border-border rounded-lg bg-gray-50 text-textPrimary min-h-[100px] whitespace-pre-wrap">
                  {campaign.sms_content}
                </div>
              </div>
            )}

            {/* Remarks */}
            <div className="mt-6">
              <label className={fieldClass}>Remarks</label>
              <div className="w-full p-3 border border-border rounded-lg bg-gray-50 text-textPrimary min-h-[100px] whitespace-pre-wrap">
                {campaign.remarks || 'No remarks added'}
              </div>
            </div>

            {/* Campaign Statistics */}
            <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className={fieldClass}>Total Recipients</label>
                <div className={valueClass}>{campaign.total_recipients || 0}</div>
              </div>
              <div>
                <label className={fieldClass}>Opened</label>
                <div className={valueClass}>{campaign.opened || 0}</div>
              </div>
              <div>
                <label className={fieldClass}>Clicked</label>
                <div className={valueClass}>{campaign.clicked || 0}</div>
              </div>
              <div>
                <label className={fieldClass}>Bounced</label>
                <div className={valueClass}>{campaign.bounced || 0}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewCampaignPage;
