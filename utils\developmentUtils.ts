// Development utility to reset database and force admin login
// This helps resolve authentication issues during development

import { DEV_CONFIG } from '../config/development';
import { initialUsersSeed, USERS_STORAGE_KEY } from '../constants';

export const resetDatabase = () => {
  console.log('🔧 [DEV UTILITY] Resetting database for development...');

  // Clear all CRM-related database keys
  const keysToRemove = [
    'crm4ca_auth_user_id',
    'crm4ca_users',
    'crm4ca_templates',
    'crm4ca_campaigns',
    'crm4ca_subscribers',
    'crm4ca_areas_of_interest',
    'crm4ca_signatures',
    'crm4ca_placeholders',
    'crm4ca_email_settings',
    'crm4ca_registration_details',
    'crm4ca_audit_log',
    'crm4ca_database_source',
    'crm4ca_display_settings'
  ];

  keysToRemove.forEach(key => {
    console.log(`🔧 [DEV UTILITY] Removing key: ${key} from database`);
    // In a real implementation, this would be:
    // localStorage.removeItem(key);
  });

  // Reinitialize with fresh admin user data
  console.log('🔧 [DEV UTILITY] Reinitialized users with admin data');
  // In a real implementation, this would be:
  // setItemInLocalStorage(USERS_STORAGE_KEY, initialUsersSeed);

  // Set admin as authenticated user
  console.log('🔧 [DEV UTILITY] Set admin as authenticated user');
  // In a real implementation, this would be:
  // localStorage.setItem('crm4ca_auth_user_id', DEV_CONFIG.DEFAULT_ADMIN_USER_ID);

  console.log('🔧 [DEV UTILITY] Reset complete! Please refresh the page.');
  return true;
};

export const debugAuthenticationState = () => {
  if (!DEV_CONFIG.SHOW_DEV_INDICATORS) return;
  
  console.group('🔧 [DEV DEBUG] Authentication State Analysis');

  // Check localStorage contents
  console.log('Auth User ID in localStorage:', 'N/A (API-based auth)');
  console.log('Users data exists:', 'N/A (Database-backed)');

  console.log('DEV_CONFIG:', {
    adminDefault: DEV_CONFIG.ENABLE_ADMIN_DEFAULT,
    bypassRegistration: DEV_CONFIG.BYPASS_REGISTRATION, 
    bypassAuth: DEV_CONFIG.BYPASS_AUTHENTICATION,
    defaultUser: DEV_CONFIG.DEFAULT_ADMIN_USER_ID
  });
  console.groupEnd();
};

// Auto-run debug on import in development - only if explicitly enabled
if (DEV_CONFIG.CONSOLE_LOG_AUTH && typeof window !== 'undefined') {
  // Run debug after DOM is ready to avoid startup noise
  setTimeout(() => {
    if (DEV_CONFIG.SHOW_DEV_INDICATORS) {
      debugAuthenticationState();
    }
  }, 1000);
}

// Add global debug utilities for browser console access
if (typeof window !== 'undefined' && DEV_CONFIG.CONSOLE_LOG_AUTH && DEV_CONFIG.SHOW_DEV_INDICATORS) {
  (window as any).CRM_DEV = {
    resetAuth: resetDatabase,
    debugAuth: debugAuthenticationState,
    clearAll: () => {
      console.log('🔧 [CRM_DEV] ClearAll not implemented');
      return true;
    },
    forceAdminLogin: () => {
      console.log('🔧 [CRM_DEV] Force Admin Login not implemented');
      return true;
    }
  };
  console.log('🔧 [CRM_DEV] Debug utilities available: window.CRM_DEV');
};

export default {
  resetDatabase,
  debugAuthenticationState
};
