
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import Header from '../components/Header';
import RichTextEditor from '../components/RichTextEditor';
import ToggleableRichTextEditor from '../components/ToggleableRichTextEditor';
import ContextualRichTextEditor from '../components/ContextualRichTextEditor';
import SubscriberSelector from '../components/SubscriberSelector';
import EnhancedSubscriberSelectorModal from '../components/EnhancedSubscriberSelectorModal';
import EnhancedDateTimePicker from '../components/EnhancedDateTimePicker';
import AttachmentManager from '../components/AttachmentManager';
// Removed CampaignSendingStatusModal import - now using dedicated page
import { 
    Campaign, CampaignStatus, CampaignType, 
    CampaignTemplate, MessageSignature, User, Channel, UserRole, TemplateStatus, RecurrenceType, HolidayHandlingRule, AuditActionType, EmailSettings, RegistrationDetails, SubscriberProfileStatus, EmailAttachment
} from '../types';
import { DeleteIcon, PaperAirplaneIcon, CogIcon, LockClosedIcon, ClockIcon } from '../components/icons';
import { 
    initialRegistrationDetails, 
    initialEmailSettings
} from '../constants';
import { formatEnumValueForDisplay } from '../utils/displayUtils';
import { calculateNextRecurrenceDate, formatDateForDisplay, parseIndianDateTimeStringToDate, isValidIndianDateInput, getPreviousMonthAndYear, parseIndianDateStringToDate, formatDateForDatabase, formatDateForInput } from '../utils/dateUtils'; 
import { addAuditLog } from '../utils/auditUtilsDB';
import CountdownTimer from '../components/CountdownTimer';
import { useAuth } from '../contexts/AuthContextDB';
import { canUserEditDeleteItem, AccessibleItem, canUserExecuteCampaign, canUserViewItem } from '../utils/accessControlUtils';
import ConfirmationModal from '../components/ConfirmationModal';
import ChannelToggles from '../components/ChannelToggles';
import SearchableTemplateDropdown from '../components/SearchableTemplateDropdown';
import FloatingActionBar from '../components/FloatingActionBar';
import { campaignService } from '../services/campaignService-API';
import { templateService } from '../services/TemplateService-API';
import { signatureService } from '../services/SignatureService-API';
import { userService } from '../services/userService-API';
import { subscriberService } from '../services/SubscriberService-API';


type CampaignFormData = Omit<Campaign, 'id' | 'created_at' | 'updated_at' | 'sent_date' | 'opened' | 'clicked' | 'bounced' | 'scheduled_date'> & {
    scheduled_date_input: string;
    sender_name: string;
    sender_email: string;
    sender_phone?: string;
    // Channel-specific recipient counts
    email_recipients_count?: number;
    whatsapp_recipients_count?: number;
    sms_recipients_count?: number;
};


const initialCampaignFormData: CampaignFormData = {
  name: '',
  subject: '',
  template_id: '',
  email_content: '',
  email_html_content: '',
  whatsapp_content: '',
  sms_content: '',
  email_signature_id: '',
  whatsapp_signature_id: '',
  sms_signature_id: '',
  status: CampaignStatus.DRAFT,
  campaign_type: CampaignType.AD_HOC, // Default to ad-hoc for new campaigns
  uses_placeholders: false,
  available_placeholders: [],
  campaign_specific_placeholder_values: {},
  created_by: '',
  scheduled_date_input: '',
  total_recipients: 0,
  birthday_send_offset_days: 0,
  birthday_send_time: '09:00',
  holiday_handling_rule: HolidayHandlingRule.SEND_ON_HOLIDAY,
  processed_recipients_count: 0,
  last_batch_sent_date: undefined,
  sent_in_current_hour_count: 0,
  current_hour_window_start_date: undefined,
  sent_in_current_day_count: 0,
  current_day_window_start_date: undefined,
  next_batch_eligible_at: undefined,
  is_admin_only: false,
  owner_user_id: undefined,
  sender_name: '',
  sender_email: '',
  sender_phone: '',
  // Channel-specific recipient counts
  email_recipients_count: 0,
  whatsapp_recipients_count: 0,
  sms_recipients_count: 0,
  // Ad-hoc campaign fields
  is_ad_hoc_campaign: true, // Default to true for new campaigns
  selected_subscriber_ids: [],
  subscriber_selection_mode: 'manual', // Default to manual for ad-hoc
  // Attachments
  attachments: [],
  use_bcc_for_attachments: false, // Default to individual emails
  // Remarks
  remarks: '', // New field for campaign notes
};

const SYSTEM_PLACEHOLDERS_TO_EXCLUDE_FROM_CAMPAIGN_INPUTS = [
    'subscriber_name', 
    'subscriber_firstName', 
    'subscriber_lastName', 
    'subscriber_email', 
    'subscriber_phone',
    'subscriber_birthDate',
    'subscriber_entityName',
    'unsubscribe_link',
    'view_in_browser_link', 
    'organization_name', 
    'user_name',
    'user_email',
    'system_previous_month_name',
    'system_previous_month_year',
];

const PREVIOUS_MONTH_NAME_PLACEHOLDER = "system_previous_month_name";
const PREVIOUS_MONTH_YEAR_PLACEHOLDER = "system_previous_month_year";


const renderContentWithPlaceholders = (content: string, placeholderValues: Record<string, string> = {}): string => {
  if (!content) return '';
  let renderedContent = content;
  for (const key in placeholderValues) {
    const value = placeholderValues[key];
    const regex = new RegExp(`\\{\\{\\s*${key}\\s*\\}\\}`, 'g');
    renderedContent = renderedContent.replace(regex, value || ''); 
  }
  return renderedContent;
};

const autoPopulateSystemPlaceholders = (
    currentFormData: CampaignFormData, 
    template: CampaignTemplate | undefined,
    useCurrentDateForSendNow: boolean = false
): Partial<CampaignFormData> => {
    const updates: Partial<Pick<CampaignFormData, 'campaign_specific_placeholder_values'>> = {};
    let newPlaceholderValues = { ...(currentFormData.campaign_specific_placeholder_values || {}) }; 
    let changed = false;

    if (!template) return {};

    const templateContentStrings = [
        template.subject_template, template.email_content_template, template.email_html_template,
        template.whatsapp_content_template, template.sms_content_template_template,
    ].filter(Boolean).join(' ');

    const needsMonthName = templateContentStrings.includes(`{{${PREVIOUS_MONTH_NAME_PLACEHOLDER}}}`);
    const needsMonthYear = templateContentStrings.includes(`{{${PREVIOUS_MONTH_YEAR_PLACEHOLDER}}}`);

    let validScheduledDate: Date | null = null;
    
    // For Send Now operations, use current date instead of scheduled date
    if (useCurrentDateForSendNow) {
        validScheduledDate = new Date();
    } else if (currentFormData.scheduled_date_input) {
        const datePart = currentFormData.scheduled_date_input.split(' ')[0];
        if (datePart && isValidIndianDateInput(datePart)) {
            validScheduledDate = parseIndianDateTimeStringToDate(currentFormData.scheduled_date_input);
        }
    }

    // If we need month/year placeholders and have a valid date (scheduled or current)
    if ((needsMonthName || needsMonthYear) && validScheduledDate) { 
        const { month, year } = getPreviousMonthAndYear(validScheduledDate);
        
        if (needsMonthName) {
            if (newPlaceholderValues[PREVIOUS_MONTH_NAME_PLACEHOLDER] !== month) {
                newPlaceholderValues[PREVIOUS_MONTH_NAME_PLACEHOLDER] = month;
                changed = true;
            }
        } else if (newPlaceholderValues[PREVIOUS_MONTH_NAME_PLACEHOLDER] !== undefined) { 
            delete newPlaceholderValues[PREVIOUS_MONTH_NAME_PLACEHOLDER];
            changed = true;
        }

        if (needsMonthYear) {
            if (newPlaceholderValues[PREVIOUS_MONTH_YEAR_PLACEHOLDER] !== String(year)) {
                newPlaceholderValues[PREVIOUS_MONTH_YEAR_PLACEHOLDER] = String(year);
                changed = true;
            }
        } else if (newPlaceholderValues[PREVIOUS_MONTH_YEAR_PLACEHOLDER] !== undefined) { 
            delete newPlaceholderValues[PREVIOUS_MONTH_YEAR_PLACEHOLDER];
            changed = true;
        }
    } else { 
        // Only clean up placeholders if they're not needed in template
        if (!needsMonthName && newPlaceholderValues[PREVIOUS_MONTH_NAME_PLACEHOLDER] !== undefined) {
            delete newPlaceholderValues[PREVIOUS_MONTH_NAME_PLACEHOLDER];
            changed = true;
        }
        if (!needsMonthYear && newPlaceholderValues[PREVIOUS_MONTH_YEAR_PLACEHOLDER] !== undefined) {
            delete newPlaceholderValues[PREVIOUS_MONTH_YEAR_PLACEHOLDER];
            changed = true;
        }
    }
    
    if (changed) {
        updates.campaign_specific_placeholder_values = newPlaceholderValues;
    }
    return updates;
};


const AddEditCampaignPage: React.FC = () => {
  const navigate = useNavigate();
  const { campaignId } = useParams<{ campaignId?: string }>();
  const { currentUser } = useAuth();
  const [isEditMode, setIsEditMode] = useState(false);
  const [formData, setFormData] = useState<CampaignFormData>(initialCampaignFormData);
  const [feedbackMessage, setFeedbackMessage] = useState<{ type: 'success' | 'error' | 'info'; message: string } | null>(null);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [createdCampaignInfo, setCreatedCampaignInfo] = useState<{ name: string; status: string } | null>(null);

  // Auto-scroll to error message when it appears
  useEffect(() => {
    if (feedbackMessage?.type === 'error') {
      const errorElement = document.querySelector('[role="alert"]');
      if (errorElement) {
        errorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }
  }, [feedbackMessage]);
  const [originalName, setOriginalName] = useState<string>('');
  const [isFormDisabled, setIsFormDisabled] = useState(false);
  const [loadedCampaign, setLoadedCampaign] = useState<Campaign | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [isEmailRichTextMode, setIsEmailRichTextMode] = useState(true); // Default to rich text mode
  const [isWhatsAppRichTextMode, setIsWhatsAppRichTextMode] = useState(true); // ✅ Changed to rich text by default
  const [isSMSRichTextMode, setIsSMSRichTextMode] = useState(true); // ✅ Changed to rich text by default

  // Ad-hoc campaign subscriber selection state
  const [subscriberSelectionMode, setSubscriberSelectionMode] = useState<'all' | 'segment' | 'manual'>('segment');
  const [selectedSubscriberIds, setSelectedSubscriberIds] = useState<string[]>([]);
  const [showEnhancedModal, setShowEnhancedModal] = useState(false);

  // Campaign sending status - now handled via page navigation
  // Removed modal state variables

  // Floating action bar state
  const [isSaving, setIsSaving] = useState(false);

  const [availableTemplates, setAvailableTemplates] = useState<CampaignTemplate[]>([]);
  const [availableSignatures, setAvailableSignatures] = useState<MessageSignature[]>([]);
  const [availableUsers, setAvailableUsers] = useState<User[]>([]);
  const [subscribers, setSubscribers] = useState<any[]>([]);
  const [isBirthdayCampaign, setIsBirthdayCampaign] = useState(false);
  const [, setSendButtonCountdownText] = useState<string>('');
  const [registrationDetails, setRegistrationDetails] = useState<RegistrationDetails | null>(null);

  // Channel toggle states
  const [emailEnabled, setEmailEnabled] = useState(true);
  const [whatsappEnabled, setWhatsappEnabled] = useState(true);
  const [smsEnabled, setSmsEnabled] = useState(true);

  // Since campaign type selection was removed, all campaigns are template-based
  const isAdHocCampaign = false;


  const updateCampaignState = useCallback((campaignToUpdate: Campaign) => {
    setLoadedCampaign(campaignToUpdate); 
    if (isEditMode && !canUserEditDeleteItem(campaignToUpdate, currentUser)) { 
        setFeedbackMessage({type: 'error', message: "Access Denied: You do not have permission to edit this campaign."});
        setIsFormDisabled(true);
    } else {
        setIsFormDisabled(false);
    }
    const { id, created_at, updated_at, sent_date, opened, clicked, bounced, scheduled_date, ...formDataFields } = campaignToUpdate;

    console.log('🔍 [updateCampaignState] Campaign scheduled_date from DB:', scheduled_date);
    console.log('🔍 [updateCampaignState] Campaign scheduled_date type:', typeof scheduled_date);
    
    // DEBUG: Log the actual campaign content from DB
    console.log('🔍 [updateCampaignState] Raw campaign content from DB:');
    console.log('  email_content:', campaignToUpdate.email_content ? `"${campaignToUpdate.email_content}"` : 'NULL/EMPTY');
    console.log('  email_html_content:', campaignToUpdate.email_html_content ? `"${campaignToUpdate.email_html_content}"` : 'NULL/EMPTY');
    console.log('  whatsapp_content:', campaignToUpdate.whatsapp_content ? `"${campaignToUpdate.whatsapp_content}"` : 'NULL/EMPTY');
    console.log('  sms_content:', campaignToUpdate.sms_content ? `"${campaignToUpdate.sms_content}"` : 'NULL/EMPTY');
    console.log('  sender_name:', campaignToUpdate.sender_name ? `"${campaignToUpdate.sender_name}"` : 'NULL/EMPTY');
    console.log('  sender_email:', campaignToUpdate.sender_email ? `"${campaignToUpdate.sender_email}"` : 'NULL/EMPTY');

    const allTemplates = availableTemplates; // Use already loaded templates
    const campaignTemplate = allTemplates.find(t => t.id === campaignToUpdate.template_id);

    const scheduledDateInput = scheduled_date ? formatDateForInput(new Date(scheduled_date)) : '';
    console.log('🔍 [updateCampaignState] Formatted scheduled_date_input:', scheduledDateInput);

    const initialDataForForm: CampaignFormData = {
        ...initialCampaignFormData,
        ...formDataFields,
        // Explicitly preserve campaign's actual content (handle null/undefined values)
        email_content: campaignToUpdate.email_content || '',
        email_html_content: campaignToUpdate.email_html_content || '',
        whatsapp_content: campaignToUpdate.whatsapp_content || '',
        sms_content: campaignToUpdate.sms_content || '',
        // Preserve campaign's actual sender values (don't override with template values)
        sender_name: campaignToUpdate.sender_name || campaignTemplate?.sender_name || '',
        sender_email: campaignToUpdate.sender_email || campaignTemplate?.sender_email || '',
        sender_phone: campaignToUpdate.sender_phone || campaignTemplate?.sender_phone || '',
        scheduled_date_input: scheduledDateInput,
        campaign_specific_placeholder_values: campaignToUpdate.campaign_specific_placeholder_values || {},
        available_placeholders: Array.isArray(campaignToUpdate.available_placeholders) ? campaignToUpdate.available_placeholders : [],
        is_admin_only: !!campaignToUpdate.is_admin_only,
        owner_user_id: campaignToUpdate.owner_user_id || undefined,
        // Channel counts
        email_recipients_count: campaignToUpdate.email_recipients_count || 0,
        whatsapp_recipients_count: campaignToUpdate.whatsapp_recipients_count || 0,
        sms_recipients_count: campaignToUpdate.sms_recipients_count || 0,
        // Preserve subscriber targeting data
        target_subscriber_ids: campaignToUpdate.target_subscriber_ids || [],
        selected_subscriber_ids: campaignToUpdate.selected_subscriber_ids || [],
        subscriber_selection_mode: campaignToUpdate.subscriber_selection_mode || 'segment',
        total_recipients: campaignToUpdate.total_recipients || 0,
    };

    console.log('🔍 [updateCampaignState] Campaign content fields from DB:');
    console.log('  email_content:', campaignToUpdate.email_content?.length || 0, 'chars');
    console.log('  email_html_content:', campaignToUpdate.email_html_content?.length || 0, 'chars');
    console.log('  whatsapp_content:', campaignToUpdate.whatsapp_content?.length || 0, 'chars');
    console.log('  sms_content:', campaignToUpdate.sms_content?.length || 0, 'chars');
    console.log('🔍 [updateCampaignState] FormData after spread:');
    console.log('  email_content:', initialDataForForm.email_content?.length || 0, 'chars');
    console.log('  email_html_content:', initialDataForForm.email_html_content?.length || 0, 'chars');
    console.log('  whatsapp_content:', initialDataForForm.whatsapp_content?.length || 0, 'chars');
    console.log('  sms_content:', initialDataForForm.sms_content?.length || 0, 'chars');
    
    const systemPlaceholderUpdates = autoPopulateSystemPlaceholders(initialDataForForm, campaignTemplate);
    const finalFormData = { ...initialDataForForm, ...systemPlaceholderUpdates };

    console.log('🔍 [updateCampaignState] Final FormData before setFormData:');
    console.log('  email_content:', finalFormData.email_content?.length || 0, 'chars');
    console.log('  email_html_content:', finalFormData.email_html_content?.length || 0, 'chars');
    console.log('  whatsapp_content:', finalFormData.whatsapp_content?.length || 0, 'chars');
    console.log('  sms_content:', finalFormData.sms_content?.length || 0, 'chars');
    console.log('  sender_name:', finalFormData.sender_name);
    console.log('  sender_email:', finalFormData.sender_email);
    console.log('  scheduled_date_input:', finalFormData.scheduled_date_input);

    setFormData(finalFormData);
    setOriginalName(campaignToUpdate.name);

    // Set channel toggles based on loaded campaign
    setEmailEnabled(campaignToUpdate.email_enabled ?? true);
    setWhatsappEnabled(campaignToUpdate.whatsapp_enabled ?? true);
    setSmsEnabled(campaignToUpdate.sms_enabled ?? true);

    // Set rich text mode settings based on campaign or template defaults
    const currentTemplate = availableTemplates.find(t => t.id === campaignToUpdate.template_id);
    setIsEmailRichTextMode(currentTemplate?.email_is_rich_text ?? true);
    setIsWhatsAppRichTextMode(currentTemplate?.whatsapp_is_rich_text ?? true);
    setIsSMSRichTextMode(currentTemplate?.sms_is_rich_text ?? true);

    // Set ad-hoc campaign state based on loaded campaign
    if (campaignToUpdate.is_ad_hoc_campaign || campaignToUpdate.campaign_type === CampaignType.AD_HOC) {
      setSubscriberSelectionMode(campaignToUpdate.subscriber_selection_mode || 'manual');
      setSelectedSubscriberIds(campaignToUpdate.selected_subscriber_ids || []);
    } else {
      // For template-based campaigns, default to 'segment' targeting
      setSubscriberSelectionMode('segment');
      setSelectedSubscriberIds([]);
    }
    
    setIsBirthdayCampaign(currentTemplate?.campaign_type === CampaignType.BIRTHDAY_WISH);
  }, [isEditMode, currentUser, availableTemplates]); // Added availableTemplates dependency


  useEffect(() => {
    const loadData = async () => {
      try {
        // Load registration details from API
        // TODO: Replace with proper registration details API call
        setRegistrationDetails(initialRegistrationDetails);

        // Load users from API
        const allUsers = await userService.getAllUsers();
        const loadedUsers = allUsers.filter(u => u.is_active);
        setAvailableUsers(loadedUsers);

        // Load templates from API
        const allTemplates = await templateService.getAllTemplates();
        const activeTemplates = allTemplates
            .filter(t => t.status === TemplateStatus.ACTIVE && t.is_active && canUserViewItem(t as AccessibleItem, currentUser));
        setAvailableTemplates(activeTemplates);

        // Load signatures from API
        const allSignatures = await signatureService.getAllSignatures();
        const activeSignatures = allSignatures
            .filter(s => s.is_active && canUserViewItem(s as AccessibleItem, currentUser));
        setAvailableSignatures(activeSignatures);

        // Load subscribers from API
        const allSubscribers = await subscriberService.getAllSubscribers();
        setSubscribers(allSubscribers);
      } catch (error) {
        console.error('Error loading form data:', error);
        setFeedbackMessage({ type: 'error', message: 'Failed to load form data. Please refresh the page.' });
      }
    };

    loadData();
  }, [currentUser]); // Fixed: Added dependency array to prevent infinite loop
    
  useEffect(() => {
    // Load campaign data if in edit mode
    const fetchCampaignData = async () => {
        if (campaignId && availableTemplates.length > 0) { // Wait for templates to be loaded
          console.log('🔄 [fetchCampaignData] Loading campaign with templates available:', availableTemplates.length);
          setIsEditMode(true);
          try {
            const campaignToEdit = await campaignService.getCampaignById(campaignId);
            if (campaignToEdit) {
              console.log('🔍 [fetchCampaignData] Campaign loaded from API:', campaignToEdit.name);
              updateCampaignState(campaignToEdit);
            } else {
              setFeedbackMessage({ type: 'error', message: 'Campaign not found.' });
              navigate('/campaigns');
            }
          } catch (error) {
            console.error('Error loading campaign:', error);
            setFeedbackMessage({ type: 'error', message: 'Failed to load campaign data.' });
          }
        } else if (campaignId && availableTemplates.length === 0) {
          console.log('🔄 [fetchCampaignData] Waiting for templates to load before loading campaign...');
        } else { 
          setIsEditMode(false);
          setLoadedCampaign(null); 
          setIsFormDisabled(currentUser?.role === UserRole.VIEWER); 
          if (currentUser?.role === UserRole.VIEWER) {
              setFeedbackMessage({ type: 'error', message: 'Access Denied: Viewers cannot create campaigns.' });
          }
          setFormData(prev => ({
            ...initialCampaignFormData,
            created_by: '', // Will be set when users are loaded
            campaign_specific_placeholder_values: {}, 
            is_admin_only: false,
            owner_user_id: undefined,
          }));
          setOriginalName('');
          setIsBirthdayCampaign(false);
        }
    };
    fetchCampaignData();
  }, [campaignId, navigate, updateCampaignState, currentUser, availableTemplates]); // Added availableTemplates dependency 

  useEffect(() => {
    if (!isEditMode && availableUsers.length > 0 && !formData.created_by && currentUser) {
      setFormData(prev => ({
        ...prev, 
        created_by: currentUser.user_id, // Use logged-in user instead of first user
        // Ensure ad-hoc state is properly initialized
        is_ad_hoc_campaign: prev.campaign_type === CampaignType.AD_HOC,
        subscriber_selection_mode: prev.subscriber_selection_mode || 'all'
      }));
    }
  }, [availableUsers, isEditMode, currentUser]); // Added currentUser dependency

  // ✅ Ensure created_by is always set for new campaigns
  useEffect(() => {
    if (!isEditMode && currentUser && !formData.created_by) {
      console.log('🔄 Setting created_by to current user:', currentUser.user_id);
      setFormData(prev => ({
        ...prev,
        created_by: currentUser.user_id
      }));
    }
  }, [currentUser, isEditMode, formData.created_by]);

  // ✅ NEW: Initialize recipient count for "All Subscribers" mode on page load
  useEffect(() => {
    if (!isEditMode && subscriberSelectionMode === 'all' && formData.total_recipients === 0) {
      console.log('🔄 Initializing recipient count for "All Subscribers" mode...');
      handleSubscriberSelectionModeChange('all');
    }
  }, [isEditMode, subscriberSelectionMode]); // Runs when page loads for new campaigns


  const emailSignatures = availableSignatures.filter(sig => sig.channel === Channel.EMAIL);
  const whatsappSignatures = availableSignatures.filter(sig => sig.channel === Channel.WHATSAPP);
  const smsSignatures = availableSignatures.filter(sig => sig.channel === Channel.SMS);

  const getResolvedPlaceholderValues = useCallback((): Record<string, string> => {
    const systemValues: Record<string, string> = {};
    if (registrationDetails?.isRegistered && registrationDetails.registeredOrganizationName) {
      systemValues['organization_name'] = registrationDetails.registeredOrganizationName;
    }
  
    return {
      ...systemValues,
      ...(formData.campaign_specific_placeholder_values || {}), 
    };
  }, [registrationDetails, formData.campaign_specific_placeholder_values]);

  // Function to calculate total recipients based on template selection
  const calculateTotalRecipients = useCallback(async (templateId: string) => {
    console.log('🔄 [calculateTotalRecipients] Starting calculation for template:', templateId);
    if (!templateId) {
      console.log('❌ [calculateTotalRecipients] No template ID provided');
      return 0;
    }

    try {
      const selectedTemplate = availableTemplates.find(t => t.id === templateId);
      console.log('🔍 [calculateTotalRecipients] Selected template:', selectedTemplate);
      if (!selectedTemplate) {
        console.log('❌ [calculateTotalRecipients] Template not found');
        return 0;
      }

      // Get all active subscribers
      const allSubscribers = await subscriberService.getAllSubscribers();
      const activeSubscribers = allSubscribers.filter(s => s.status === SubscriberProfileStatus.ACTIVE);
      console.log('📊 [calculateTotalRecipients] Total active subscribers:', activeSubscribers.length);

      // If template has specific interest area, filter by that
      if (selectedTemplate.interest_area_id) {
        console.log('🎯 [calculateTotalRecipients] Template has interest area:', selectedTemplate.interest_area_id);
        const targetSubscribers = activeSubscribers.filter(subscriber => {
          const areasArray = subscriber.areasOfInterestIds || [];
          console.log('🔍 [calculateTotalRecipients] Subscriber areas:', subscriber.email, areasArray);

          if (!Array.isArray(areasArray)) {
            return false;
          }

          const matches = areasArray.includes(selectedTemplate.interest_area_id!);
          console.log('🔍 [calculateTotalRecipients] Subscriber matches:', subscriber.email, matches);
          return matches;
        });

        console.log('✅ [calculateTotalRecipients] Target subscribers count:', targetSubscribers.length);
        return targetSubscribers.length;
      }

      // For birthday campaigns, count subscribers with birth dates
      if (selectedTemplate.campaign_type === CampaignType.BIRTHDAY_WISH) {
        const birthdaySubscribers = activeSubscribers.filter(subscriber =>
          subscriber.birthDate && subscriber.birthDate.trim() !== ''
        );
        console.log('🎂 [calculateTotalRecipients] Birthday subscribers count:', birthdaySubscribers.length);
        return birthdaySubscribers.length;
      }

      // For other campaigns without specific segments, return all active subscribers
      console.log('📧 [calculateTotalRecipients] General campaign - all active subscribers:', activeSubscribers.length);
      return activeSubscribers.length;
    } catch (error) {
      console.error('❌ [calculateTotalRecipients] Error calculating total recipients:', error);
      return 0;
    }
  }, [availableTemplates]);

  // Function to calculate channel-specific recipient counts
  const calculateChannelRecipients = useCallback(async (templateId: string) => {
    console.log('🔄 [calculateChannelRecipients] Starting calculation for template:', templateId);
    if (!templateId) {
      return { email: 0, whatsapp: 0, sms: 0, total: 0 };
    }

    try {
      const selectedTemplate = availableTemplates.find(t => t.id === templateId);
      if (!selectedTemplate) {
        return { email: 0, whatsapp: 0, sms: 0, total: 0 };
      }

      const allSubscribers = await subscriberService.getAllSubscribers();
      let targetSubscribers = allSubscribers.filter(s => s.status === SubscriberProfileStatus.ACTIVE);

      console.log('📊 [calculateChannelRecipients] Total active subscribers:', targetSubscribers.length);

      // Filter by template's interest area if applicable
      if (selectedTemplate.interest_area_id) {
        targetSubscribers = targetSubscribers.filter(subscriber => {
          const areasArray = subscriber.areasOfInterestIds || [];
          return Array.isArray(areasArray) && areasArray.includes(selectedTemplate.interest_area_id!);
        });
        console.log('🎯 [calculateChannelRecipients] Filtered by interest area:', targetSubscribers.length);
      }

      // For birthday campaigns, filter by birth date
      if (selectedTemplate.campaign_type === CampaignType.BIRTHDAY_WISH) {
        targetSubscribers = targetSubscribers.filter(subscriber =>
          subscriber.birthDate && subscriber.birthDate.trim() !== ''
        );
        console.log('🎂 [calculateChannelRecipients] Filtered by birth date:', targetSubscribers.length);
      }

      // Calculate channel-specific counts with detailed logging
      const emailRecipients = targetSubscribers.filter(s => {
        const hasEmail = s.email && s.email.trim() !== '';
        console.log(`📧 [calculateChannelRecipients] ${s.email}: hasEmail=${hasEmail}`);
        return hasEmail;
      });

      const whatsappRecipients = targetSubscribers.filter(s => {
        const hasPhone = s.phone && s.phone.trim() !== '';
        const allowsWhatsApp = s.allowWhatsApp === true;
        console.log(`💬 [calculateChannelRecipients] ${s.email}: hasPhone=${hasPhone}, allowsWhatsApp=${allowsWhatsApp}, phone="${s.phone}"`);
        return hasPhone && allowsWhatsApp;
      });

      const smsRecipients = targetSubscribers.filter(s => {
        const hasPhone = s.phone && s.phone.trim() !== '';
        const allowsSms = s.allowSms === true;
        console.log(`📱 [calculateChannelRecipients] ${s.email}: hasPhone=${hasPhone}, allowsSms=${allowsSms}, phone="${s.phone}"`);
        return hasPhone && allowsSms;
      });

      // Calculate total unique recipients (avoid double counting)
      const allRecipientIds = new Set([
        ...emailRecipients.map(s => s.id),
        ...whatsappRecipients.map(s => s.id),
        ...smsRecipients.map(s => s.id)
      ]);

      const result = {
        email: emailRecipients.length,
        whatsapp: whatsappRecipients.length,
        sms: smsRecipients.length,
        total: allRecipientIds.size
      };

      console.log('✅ [calculateChannelRecipients] Channel counts:', result);
      console.log('📧 [calculateChannelRecipients] Email recipients:', emailRecipients.map(s => s.email));
      console.log('💬 [calculateChannelRecipients] WhatsApp recipients:', whatsappRecipients.map(s => s.email));
      console.log('📱 [calculateChannelRecipients] SMS recipients:', smsRecipients.map(s => s.email));

      return result;
    } catch (error) {
      console.error('❌ [calculateChannelRecipients] Error:', error);
      return { email: 0, whatsapp: 0, sms: 0, total: 0 };
    }
  }, [availableTemplates]);

  // Auto-calculate recipients when template and data are available
  useEffect(() => {
    if (formData.template_id && availableTemplates.length > 0) {
      console.log('🔄 [useEffect] Auto-calculating recipients for template:', formData.template_id);
      console.log('🔄 [useEffect] Current total_recipients:', formData.total_recipients);
      console.log('🔄 [useEffect] isEditMode:', isEditMode);

      // Find the selected template
      const selectedTemplate = availableTemplates.find(t => t.id === formData.template_id);

      // Always set default subscriber selection mode based on template for NEW campaigns
      if (selectedTemplate && !isEditMode) {
        setSubscriberSelectionMode('segment'); // Always default to segment for consistency
      }

      // Only recalculate recipients for NEW campaigns or when explicitly needed
      // In edit mode, preserve the existing recipient counts unless they're 0
      const shouldRecalculate = !isEditMode || 
        (isEditMode && formData.total_recipients === 0 && formData.email_recipients_count === 0);
      
      if (shouldRecalculate) {
        console.log('🔄 [useEffect] Triggering recipient calculation...');
        Promise.all([
          calculateTotalRecipients(formData.template_id),
          calculateChannelRecipients(formData.template_id)
        ]).then(([recipientCount, channelCounts]) => {
          console.log('✅ [useEffect] Recipients calculated:', recipientCount);
          console.log('✅ [useEffect] Channel counts:', channelCounts);
          if (recipientCount >= 0) { // Allow 0 as a valid count
            setFormData(prev => ({
              ...prev,
              total_recipients: recipientCount,
              email_recipients_count: channelCounts.email,
              whatsapp_recipients_count: channelCounts.whatsapp,
              sms_recipients_count: channelCounts.sms
            }));
          }
        }).catch(error => {
          console.error('❌ [useEffect] Error calculating recipients:', error);
        });
      } else {
        console.log('📋 [useEffect] Skipping recipient calculation for edit mode (preserving existing counts)');
      }
    }
  }, [formData.template_id, availableTemplates, calculateTotalRecipients, isEditMode]);


  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    if (isFormDisabled) return;
    const { name, value, type } = e.target;
    let newFormDataState = { ...formData };

    if (name === "owner_user_id_toggle") {
        const checked = (e.target as HTMLInputElement).checked;
        const newOwnerUserId = checked && currentUser ? currentUser.user_id : undefined;
        newFormDataState = {
            ...newFormDataState,
            owner_user_id: newOwnerUserId,
        };
        if (newOwnerUserId && currentUser) { 
            newFormDataState.sender_name = currentUser.default_sender_name || '';
            newFormDataState.sender_email = currentUser.default_sender_email || '';
            newFormDataState.sender_phone = currentUser.default_sender_phone || '';
        }
    } else if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      if (name === "is_admin_only") {
            newFormDataState = {
                ...newFormDataState,
                is_admin_only: checked,
                owner_user_id: !checked ? undefined : newFormDataState.owner_user_id, 
            };
        } else { 
            newFormDataState = { ...newFormDataState, [name]: checked };
        }
    } else if (name === "birthday_send_offset_days" || name === "total_recipients") {
        newFormDataState = { ...newFormDataState, [name]: value === '' ? 0 : parseInt(value, 10)}; 
    }
    else if (name.startsWith('cspv_')) { 
        const placeholderKey = name.substring(5); 
        newFormDataState = {
            ...newFormDataState,
            campaign_specific_placeholder_values: {
                ...(newFormDataState.campaign_specific_placeholder_values || {}),
                [placeholderKey]: value,
            }
        };
    } else {
      newFormDataState = { ...newFormDataState, [name]: value };
    }

    // Handle template selection - auto-calculate recipients and update campaign content
    if (name === "template_id" && value) {
      const selectedTemplate = availableTemplates.find(t => t.id === value);
      if (selectedTemplate) {
        // Auto-populate campaign content from template (preserve existing content in edit mode)
        // For email, check if ANY email content exists (either plain text or HTML)
        const hasExistingEmailContent = isEditMode && (
          (newFormDataState.email_content && newFormDataState.email_content.trim() !== '') ||
          (newFormDataState.email_html_content && newFormDataState.email_html_content.trim() !== '')
        );

        newFormDataState = {
          ...newFormDataState,
          subject: isEditMode && newFormDataState.subject && newFormDataState.subject.trim() !== '' ? newFormDataState.subject : selectedTemplate.subject_template,
          email_content: hasExistingEmailContent ? newFormDataState.email_content : (selectedTemplate.email_content_template || ''),
          email_html_content: hasExistingEmailContent ? newFormDataState.email_html_content : (selectedTemplate.email_html_template || ''),
          whatsapp_content: isEditMode && newFormDataState.whatsapp_content && newFormDataState.whatsapp_content.trim() !== '' ? newFormDataState.whatsapp_content : (selectedTemplate.whatsapp_content_template || ''),
          sms_content: isEditMode && newFormDataState.sms_content && newFormDataState.sms_content.trim() !== '' ? newFormDataState.sms_content : (selectedTemplate.sms_content_template || ''),
          campaign_type: selectedTemplate.campaign_type,
          uses_placeholders: selectedTemplate.uses_placeholders,
          available_placeholders: selectedTemplate.available_placeholders || [],
          email_signature_id: selectedTemplate.email_signature_id || '',
          whatsapp_signature_id: selectedTemplate.whatsapp_signature_id || '',
          sms_signature_id: selectedTemplate.sms_signature_id || '',
          // Preserve scheduled date in edit mode
          scheduled_date_input: isEditMode ? newFormDataState.scheduled_date_input : (newFormDataState.scheduled_date_input || ''),
          // Preserve status in edit mode
          status: isEditMode ? newFormDataState.status : (newFormDataState.status || CampaignStatus.DRAFT),
        };

        // ✅ Copy rich text mode settings from template (defaulting to rich text)
        const newEmailRichTextMode = selectedTemplate.email_is_rich_text ?? true;
        const newWhatsAppRichTextMode = selectedTemplate.whatsapp_is_rich_text ?? true;
        const newSMSRichTextMode = selectedTemplate.sms_is_rich_text ?? true;

        // Handle content conversion when rich text mode changes
        if (newEmailRichTextMode !== isEmailRichTextMode) {
          console.log('🔄 Email rich text mode changing from', isEmailRichTextMode, 'to', newEmailRichTextMode);
          if (newEmailRichTextMode && newFormDataState.email_content && !newFormDataState.email_html_content?.trim()) {
            // Converting from plain text to rich text
            const htmlContent = newFormDataState.email_content.split('\n').map(line => `<p>${line || '<br>'}</p>`).join('');
            newFormDataState.email_html_content = htmlContent;
            console.log('✅ Converted email plain text to HTML');
          } else if (!newEmailRichTextMode && newFormDataState.email_html_content && !newFormDataState.email_content?.trim()) {
            // Converting from rich text to plain text
            const tmp = document.createElement('div');
            tmp.innerHTML = newFormDataState.email_html_content;
            const plainContent = tmp.textContent || tmp.innerText || '';
            newFormDataState.email_content = plainContent;
            console.log('✅ Converted email HTML to plain text');
          }
        }

        if (newWhatsAppRichTextMode !== isWhatsAppRichTextMode) {
          console.log('🔄 WhatsApp rich text mode changing from', isWhatsAppRichTextMode, 'to', newWhatsAppRichTextMode);
          // WhatsApp content conversion logic can be added here if needed
        }

        if (newSMSRichTextMode !== isSMSRichTextMode) {
          console.log('🔄 SMS rich text mode changing from', isSMSRichTextMode, 'to', newSMSRichTextMode);
          // SMS content conversion logic can be added here if needed
        }

        setIsEmailRichTextMode(newEmailRichTextMode);
        setIsWhatsAppRichTextMode(newWhatsAppRichTextMode);
        setIsSMSRichTextMode(newSMSRichTextMode);

        // ✅ Copy channel toggle settings from template (defaulting to enabled) - only for NEW campaigns
        if (!isEditMode) {
          setEmailEnabled(selectedTemplate.email_enabled ?? true);
          setWhatsappEnabled(selectedTemplate.whatsapp_enabled ?? true);
          setSmsEnabled(selectedTemplate.sms_enabled ?? true);
        }

        // ✅ Set recipient selection to template segment by default
        if (!isEditMode && selectedTemplate.interest_area_id) {
          setSubscriberSelectionMode('segment');
        } else if (!isEditMode) {
          // Set default based on template's area targeting
          setSubscriberSelectionMode(selectedTemplate.interest_area_id ? 'segment' : 'segment');
        }

        console.log('✅ Content populated - WhatsApp:', newFormDataState.whatsapp_content?.substring(0, 50) + '...');
        console.log('✅ Content populated - SMS:', newFormDataState.sms_content?.substring(0, 50) + '...');
        console.log('✅ Rich text modes set - Email:', selectedTemplate.email_is_rich_text ?? true, 'WhatsApp:', selectedTemplate.whatsapp_is_rich_text ?? false, 'SMS:', selectedTemplate.sms_is_rich_text ?? false);
        console.log('✅ Channel toggles set - Email:', selectedTemplate.email_enabled ?? true, 'WhatsApp:', selectedTemplate.whatsapp_enabled ?? true, 'SMS:', selectedTemplate.sms_enabled ?? true);
        console.log('✅ Recipient mode set to:', selectedTemplate.interest_area_id ? 'segment' : 'all');

        // Auto-calculate total recipients and channel-specific counts based on template
        console.log('🔄 [handleChange] Template changed, calculating recipients...');
        Promise.all([
          calculateTotalRecipients(value),
          calculateChannelRecipients(value)
        ]).then(([recipientCount, channelCounts]) => {
          console.log('✅ [handleChange] Recipients calculated:', recipientCount);
          console.log('✅ [handleChange] Channel counts:', channelCounts);
          setFormData(prev => ({
            ...prev,
            total_recipients: recipientCount,
            email_recipients_count: channelCounts.email,
            whatsapp_recipients_count: channelCounts.whatsapp,
            sms_recipients_count: channelCounts.sms
          }));
        }).catch(error => {
          console.error('❌ [handleChange] Error calculating recipients:', error);
        });
      }
    }

    if (name === "scheduled_date_input") {
        const selectedTemplate = availableTemplates.find(t => t.id === newFormDataState.template_id);
        const systemPlaceholderUpdates = autoPopulateSystemPlaceholders(newFormDataState, selectedTemplate);
        newFormDataState = { ...newFormDataState, ...systemPlaceholderUpdates };
    }
    
    setFormData(newFormDataState);
  };

  const handleScheduledDateInputBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const currentValue = e.target.value.trim();
    if (isFormDisabled || isBirthdayCampaign || !currentValue) return;

    if (parseIndianDateTimeStringToDate(currentValue)) {
        const selectedTemplate = availableTemplates.find(t => t.id === formData.template_id);
        const systemPlaceholderUpdates = autoPopulateSystemPlaceholders(formData, selectedTemplate);
        if (Object.keys(systemPlaceholderUpdates).length > 0) {
             setFormData(prev => ({...prev, ...systemPlaceholderUpdates}));
        }
        return; 
    }

    const datePartOnly = currentValue.split(' ')[0];
    if (isValidIndianDateInput(datePartOnly)) {
        const newScheduledDateInput = `${datePartOnly} 09:00`;
        
        setFormData(prev => {
            let updatedFormData = { ...prev, scheduled_date_input: newScheduledDateInput };
            const selectedTemplate = availableTemplates.find(t => t.id === updatedFormData.template_id);
            const systemPlaceholderUpdates = autoPopulateSystemPlaceholders(updatedFormData, selectedTemplate);
            return { ...updatedFormData, ...systemPlaceholderUpdates };
        });
    }
  };

  const handleTemplateChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    if (isFormDisabled) return;
    const selectedTemplateId = e.target.value;
    const selectedTemplate = availableTemplates.find(t => t.id === selectedTemplateId);
    
    const isNewTemplateBirthdayType = selectedTemplate?.campaign_type === CampaignType.BIRTHDAY_WISH;
    setIsBirthdayCampaign(isNewTemplateBirthdayType);

    setFormData(prev => {
        let updatedFormData: CampaignFormData = { ...prev };
        let newCampaignSpecificValues: Record<string, string> = {};

        if (selectedTemplate) {
            const templatePlaceholders = selectedTemplate.available_placeholders || [];
            const campaignSpecificFromTemplate = templatePlaceholders.filter(
                ph => !SYSTEM_PLACEHOLDERS_TO_EXCLUDE_FROM_CAMPAIGN_INPUTS.includes(ph)
            );

            campaignSpecificFromTemplate.forEach(ph => {
                newCampaignSpecificValues[ph] = prev.campaign_specific_placeholder_values?.[ph] || '';
            });

            updatedFormData = {
                ...prev,
                template_id: selectedTemplateId,
                // Only auto-populate name if it's empty (for new campaigns)
                name: prev.name && prev.name.trim() === '' ? selectedTemplate.display_name : prev.name,
                // In edit mode, preserve existing content unless it's empty
                // For email, check if ANY email content exists (either plain text or HTML)
                subject: isEditMode && prev.subject && prev.subject.trim() !== '' ? prev.subject : selectedTemplate.subject_template,

                // Email content preservation logic (handle dual fields)
                email_content: (() => {
                    const hasExistingEmailContent = isEditMode && (
                        (prev.email_content && prev.email_content.trim() !== '') ||
                        (prev.email_html_content && prev.email_html_content.trim() !== '')
                    );
                    return hasExistingEmailContent ? prev.email_content : (selectedTemplate.email_content_template || '');
                })(),
                email_html_content: (() => {
                    const hasExistingEmailContent = isEditMode && (
                        (prev.email_content && prev.email_content.trim() !== '') ||
                        (prev.email_html_content && prev.email_html_content.trim() !== '')
                    );
                    return hasExistingEmailContent ? prev.email_html_content : (selectedTemplate.email_html_template || '');
                })(),

                whatsapp_content: isEditMode && prev.whatsapp_content && prev.whatsapp_content.trim() !== '' ? prev.whatsapp_content : (selectedTemplate.whatsapp_content_template || ''),
                sms_content: isEditMode && prev.sms_content && prev.sms_content.trim() !== '' ? prev.sms_content : (selectedTemplate.sms_content_template || ''),

                campaign_type: selectedTemplate.campaign_type,
                uses_placeholders: selectedTemplate.uses_placeholders,
                available_placeholders: selectedTemplate.available_placeholders || [],
                campaign_specific_placeholder_values: newCampaignSpecificValues,
                email_signature_id: selectedTemplate.email_signature_id || '',
                whatsapp_signature_id: selectedTemplate.whatsapp_signature_id || '',
                sms_signature_id: selectedTemplate.sms_signature_id || '',
                
                // In edit mode, preserve existing sender info unless it's empty
                sender_name: isEditMode && prev.sender_name && prev.sender_name.trim() !== '' ? prev.sender_name : (selectedTemplate.sender_name || ''),
                sender_email: isEditMode && prev.sender_email && prev.sender_email.trim() !== '' ? prev.sender_email : (selectedTemplate.sender_email || ''),
                sender_phone: isEditMode && prev.sender_phone && prev.sender_phone.trim() !== '' ? prev.sender_phone : (selectedTemplate.sender_phone || ''),

                processed_recipients_count: 0, 
                last_batch_sent_date: undefined,
                sent_in_current_hour_count: 0,
                current_hour_window_start_date: undefined,
                sent_in_current_day_count: 0,
                current_day_window_start_date: undefined,
                next_batch_eligible_at: undefined,
                
                birthday_send_offset_days: isNewTemplateBirthdayType ? (prev.birthday_send_offset_days ?? 0) : undefined,
                birthday_send_time: isNewTemplateBirthdayType ? (prev.birthday_send_time ?? '09:00') : undefined,
                holiday_handling_rule: isNewTemplateBirthdayType ? (prev.holiday_handling_rule ?? HolidayHandlingRule.SEND_ON_HOLIDAY) : undefined,
                
                is_admin_only: !!selectedTemplate.is_admin_only, 
                owner_user_id: selectedTemplate.owner_user_id || undefined,
            };
            
            // ✅ Copy rich text mode settings from template (defaulting to rich text)
            const newEmailRichTextMode = selectedTemplate.email_is_rich_text ?? true;
            const newWhatsAppRichTextMode = selectedTemplate.whatsapp_is_rich_text ?? true;
            const newSMSRichTextMode = selectedTemplate.sms_is_rich_text ?? true;

            // Handle content conversion when rich text mode changes
            if (newEmailRichTextMode !== isEmailRichTextMode) {
              console.log('🔄 [Template Change] Email rich text mode changing from', isEmailRichTextMode, 'to', newEmailRichTextMode);
              if (newEmailRichTextMode && updatedFormData.email_content && !updatedFormData.email_html_content?.trim()) {
                // Converting from plain text to rich text
                const htmlContent = updatedFormData.email_content.split('\n').map(line => `<p>${line || '<br>'}</p>`).join('');
                updatedFormData.email_html_content = htmlContent;
                console.log('✅ [Template Change] Converted email plain text to HTML');
              } else if (!newEmailRichTextMode && updatedFormData.email_html_content && !updatedFormData.email_content?.trim()) {
                // Converting from rich text to plain text
                const tmp = document.createElement('div');
                tmp.innerHTML = updatedFormData.email_html_content;
                const plainContent = tmp.textContent || tmp.innerText || '';
                updatedFormData.email_content = plainContent;
                console.log('✅ [Template Change] Converted email HTML to plain text');
              }
            }

            if (newWhatsAppRichTextMode !== isWhatsAppRichTextMode) {
              console.log('🔄 [Template Change] WhatsApp rich text mode changing from', isWhatsAppRichTextMode, 'to', newWhatsAppRichTextMode);
              // WhatsApp content conversion logic can be added here if needed
            }

            if (newSMSRichTextMode !== isSMSRichTextMode) {
              console.log('🔄 [Template Change] SMS rich text mode changing from', isSMSRichTextMode, 'to', newSMSRichTextMode);
              // SMS content conversion logic can be added here if needed
            }

            setIsEmailRichTextMode(newEmailRichTextMode);
            setIsWhatsAppRichTextMode(newWhatsAppRichTextMode);
            setIsSMSRichTextMode(newSMSRichTextMode);
            
            if (updatedFormData.owner_user_id && currentUser && updatedFormData.owner_user_id === currentUser.user_id) {
                updatedFormData.sender_name = currentUser.default_sender_name || '';
                updatedFormData.sender_email = currentUser.default_sender_email || '';
                updatedFormData.sender_phone = currentUser.default_sender_phone || '';
            }
            
            // Only auto-set scheduled dates for NEW campaigns, not when editing existing ones
            if (!isEditMode) {
                if (selectedTemplate.is_recurring && !isNewTemplateBirthdayType) {
                    const templateNextRecurrenceDate = calculateNextRecurrenceDate(selectedTemplate);
                    if (templateNextRecurrenceDate) {
                        let campaignScheduledDate = new Date(templateNextRecurrenceDate);
                        campaignScheduledDate.setHours(9, 0, 0, 0);
                        const now = new Date();
                        if (campaignScheduledDate < now) {
                            // If scheduled date is in the past, set to current time for immediate sending
                            campaignScheduledDate = new Date();
                        }
                        updatedFormData.scheduled_date_input = formatDateForInput(campaignScheduledDate);
                        updatedFormData.status = CampaignStatus.SCHEDULED;
                    } else {
                        updatedFormData.scheduled_date_input = '';
                        updatedFormData.status = CampaignStatus.DRAFT;
                    }
                } else if (isNewTemplateBirthdayType) {
                    updatedFormData.scheduled_date_input = '';
                    updatedFormData.status = CampaignStatus.SCHEDULED;
                } else {
                    // For non-recurring templates, keep existing scheduled date
                    updatedFormData.scheduled_date_input = prev.scheduled_date_input || '';
                    if (!updatedFormData.scheduled_date_input && updatedFormData.status === CampaignStatus.SCHEDULED) {
                       updatedFormData.status = CampaignStatus.DRAFT;
                    }
                }
            } else {
                // In edit mode, always preserve the existing scheduled_date_input and status
                updatedFormData.scheduled_date_input = prev.scheduled_date_input;
                updatedFormData.status = prev.status;
            }
            const systemPlaceholderUpdates = autoPopulateSystemPlaceholders(updatedFormData, selectedTemplate);
            updatedFormData = { ...updatedFormData, ...systemPlaceholderUpdates };

        } else { // No template selected or invalid template
            updatedFormData = {
                ...prev,
                template_id: '',
                // Reset fields that depend on template
                subject: '', email_content: '', email_html_content: '', whatsapp_content: '', sms_content: '',
                campaign_type: CampaignType.NEWSLETTER, // Default type
                uses_placeholders: false, available_placeholders: [], campaign_specific_placeholder_values: {},
                email_signature_id: '', whatsapp_signature_id: '', sms_signature_id: '',
                sender_name: '', sender_email: '', sender_phone: '',
                processed_recipients_count: 0, last_batch_sent_date: undefined,
                sent_in_current_hour_count: 0, current_hour_window_start_date: undefined,
                sent_in_current_day_count: 0, current_day_window_start_date: undefined,
                next_batch_eligible_at: undefined,
                birthday_send_offset_days: undefined, birthday_send_time: undefined, holiday_handling_rule: undefined,
                is_admin_only: false, owner_user_id: undefined,
            };
            if (!updatedFormData.scheduled_date_input && updatedFormData.status === CampaignStatus.SCHEDULED) {
                updatedFormData.status = CampaignStatus.DRAFT;
            }
        }
        return updatedFormData;
    });
  };

  // Handle subscriber selection for all campaigns
  const handleSubscriberSelectionChange = (subscriberIds: string[]) => {
    setSelectedSubscriberIds(subscriberIds);
    setFormData(prev => ({
      ...prev,
      selected_subscriber_ids: subscriberIds,
      // Update total recipients only when manual selection is active
      total_recipients: subscriberSelectionMode === 'manual' ? subscriberIds.length : prev.total_recipients
    }));
  };

  const handleSubscriberSelectionModeChange = async (mode: 'all' | 'segment' | 'manual') => {
    console.log('🔄 Selection mode change:', mode, 'Current recipients:', formData.total_recipients);
    
    setSubscriberSelectionMode(mode);
    setFormData(prev => ({
      ...prev,
      subscriber_selection_mode: mode
    }));

    // Recalculate total recipients based on selection mode
    if (mode === 'manual') {
      // For manual mode, use selected subscribers count
      console.log('📊 Manual mode: Setting recipients to', selectedSubscriberIds.length);
      setFormData(prev => ({
        ...prev,
        total_recipients: selectedSubscriberIds.length
      }));
    } else if (mode === 'all') {
      // For 'all' mode, get count of all active subscribers
      console.log('📊 All mode: Fetching all active subscribers...');
      try {
        const allSubscribers = await subscriberService.getAllSubscribers();
        const activeSubscribers = allSubscribers.filter(s => s.status === SubscriberProfileStatus.ACTIVE);
        console.log('📊 Found', activeSubscribers.length, 'active subscribers');
        setFormData(prev => ({
          ...prev,
          total_recipients: activeSubscribers.length
        }));
      } catch (error) {
        console.error('Error getting all subscribers count:', error);
      }
    } else if (mode === 'segment') {
      // For 'segment' mode, recalculate based on template
      console.log('📊 Segment mode: Calculating based on template', formData.template_id);
      if (formData.template_id) {
        const [recipientCount, channelCounts] = await Promise.all([
          calculateTotalRecipients(formData.template_id),
          calculateChannelRecipients(formData.template_id)
        ]);
        console.log('📊 Template-based recipients:', recipientCount);
        console.log('📊 Template-based channel counts:', channelCounts);
        setFormData(prev => ({
          ...prev,
          total_recipients: recipientCount,
          email_recipients_count: channelCounts.email,
          whatsapp_recipients_count: channelCounts.whatsapp,
          sms_recipients_count: channelCounts.sms
        }));
      }
    }
  };

  // Manual recipient calculation for debugging
  const handleManualRecalculation = async () => {
    if (!formData.template_id) {
      console.log('⚠️ No template selected for manual recalculation');
      return;
    }

    console.log('🔄 [Manual] Recalculating recipients for template:', formData.template_id);
    try {
      const [recipientCount, channelCounts] = await Promise.all([
        calculateTotalRecipients(formData.template_id),
        calculateChannelRecipients(formData.template_id)
      ]);
      console.log('✅ [Manual] Manual calculation result:', recipientCount);
      console.log('✅ [Manual] Manual channel counts:', channelCounts);
      setFormData(prev => ({
        ...prev,
        total_recipients: recipientCount,
        email_recipients_count: channelCounts.email,
        whatsapp_recipients_count: channelCounts.whatsapp,
        sms_recipients_count: channelCounts.sms
      }));
    } catch (error) {
      console.error('❌ [Manual] Manual calculation failed:', error);
    }
  };



  // Helper function to prepare campaign data
  const prepareCampaignData = async (overrideStatus?: CampaignStatus) => {
    console.log('📋 Validating required fields...');
    console.log('🔍 [prepareCampaignData] Full formData.scheduled_date_input:', JSON.stringify(formData.scheduled_date_input));
    console.log('🔍 [prepareCampaignData] Type of scheduled_date_input:', typeof formData.scheduled_date_input);
    console.log('🔍 [prepareCampaignData] Length of scheduled_date_input:', formData.scheduled_date_input?.length);
    console.log('🔍 [prepareCampaignData] Full formData object:', JSON.stringify(formData, null, 2));

    // Note: Field validation is now handled in handleSubmit() before calling this function
    // This function assumes all required fields have been validated

    // Validate channel selection
    if (!emailEnabled && !whatsappEnabled && !smsEnabled) {
      setFeedbackMessage({ type: 'error', message: 'At least one sending channel (Email, WhatsApp, or SMS) must be enabled.' });
      return null;
    }

    const selectedTemplate = availableTemplates.find(t => t.id === formData.template_id);

    // Ensure we have a current user for created_by field
    if (!currentUser?.user_id) {
        console.log('❌ No current user available for created_by field');
        setFeedbackMessage({ type: 'error', message: 'User authentication required. Please log in again.' });
        return null;
    }

    // Parse scheduled date first to determine status
    let parsedScheduledDate: Date | null = null;
    console.log('🔍 [prepareCampaignData] scheduled_date_input value:', formData.scheduled_date_input);
    if (formData.scheduled_date_input && !isBirthdayCampaign) {
        parsedScheduledDate = parseIndianDateTimeStringToDate(formData.scheduled_date_input);
        console.log('🔍 [prepareCampaignData] parsed date:', parsedScheduledDate);
        if (!parsedScheduledDate) {
            setFeedbackMessage({ type: 'error', message: 'Invalid scheduled date format. Please use DD/MM/YYYY HH:MM format.' });
            return null;
        }
    } else {
        console.log('🔍 [prepareCampaignData] No scheduled_date_input or is birthday campaign');
    }

    // Automatically determine status based on scheduled date and override
    let determinedStatus: CampaignStatus;
    if (overrideStatus) {
        // Use override status (for Send Now, etc.)
        determinedStatus = overrideStatus;
    } else if (parsedScheduledDate) {
        // Has scheduled date → scheduled
        determinedStatus = CampaignStatus.SCHEDULED;
    } else {
        // No scheduled date → draft
        determinedStatus = CampaignStatus.DRAFT;
    }

    // Update system placeholders based on campaign type
    // For "Send Now" campaigns (status === 'sending'), use current date for placeholders
    const isSendNow = determinedStatus === 'sending';
    const updatedFormData = { ...formData };

    if (isSendNow) {
        console.log('🔄 Updating system placeholders for Send Now campaign using current date...');
        const systemPlaceholderUpdates = autoPopulateSystemPlaceholders(formData, selectedTemplate, true);
        if (systemPlaceholderUpdates.campaign_specific_placeholder_values) {
            updatedFormData.campaign_specific_placeholder_values = systemPlaceholderUpdates.campaign_specific_placeholder_values;
            console.log('✅ Updated placeholders for Send Now:', systemPlaceholderUpdates.campaign_specific_placeholder_values);
        }
    }

    // Since campaign type selection was removed, all campaigns are template-based
    const isAdHocCampaign = false;

    // Create campaign data with essential fields
    const campaignDataForService = {
      // Core required fields
      name: formData.name.trim(),
      subject: formData.subject.trim(),
      status: determinedStatus,
      campaign_type: 'newsletter', // Use 'newsletter' for template-based campaigns

      // Content fields
      email_content: formData.email_content || formData.email_html_content || '',
      sender_name: formData.sender_name?.trim() || '',
      sender_email: formData.sender_email?.trim() || '',

      whatsapp_content: formData.whatsapp_content || '',
      sms_content: formData.sms_content || '',


      // Scheduling fields
      scheduled_date: parsedScheduledDate ? parsedScheduledDate.toISOString() : null,

      // User fields
      created_by: currentUser?.user_id || currentUser?.id || 'system',

      // Template-based campaign fields
      template_id: formData.template_id,
      subscriber_selection_mode: subscriberSelectionMode,
      is_ad_hoc_campaign: false,

      // Recipients count (IMPORTANT: Include this!)
      total_recipients: formData.total_recipients || 0,

      // Channel-specific recipient counts (calculated based on actual subscriber capabilities)
      email_recipients_count: emailEnabled ? (formData.email_recipients_count || 0) : 0,
      whatsapp_recipients_count: whatsappEnabled ? (formData.whatsapp_recipients_count || 0) : 0,
      sms_recipients_count: smsEnabled ? (formData.sms_recipients_count || 0) : 0,

      // Channel enabled flags
      email_enabled: emailEnabled,
      whatsapp_enabled: whatsappEnabled,
      sms_enabled: smsEnabled,

      // Access control fields (required for permission checks)
      is_admin_only: formData.is_admin_only || false,
      owner_user_id: formData.owner_user_id || undefined,

      // Campaign notes
      remarks: formData.remarks || ''
    };

    console.log('🔍 [prepareCampaignData] Including total_recipients in campaign data:', formData.total_recipients);
    console.log('🔍 [prepareCampaignData] Channel counts - Email:', emailEnabled ? (formData.email_recipients_count || 0) : 0, 'WhatsApp:', whatsappEnabled ? (formData.whatsapp_recipients_count || 0) : 0, 'SMS:', smsEnabled ? (formData.sms_recipients_count || 0) : 0);
    console.log('🔍 [prepareCampaignData] Channel enabled flags - Email:', emailEnabled, 'WhatsApp:', whatsappEnabled, 'SMS:', smsEnabled);
    console.log('🔍 [prepareCampaignData] Actual channel counts being sent to backend:', {
      email_recipients_count: campaignDataForService.email_recipients_count,
      whatsapp_recipients_count: campaignDataForService.whatsapp_recipients_count,
      sms_recipients_count: campaignDataForService.sms_recipients_count
    });

    return campaignDataForService;
  };

  const validateCampaignForm = (): boolean => {
    setFeedbackMessage(null);
    setValidationErrors({});

    const currentErrors: Record<string, string> = {};
    let firstErrorFieldId = '';

    // Check basic required fields
    const requiredFieldsToCheckCampaign = {
      name: "Campaign Name",
      subject: "Subject Line",
      sender_name: "Sender Name",
      sender_email: "Sender Email",
    } as const;
    type RequiredCampaignFieldKey = keyof typeof requiredFieldsToCheckCampaign;

    for (const field of Object.keys(requiredFieldsToCheckCampaign) as RequiredCampaignFieldKey[]) {
        const valueToCheck = formData[field];
        if (typeof valueToCheck === 'string' && !valueToCheck.trim()) {
             currentErrors[field] = `${requiredFieldsToCheckCampaign[field]} is required.`;
            if(!firstErrorFieldId) firstErrorFieldId = field;
        } else if (typeof valueToCheck !== 'string' && !valueToCheck) {
            currentErrors[field] = `${requiredFieldsToCheckCampaign[field]} is required.`;
            if(!firstErrorFieldId) firstErrorFieldId = field;
        }
    }

    // Check email content separately (can be in email_content or email_html_content)
    const hasEmailContent = (formData.email_content && formData.email_content.trim()) ||
                           (formData.email_html_content && formData.email_html_content.trim());
    if (!hasEmailContent) {
        currentErrors.email_content = "Email Content is required.";
        if(!firstErrorFieldId) firstErrorFieldId = 'email_content';
    }

    // Template is required for all campaigns since campaign type selection was removed
    if (!formData.template_id) {
        currentErrors.template_id = "Base Template is required.";
        if(!firstErrorFieldId) firstErrorFieldId = 'template_id';
    }

    if (Object.keys(currentErrors).length > 0) {
        setValidationErrors(currentErrors);
        setFeedbackMessage({ type: 'error', message: `Please correct the highlighted errors.` });

        // Focus and scroll to first error
        if (firstErrorFieldId) {
            setTimeout(() => {
                const errorElement = document.getElementById(firstErrorFieldId);
                if (errorElement) {
                    errorElement.focus();
                    errorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }, 100);
        }
        return false;
    }

    return true;
  };



  const handleSendNow = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('🚀 Send Now clicked - creating and sending campaign immediately...');

    if (isFormDisabled) {
      console.log('❌ Form is disabled');
      setFeedbackMessage({type: 'error', message: 'Form is disabled due to insufficient permissions.'});
      return;
    }

    // Validate form before proceeding
    if (!validateCampaignForm()) {
      return;
    }

    // Create campaign with 'sending' status and immediate execution
    const campaignData = await prepareCampaignData('sending');
    if (!campaignData) return;

    try {
      console.log('📧 Creating campaign for immediate sending...');
      
      // Use the WhatsApp automation API endpoint directly
      const sendResponse = await fetch('/api/whatsapp-automation/session/start', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          campaignId: campaignData.name, // Using name as temporary ID
          messageCount: campaignData.whatsapp_recipients_count || 0,
          estimatedDuration: (campaignData.whatsapp_recipients_count || 0) * 3000 // 3 sec per message
        })
      });

      if (!sendResponse.ok) {
        throw new Error('Failed to start WhatsApp automation session');
      }

      const session = await sendResponse.json();
      console.log('✅ WhatsApp automation session started:', session.sessionId);

      // Navigate to sending logs page to show progress
      navigate('/campaigns/sending-logs');

      // Clear any existing feedback message since page will show status
      setFeedbackMessage(null);
    } catch (error) {
      console.error('❌ Error in WhatsApp automation:', error);
      setFeedbackMessage({
        type: 'error',
        message: `Failed to start WhatsApp automation: ${(error as Error).message}`
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    console.log('🚀 Campaign submission started...');

    if (isFormDisabled) {
      console.log('❌ Form is disabled');
      setFeedbackMessage({type: 'error', message: 'Form is disabled due to insufficient permissions.'});
      return;
    }

    // Validate form before proceeding
    if (!validateCampaignForm()) {
      return;
    }

    // Use the helper function to prepare campaign data
    const campaignDataForService = await prepareCampaignData();
    if (!campaignDataForService) return;

    console.log('✅ Campaign data prepared successfully');


    try {
        console.log('📋 Campaign data for service:', JSON.stringify(campaignDataForService, null, 2));
        let savedCampaign: Campaign;
        if (isEditMode && campaignId) {
          console.log('🔄 Updating existing campaign...');
          savedCampaign = await campaignService.updateCampaign(campaignId, campaignDataForService);
          console.log('✅ Campaign updated successfully:', savedCampaign.id);
          setFeedbackMessage({ type: 'success', message: 'Campaign updated successfully.' });
          addAuditLog(AuditActionType.UPDATE, 'Campaign', `Updated campaign: ${savedCampaign.name}`, { 
            entityId: savedCampaign.id, 
            userId: currentUser?.user_id 
          });
        } else {

          try {
            savedCampaign = await campaignService.createCampaign(campaignDataForService);

            // Check if the API response is what we expect
            if (!savedCampaign || !savedCampaign.id) {
              throw new Error('Invalid campaign response: missing ID');
            }

            // Show success modal immediately for better user experience
            setCreatedCampaignInfo({
              name: campaignDataForService.name,
              status: campaignDataForService.status
            });
            setShowSuccessModal(true);



            addAuditLog(AuditActionType.CREATE, 'Campaign', `Created campaign: ${savedCampaign.name}`, {
              entityId: savedCampaign.id,
              userId: currentUser?.user_id
            });
          } catch (createError) {
            console.error('❌ Campaign creation failed:', createError);
            console.error('❌ Error details:', {
              message: (createError as Error).message,
              stack: (createError as Error).stack,
              campaignData: campaignDataForService
            });
            throw createError; // Re-throw to be caught by outer catch block
          }
        }
        
        // Navigation after successful save with refresh parameter
        const selectedTemplate = availableTemplates.find(t => t.id === formData.template_id);
        const targetPath = selectedTemplate?.campaign_type === CampaignType.BIRTHDAY_WISH ? '/birthday-automations' : '/campaigns';
        setTimeout(() => {
          navigate(`${targetPath}?refresh=${Date.now()}`);
        }, 2000);
    } catch (error) {
        console.error("❌ Failed to save campaign:", error);
        console.error("❌ Campaign data that failed:", campaignDataForService);
        
        // Extract more detailed error information
        let errorMessage = 'Unknown error occurred';
        if (error instanceof Error) {
          errorMessage = error.message;
          console.error("❌ Error details:", error);
        }
        
        // If it's an Axios error, try to get more details
        if ((error as any)?.response) {
          console.error("❌ Server response:", (error as any).response);
          console.error("❌ Server status:", (error as any).response?.status);
          console.error("❌ Server data:", (error as any).response?.data);
          errorMessage = `Server error (${(error as any).response?.status}): ${(error as any).response?.data?.error || errorMessage}`;
        }
        
        setFeedbackMessage({ 
          type: 'error', 
          message: `Failed to ${isEditMode ? 'update' : 'create'} campaign: ${errorMessage}` 
        });
    }
  };

  const handleSendBatch = async () => { 
    if (isFormDisabled || !campaignId) return;
    setFeedbackMessage(null);
    
    let campaignToUpdate = loadedCampaign;
    if (!campaignToUpdate) {
        const fetchedCampaign = await campaignService.getCampaignById(campaignId);
        if (!fetchedCampaign) {
            setFeedbackMessage({ type: 'error', message: 'Campaign not found. Save the campaign first.'});
            return;
        }
        campaignToUpdate = fetchedCampaign;
    }


    if (!canUserExecuteCampaign(campaignToUpdate, currentUser)){ 
        setFeedbackMessage({type: 'error', message: "Access Denied: You do not have permission to send this campaign."});
        return;
    }

    if (isBirthdayCampaign) {
        setFeedbackMessage({ type: 'error', message: '"Send Batch" is not applicable to Birthday Automations.' });
        return;
    }

    // Check template requirement only for non-ad-hoc campaigns
    const isAdHocCampaign = campaignToUpdate.campaign_type === CampaignType.AD_HOC;
    if (!isAdHocCampaign && !campaignToUpdate.template_id) {
        setFeedbackMessage({ type: 'error', message: 'A Base Template must be selected to send non-ad-hoc campaigns.' });
        return;
    }

    try {
        const result = await campaignService.sendCampaign(campaignId);
        updateCampaignState(result.campaign);
        addAuditLog(AuditActionType.SEND_BATCH, 'Campaign', `Sent campaign: ${result.campaign.name}`, {
          entityId: result.campaign.id,
          userId: currentUser?.user_id,
          metadata: { message: result.message }
        });

        // Navigate to sending logs page instead of showing modal
        navigate(`/campaigns/${campaignId}/sending-logs`);

        // Clear feedback message since page will show status
        setFeedbackMessage(null);
    } catch (error) {
        console.error("Failed to send batch:", error);
        setFeedbackMessage({ type: 'error', message: `Failed to send batch: ${(error as Error).message}` });
    }
  };

  const confirmDelete = async () => { 
    if (!loadedCampaign || !campaignId) return;
    try {
        await campaignService.deleteCampaign(campaignId);
        addAuditLog(AuditActionType.DELETE, 'Campaign', `Deleted campaign: ${loadedCampaign.name}`, { 
          entityId: loadedCampaign.id, 
          userId: currentUser?.user_id 
        });
        setFeedbackMessage({ type: 'success', message: `Campaign "${loadedCampaign.name}" deleted successfully.` });
        setShowDeleteModal(false);
        // Navigation after successful deletion with refresh parameter
        const targetPath = isBirthdayCampaign ? '/birthday-automations' : '/campaigns';
        setTimeout(() => { navigate(`${targetPath}?refresh=${Date.now()}`); }, 1500);
    } catch (error) {
        console.error("Failed to delete campaign:", error);
        setFeedbackMessage({ type: 'error', message: `Failed to delete campaign: ${(error as Error).message}` });
        setShowDeleteModal(false);
    }
  };

  const handleDelete = () => {
    if (isFormDisabled || !isEditMode || !campaignId || !loadedCampaign) return;
    
    if (!canUserEditDeleteItem(loadedCampaign, currentUser)){
        setFeedbackMessage({ type: 'error', message: 'Access Denied or Campaign not found.' });
        return;
    }
    setShowDeleteModal(true);
  };

  const handleEnhancedModalConfirm = (selectedIds: string[]) => {
    setSelectedSubscriberIds(selectedIds);
    setFormData(prev => ({
      ...prev,
      selected_subscriber_ids: selectedIds,
      total_recipients: selectedIds.length
    }));
  };

  const handleCancel = () => {
    const targetPath = isBirthdayCampaign ? '/birthday-automations' : '/campaigns';
    navigate(`${targetPath}?refresh=${Date.now()}`);
  };

  // Floating action bar handlers
  const handleFloatingSave = async () => {
    setIsSaving(true);
    try {
      // Trigger form submission
      const form = document.querySelector('form');
      if (form) {
        const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
        form.dispatchEvent(submitEvent);
      }
    } finally {
      // Reset saving state after a delay to show feedback
      setTimeout(() => setIsSaving(false), 1000);
    }
  };

  const handleFloatingCancel = () => {
    handleCancel();
  };

  const handleFloatingDelete = () => {
    handleDelete();
  };

  const inputClass = "mt-1 block w-full px-3 py-2 bg-surface text-textPrimary border-border rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm disabled:bg-gray-100 disabled:cursor-not-allowed";
  const labelClass = "block text-sm font-medium text-textPrimary";
  const sectionClass = "p-6 bg-surface shadow-md rounded-lg mb-6 border border-border";
  const fieldGroupClass = "grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6";
  const helpTextClass = "mt-1 text-xs text-textSecondary";
  const sectionHeaderClass = "text-lg font-medium leading-6 bg-primary text-white px-4 py-3 -mx-6 -mt-6 mb-6 rounded-t-lg shadow-sm flex items-center";
  const checkboxItemClass = "flex items-center";
  const checkboxInputClass = "h-4 w-4 text-primary border-border rounded focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed";
  const checkboxLabelClass = "ml-2 block text-sm text-textPrimary";
  
  const campaignSpecificPlaceholders = (formData.available_placeholders || []).filter(
    ph => !SYSTEM_PLACEHOLDERS_TO_EXCLUDE_FROM_CAMPAIGN_INPUTS.includes(ph)
  );

  // Calculate eligible subscribers for WhatsApp campaign sending
  const eligibleSubscribers = useMemo(() => {
    if (!subscribers || subscribers.length === 0) {
      return [];
    }

    // Get all active subscribers
    const activeSubscribers = subscribers.filter(s => s.status === SubscriberProfileStatus.ACTIVE);

    // Filter for WhatsApp-eligible subscribers (have phone and allow WhatsApp)
    const whatsappEligible = activeSubscribers.filter(s =>
      s.phone && s.phone.trim() !== '' && s.allowWhatsApp
    );

    // Apply selection mode filtering
    if (subscriberSelectionMode === 'manual') {
      // Return manually selected subscribers that are WhatsApp eligible
      return whatsappEligible.filter(s => selectedSubscriberIds.includes(s.id));
    }

    if (subscriberSelectionMode === 'segment' && formData.template_id) {
      // Return template segment subscribers that are WhatsApp eligible
      const selectedTemplate = availableTemplates.find(t => t.id === formData.template_id);
      if (selectedTemplate?.interest_area_id) {
        return whatsappEligible.filter(s =>
          s.areasOfInterestIds && s.areasOfInterestIds.includes(selectedTemplate.interest_area_id!)
        );
      }
    }

    // For 'all' mode or fallback, return all WhatsApp eligible subscribers
    return whatsappEligible;
  }, [subscribers, subscriberSelectionMode, selectedSubscriberIds, formData.template_id, availableTemplates]);

  const resolvedValues = getResolvedPlaceholderValues();


  return (
    <div className="text-textPrimary">
      <Header 
        title={isEditMode ? `Edit Campaign: ${originalName}` : (isBirthdayCampaign ? "Add New Birthday Automation" : "Add New Standard Campaign")} 
        subtitle={isEditMode ? "Update the details of this campaign." : "Create a new campaign."}
      />

      {feedbackMessage && (
        <div className={`mb-4 p-3 rounded-md shadow-sm text-sm 
          ${feedbackMessage.type === 'success' ? 'bg-green-100 text-green-700 border border-green-300 dark:bg-green-700 dark:text-green-100 dark:border-green-500' : 
            feedbackMessage.type === 'info' ? 'bg-blue-100 text-blue-700 border border-blue-300 dark:bg-blue-700 dark:text-blue-100 dark:border-blue-500' :
            'bg-red-100 text-red-700 border border-red-300 dark:bg-red-700 dark:text-red-100 dark:border-red-500'}`} 
            role="alert">
          {feedbackMessage.message}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-8">
        <fieldset disabled={isFormDisabled}>
        <div className={sectionClass}>
          <h3 className={sectionHeaderClass}>Campaign Setup</h3>
          <div className={fieldGroupClass}>
            <div className="sm:col-span-2">
              <label htmlFor="template_id" className={labelClass}>
                Base Template *
              </label>
              <SearchableTemplateDropdown
                templates={availableTemplates}
                selectedTemplateId={formData.template_id}
                onTemplateSelect={(templateId) => {
                  const syntheticEvent = {
                    target: { name: 'template_id', value: templateId }
                  } as React.ChangeEvent<HTMLSelectElement>;
                  handleTemplateChange(syntheticEvent);
                }}
                placeholder="Search and select a template..."
                disabled={isFormDisabled}
                className={`mt-1 ${validationErrors.template_id ? 'border-red-500' : ''}`}
              />
              {validationErrors.template_id && (
                <p className="mt-1 text-sm text-red-600">{validationErrors.template_id}</p>
              )}
            </div>

            <div className="sm:col-span-2">
              <label htmlFor="name" className={labelClass}>Campaign Name *</label>
              <input
                type="text"
                name="name"
                id="name"
                value={formData.name}
                onChange={handleChange}
                className={`${inputClass} ${validationErrors.name ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}`}
                required
                aria-required="true"
              />
              {validationErrors.name && (
                <p className="mt-1 text-sm text-red-600">{validationErrors.name}</p>
              )}
            </div>

            <div className="sm:col-span-2">
              <label htmlFor="subject" className={labelClass}>Subject Line *</label>
              <input
                type="text"
                name="subject"
                id="subject"
                value={formData.subject}
                onChange={handleChange}
                className={`${inputClass} ${validationErrors.subject ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}`}
                required
                aria-required="true"
              />
              {validationErrors.subject && (
                <p className="mt-1 text-sm text-red-600">{validationErrors.subject}</p>
              )}
            </div>

             {isEditMode && (
                 <div className="sm:col-span-2">
                    <label htmlFor="processed_recipients_count" className={labelClass}>Processed Recipients</label>
                    <input type="number" name="processed_recipients_count" id="processed_recipients_count" value={formData.processed_recipients_count || 0} className={`${inputClass} bg-gray-100 dark:bg-gray-700`} readOnly />
                 </div>
             )}
             {!isEditMode && <div className="sm:col-span-2"></div>}
             {/* Status field removed for new campaigns - always created as draft */}
             {isEditMode && (
               <div className="sm:col-span-2">
                  <label htmlFor="status" className={labelClass}>Status</label>
                  <select name="status" id="status" value={formData.status} onChange={handleChange} className={inputClass} disabled={isBirthdayCampaign && formData.status === CampaignStatus.SCHEDULED}>
                      {Object.values(CampaignStatus)
                          .filter(status => isBirthdayCampaign ? (status === CampaignStatus.SCHEDULED || status === CampaignStatus.DRAFT) : true)
                          .map(statusVal => (
                          <option key={statusVal} value={statusVal}>
                              {isBirthdayCampaign && statusVal === CampaignStatus.SCHEDULED ? 'Active (Automation Running)' :
                               isBirthdayCampaign && statusVal === CampaignStatus.DRAFT ? 'Paused (Automation Off)' :
                               formatEnumValueForDisplay(statusVal)}
                          </option>
                      ))}
                  </select>
                   {isBirthdayCampaign && formData.status === CampaignStatus.SCHEDULED && <p className={helpTextClass}>Birthday Automations are 'Active' when their status is 'Scheduled'. Set to 'Paused' (Draft) to disable.</p>}
              </div>
             )}
          </div>
        </div>
        
        <div className={sectionClass}>
            <h3 className={sectionHeaderClass}>Content & Placeholders</h3>
            <div className={fieldGroupClass}>
                 <div className="sm:col-span-3">
                    <label htmlFor="sender_name" className={labelClass}>Sender Name *</label>
                    <input
                      type="text"
                      name="sender_name"
                      id="sender_name"
                      value={formData.sender_name}
                      onChange={handleChange}
                      className={`${inputClass} ${validationErrors.sender_name ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}`}
                      required
                    />
                    {validationErrors.sender_name && (
                      <p className="mt-1 text-sm text-red-600">{validationErrors.sender_name}</p>
                    )}
                    <p className={helpTextClass}>Prefilled from template. Editable for this campaign.</p>
                </div>
                <div className="sm:col-span-3">
                    <label htmlFor="sender_email" className={labelClass}>Sender Email *</label>
                    <input
                      type="email"
                      name="sender_email"
                      id="sender_email"
                      value={formData.sender_email}
                      onChange={handleChange}
                      className={`${inputClass} ${validationErrors.sender_email ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}`}
                      required
                    />
                    {validationErrors.sender_email && (
                      <p className="mt-1 text-sm text-red-600">{validationErrors.sender_email}</p>
                    )}
                    <p className={helpTextClass}>Prefilled from template. Editable for this campaign.</p>
                </div>
                 <div className="sm:col-span-3">
                    <label htmlFor="sender_phone" className={labelClass}>Sender Phone (Optional)</label>
                    <input type="tel" name="sender_phone" id="sender_phone" value={formData.sender_phone || ''} onChange={handleChange} className={inputClass} />
                    <p className={helpTextClass}>Prefilled from template. Editable for this campaign.</p>
                </div>
                <div className="sm:col-span-3"></div> 


                <div className="sm:col-span-6">
                    <ContextualRichTextEditor
                        context="campaign_email"
                        label="Email Content (Editable for this campaign)"
                        value={formData.email_html_content || ''}
                        plainTextValue={formData.email_content}
                        onChange={(value) => setFormData(prev => ({ ...prev, email_html_content: value }))}
                        onPlainTextChange={(value) => setFormData(prev => ({ ...prev, email_content: value }))}
                        placeholder="Create your email campaign content with placeholders..."
                        height={400}
                        showPlaceholderButtons={true}
                        availablePlaceholders={formData.available_placeholders || []}
                        onModeChange={setIsEmailRichTextMode}
                        required={true}
                    />
                    {validationErrors.email_content && (
                      <p className="mt-1 text-sm text-red-600">{validationErrors.email_content}</p>
                    )}
                </div>
                {(isEmailRichTextMode ? formData.email_html_content : formData.email_content) && (
                    <div className="sm:col-span-6 p-4 border border-border rounded-md bg-background">
                        <h4 className="text-sm font-medium text-textPrimary mb-2">
                            Live {isEmailRichTextMode ? 'HTML' : 'Text'} Preview (Approximate):
                        </h4>
                        {isEmailRichTextMode ? (
                            <div className="prose prose-sm dark:prose-invert max-w-none" 
                                 dangerouslySetInnerHTML={{ __html: renderContentWithPlaceholders(formData.email_html_content, resolvedValues) }} />
                        ) : (
                            <div className="whitespace-pre-wrap text-sm text-gray-700 bg-gray-50 p-3 rounded border">
                                {renderContentWithPlaceholders(formData.email_content, resolvedValues)}
                            </div>
                        )}
                    </div>
                )}
                <div className="sm:col-span-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <ToggleableRichTextEditor
                                label="WhatsApp Content (Editable)"
                                value={formData.whatsapp_content || ''}
                                onChange={(value) => setFormData(prev => ({ ...prev, whatsapp_content: value }))}
                                placeholder="Create WhatsApp message content..."
                                height={150}
                                showPlaceholderButtons={true}
                                availablePlaceholders={formData.available_placeholders || []}
                                defaultRichTextMode={isWhatsAppRichTextMode}
                                showModeToggle={true}
                                onModeChange={setIsWhatsAppRichTextMode}
                            />
                        </div>
                        <div>
                            <ToggleableRichTextEditor
                                label="SMS Content (Editable)"
                                value={formData.sms_content || ''}
                                onChange={(value) => setFormData(prev => ({ ...prev, sms_content: value }))}
                                placeholder="Create SMS message content..."
                                height={150}
                                showPlaceholderButtons={true}
                                availablePlaceholders={formData.available_placeholders || []}
                                defaultRichTextMode={isSMSRichTextMode}
                                showModeToggle={true}
                                onModeChange={setIsSMSRichTextMode}
                            />
                        </div>
                    </div>
                </div>
                {formData.uses_placeholders && campaignSpecificPlaceholders.length > 0 && (
                    <div className="sm:col-span-6 pt-4 border-t border-border">
                        <h4 className="text-md font-medium text-textPrimary mb-2">Campaign-Specific Placeholder Values:</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {campaignSpecificPlaceholders.map(phKey => (
                                <div key={phKey}>
                                    <label htmlFor={`cspv_${phKey}`} className={labelClass}>{`{{${phKey}}}`}</label>
                                    <input 
                                        type="text" 
                                        name={`cspv_${phKey}`} 
                                        id={`cspv_${phKey}`} 
                                        value={formData.campaign_specific_placeholder_values?.[phKey] || ''}
                                        onChange={handleChange}
                                        className={inputClass}
                                        placeholder={`Enter value for ${phKey}`}
                                    />
                                </div>
                            ))}
                        </div>
                    </div>
                )}
                
                {/* Attachments Section */}
                <div className="sm:col-span-6 pt-4 border-t border-border">
                    <h4 className="text-md font-medium text-textPrimary mb-2">📎 Email & WhatsApp Attachments:</h4>
                    <AttachmentManager
                        attachments={formData.attachments || []}
                        onAttachmentsChange={(attachments) => setFormData(prev => ({ ...prev, attachments }))}
                        disabled={isFormDisabled}
                        className="mt-2"
                    />

                    {/* Attachment Sending Mode Selection */}
                    {formData.attachments && formData.attachments.length > 0 && (
                        <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                            <h5 className="text-sm font-medium text-gray-900 mb-3">📧 Email Sending Mode for Attachments</h5>
                            <div className="space-y-3">
                                <div className="flex items-start">
                                    <input
                                        type="radio"
                                        id="individual_emails"
                                        name="attachment_sending_mode"
                                        value="individual"
                                        checked={!formData.use_bcc_for_attachments}
                                        onChange={(e) => setFormData(prev => ({
                                            ...prev,
                                            use_bcc_for_attachments: !e.target.checked
                                        }))}
                                        className="mt-0.5 h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                                        disabled={isFormDisabled}
                                    />
                                    <div className="ml-3">
                                        <label htmlFor="individual_emails" className="text-sm font-medium text-gray-900">
                                            📤 Individual Emails (Recommended)
                                        </label>
                                        <p className="text-xs text-gray-600 mt-1">
                                            Send separate emails to each recipient with personalized content and attachments.
                                            Better for personalization but uses more SMTP resources.
                                        </p>
                                    </div>
                                </div>

                                <div className="flex items-start">
                                    <input
                                        type="radio"
                                        id="bcc_email"
                                        name="attachment_sending_mode"
                                        value="bcc"
                                        checked={formData.use_bcc_for_attachments || false}
                                        onChange={(e) => setFormData(prev => ({
                                            ...prev,
                                            use_bcc_for_attachments: e.target.checked
                                        }))}
                                        className="mt-0.5 h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                                        disabled={isFormDisabled}
                                    />
                                    <div className="ml-3">
                                        <label htmlFor="bcc_email" className="text-sm font-medium text-gray-900">
                                            📮 Single BCC Email (Bulk Mode)
                                        </label>
                                        <p className="text-xs text-gray-600 mt-1">
                                            Send one email with all recipients in BCC. Reduces SMTP load but no personalization.
                                            Good for newsletters or announcements with attachments.
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded">
                                <div className="flex items-start">
                                    <div className="text-yellow-600 mr-2">⚠️</div>
                                    <div className="text-xs text-yellow-800">
                                        <strong>Note:</strong> BCC mode will send a single email to the sender with all recipients in BCC.
                                        Recipients won't see each other's email addresses, but content won't be personalized per recipient.
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    <p className="text-xs text-textSecondary mt-2">
                        Attachments will be included in both email and WhatsApp messages (where supported by the platform).
                    </p>
                </div>
            </div>
        </div>
        
        {/* Subscriber Selection for All Campaigns */}
        <div className={sectionClass}>
            <h3 className={sectionHeaderClass}>📋 Recipient Selection</h3>
            <div className="space-y-4">
                <p className="text-sm text-gray-600">
                    Choose how to select recipients for this campaign. You can use the template's default targeting,
                    customize the list by adding/removing specific subscribers, or create a completely custom selection.
                </p>
                
                {/* Selection Mode Buttons */}
                <div className="flex space-x-4 mb-4">
                    <button
                        type="button"
                        onClick={() => handleSubscriberSelectionModeChange('segment')}
                        className={`px-4 py-2 rounded-md text-sm font-medium ${
                            subscriberSelectionMode === 'segment'
                                ? 'bg-blue-600 text-white'
                                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                        }`}
                    >
                        Template Segment
                    </button>
                    <button
                        type="button"
                        onClick={() => handleSubscriberSelectionModeChange('manual')}
                        className={`px-4 py-2 rounded-md text-sm font-medium ${
                            subscriberSelectionMode === 'manual'
                                ? 'bg-blue-600 text-white'
                                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                        }`}
                    >
                        Custom Selection
                    </button>
                    <button
                        type="button"
                        onClick={() => handleSubscriberSelectionModeChange('all')}
                        className={`px-4 py-2 rounded-md text-sm font-medium ${
                            subscriberSelectionMode === 'all'
                                ? 'bg-blue-600 text-white'
                                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                        }`}
                    >
                        All Subscribers
                    </button>
                </div>

                {/* Template Segment with Modification Option */}
                {subscriberSelectionMode === 'segment' && (
                    <div className="border-2 border-solid border-blue-200 rounded-lg p-6 bg-blue-50">
                        <div className="text-center">
                            <div className="text-lg font-medium text-gray-900 mb-2">
                                Template-Based Recipients
                            </div>
                            <div className="text-sm text-gray-600 mb-4">
                                Based on template's area of interest criteria
                                {selectedSubscriberIds.length > 0 && (
                                    <span className="block mt-1 text-blue-600 font-medium">
                                        {selectedSubscriberIds.length} custom modifications applied
                                    </span>
                                )}
                            </div>
                            <button
                                type="button"
                                onClick={() => setShowEnhancedModal(true)}
                                disabled={isFormDisabled}
                                className="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {selectedSubscriberIds.length > 0 ? 'Modify Recipients' : 'Add/Remove Recipients'}
                            </button>
                        </div>
                    </div>
                )}

                {/* Custom Manual Selection */}
                {subscriberSelectionMode === 'manual' && (
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
                        <div className="text-center">
                            <div className="text-lg font-medium text-gray-900 mb-2">
                                Custom Subscriber Selection
                            </div>
                            <div className="text-sm text-gray-600 mb-4">
                                {selectedSubscriberIds.length > 0
                                    ? `${selectedSubscriberIds.length} subscribers selected`
                                    : 'No subscribers selected'
                                }
                                {formData.template_id && (
                                    <div className="mt-1 text-blue-600">
                                        Template matches will be auto-selected
                                    </div>
                                )}
                            </div>
                            <button
                                type="button"
                                onClick={() => setShowEnhancedModal(true)}
                                disabled={isFormDisabled}
                                className="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {selectedSubscriberIds.length > 0 ? 'Modify Selection' : 'Select Subscribers'}
                            </button>
                        </div>
                    </div>
                )}

                {/* Current Selection Mode Description */}
                <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded-md">
                    {subscriberSelectionMode === 'all' && (
                        <>📊 <strong>All Subscribers:</strong> Campaign will target all active subscribers in the system.</>
                    )}
                    {subscriberSelectionMode === 'segment' && (
                        <>🎯 <strong>Template Segment:</strong> Campaign will target subscribers matching the template's area of interest criteria. You can add or remove specific subscribers from this list.</>
                    )}
                    {subscriberSelectionMode === 'manual' && (
                        <>✋ <strong>Custom Selection:</strong> Campaign will only target the specifically selected subscribers.</>
                    )}
                </div>

                {/* Total Target Recipients - Moved from Campaign Setup */}
                <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-center justify-between">
                        <div className="flex-1">
                            <label htmlFor="total_recipients_display" className="block text-sm font-medium text-gray-700 mb-1">
                                Total Target Recipients
                            </label>
                            <div className="flex items-center space-x-2">
                                <input
                                    type="number"
                                    id="total_recipients_display"
                                    value={formData.total_recipients}
                                    className="block w-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-900 font-semibold text-lg cursor-not-allowed"
                                    min="0"
                                    readOnly
                                    disabled
                                    title="Automatically calculated based on recipient selection"
                                />
                                {import.meta.env.DEV && (
                                    <button
                                        type="button"
                                        onClick={handleManualRecalculation}
                                        className="px-3 py-2 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                                        title="Debug: Manually recalculate recipients"
                                    >
                                        🔄 Recalc
                                    </button>
                                )}
                            </div>
                        </div>
                        <div className="ml-4 text-right">
                            <div className="text-2xl font-bold text-blue-600">
                                {formData.total_recipients}
                            </div>
                            <div className="text-xs text-gray-500">
                                {subscriberSelectionMode === 'manual'
                                    ? 'Custom Selected'
                                    : subscriberSelectionMode === 'segment'
                                        ? selectedSubscriberIds.length > 0 ? 'Template + Custom' : 'Template Match'
                                        : 'All Active'
                                }
                            </div>
                        </div>
                    </div>
                    <p className="text-xs text-gray-600 mt-2">
                        {subscriberSelectionMode === 'manual'
                            ? `Count of manually selected subscribers (${selectedSubscriberIds.length} selected)`
                            : subscriberSelectionMode === 'segment'
                                ? selectedSubscriberIds.length > 0
                                    ? `Template-based with ${selectedSubscriberIds.length} custom modifications`
                                    : 'Auto-calculated based on template\'s area of interest criteria'
                                : 'Auto-calculated based on all active subscribers in the system'
                        }
                    </p>
                    <div className="text-xs text-gray-500 mt-2 p-2 bg-gray-50 rounded border">
                        <strong>Channel Breakdown:</strong>
                        📧 Email: {formData.email_recipients_count || 0} |
                        💬 WhatsApp: {formData.whatsapp_recipients_count || 0} |
                        📱 SMS: {formData.sms_recipients_count || 0}
                    </div>
                </div>
            </div>
        </div>
        
         {currentUser?.role === UserRole.ADMIN && (
            <div className={sectionClass}>
                <h3 className={sectionHeaderClass}><LockClosedIcon className="h-5 w-5 mr-2"/>Access Control</h3>
                <div className="space-y-4">
                    <div className={checkboxItemClass}>
                        <input 
                            id="is_admin_only" 
                            name="is_admin_only" 
                            type="checkbox" 
                            checked={formData.is_admin_only || !!formData.owner_user_id} 
                            onChange={handleChange} 
                            className={checkboxInputClass} 
                            disabled={isFormDisabled || !!formData.owner_user_id} 
                        />
                        <label htmlFor="is_admin_only" className={checkboxLabelClass}>Admin Only Access</label>
                    </div>
                    
                    <div className={checkboxItemClass}>
                        <input 
                            id="owner_user_id_toggle" 
                            name="owner_user_id_toggle" 
                            type="checkbox" 
                            checked={!!formData.owner_user_id} 
                            onChange={handleChange} 
                            className={checkboxInputClass} 
                            disabled={isFormDisabled} 
                        />
                        <label htmlFor="owner_user_id_toggle" className={checkboxLabelClass}>For Me Only (Private to You)</label>
                    </div>
                </div>
                <p className="mt-2 text-xs text-textSecondary">
                    If "For Me Only" is checked, this item will only be visible and editable by you. It automatically implies "Admin Only".<br/>
                    If only "Admin Only" is checked, any admin can view/edit, but non-admins cannot.
                </p>
            </div>
        )}

        {isBirthdayCampaign ? (
             <div className={sectionClass}>
                <h3 className={sectionHeaderClass}>Birthday Automation Rules</h3>
                <div className={fieldGroupClass}>
                    <div className="sm:col-span-2">
                        <label htmlFor="birthday_send_offset_days" className={labelClass}>Send Relative to Birthday</label>
                        <select name="birthday_send_offset_days" id="birthday_send_offset_days" value={formData.birthday_send_offset_days ?? 0} onChange={handleChange} className={inputClass}>
                            <option value="0">On Birthday</option>
                            <option value="1">1 Day Before</option>
                            <option value="2">2 Days Before</option>
                            <option value="3">3 Days Before</option>
                            <option value="7">7 Days Before</option>
                        </select>
                    </div>
                     <div className="sm:col-span-2">
                        <label htmlFor="birthday_send_time" className={labelClass}>Send at Time (HH:MM) *</label>
                        <input type="time" name="birthday_send_time" id="birthday_send_time" value={formData.birthday_send_time || '09:00'} onChange={handleChange} className={inputClass} required />
                    </div>
                     <div className="sm:col-span-2">
                        <label htmlFor="holiday_handling_rule" className={labelClass}>Holiday Handling Rule</label>
                        <select name="holiday_handling_rule" id="holiday_handling_rule" value={formData.holiday_handling_rule || HolidayHandlingRule.SEND_ON_HOLIDAY} onChange={handleChange} className={inputClass}>
                            {Object.values(HolidayHandlingRule).map(rule => (
                                <option key={rule} value={rule}>{formatEnumValueForDisplay(rule)}</option>
                            ))}
                        </select>
                    </div>
                </div>
            </div>
        ) : (
            <div className={sectionClass}>
                <h3 className={sectionHeaderClass}>Scheduling & Sending</h3>
                <div className={fieldGroupClass}>
                    <div className="sm:col-span-3">
                        <EnhancedDateTimePicker
                          value={formData.scheduled_date_input}
                          onChange={(value) => {
                            console.log('🔍 [DateTimePicker] onChange called with value:', JSON.stringify(value));
                            setFormData(prev => ({ ...prev, scheduled_date_input: value }));
                            // Trigger the blur handler logic if needed
                            handleScheduledDateInputBlur({ target: { value } } as any);
                          }}
                          disabled={isFormDisabled}
                          label="Scheduled Date & Time (Optional)"
                        />
                        <p className={helpTextClass}>
                          {formData.scheduled_date_input
                            ? '✅ Campaign will be automatically set to "Scheduled" status'
                            : '📝 Leave blank to save as "Draft" status'
                          }
                        </p>
                    </div>
                    {isEditMode && formData.next_batch_eligible_at && (
                        <div className="sm:col-span-3 flex items-end">
                            <CountdownTimer targetDate={formData.next_batch_eligible_at} className="text-sm text-orange-600 dark:text-orange-400 font-medium" prefixText="Next batch eligible in: "/>
                        </div>
                    )}

                </div>
            </div>
        )}

        <div className={sectionClass}>
          <h3 className={sectionHeaderClass}>Signatures (Optional)</h3>
          <div className={fieldGroupClass}>
            <div className="sm:col-span-2">
              <label htmlFor="email_signature_id" className={labelClass}>Email Signature</label>
              <select name="email_signature_id" id="email_signature_id" value={formData.email_signature_id || ''} onChange={handleChange} className={inputClass}>
                <option value="">None</option>
                {emailSignatures.map(sig => (<option key={sig.id} value={sig.id}>{sig.name}</option>))}
              </select>
            </div>
            <div className="sm:col-span-2">
              <label htmlFor="whatsapp_signature_id" className={labelClass}>WhatsApp Signature</label>
              <select name="whatsapp_signature_id" id="whatsapp_signature_id" value={formData.whatsapp_signature_id || ''} onChange={handleChange} className={inputClass}>
                <option value="">None</option>
                {whatsappSignatures.map(sig => (<option key={sig.id} value={sig.id}>{sig.name}</option>))}
              </select>
            </div>
             <div className="sm:col-span-2">
              <label htmlFor="sms_signature_id" className={labelClass}>SMS Signature</label>
              <select name="sms_signature_id" id="sms_signature_id" value={formData.sms_signature_id || ''} onChange={handleChange} className={inputClass}>
                <option value="">None</option>
                {smsSignatures.map(sig => (<option key={sig.id} value={sig.id}>{sig.name}</option>))}
              </select>
            </div>
          </div>
        </div>

        {/* Remarks Section */}
        <div className={sectionClass}>
          <h3 className={sectionHeaderClass}>Campaign Notes</h3>
          <div className={fieldGroupClass}>
            <div className="sm:col-span-6">
              <label htmlFor="remarks" className={labelClass}>Remarks (Optional)</label>
              <textarea
                name="remarks"
                id="remarks"
                rows={3}
                value={formData.remarks || ''}
                onChange={handleChange}
                className={inputClass}
                placeholder="Add any notes or remarks about this campaign for future reference..."
                disabled={isFormDisabled}
              />
              <p className={helpTextClass}>
                Use this field to keep notes about the campaign for future reference, such as special instructions, context, or reminders.
              </p>
            </div>
          </div>
        </div>

        {/* Channel Toggles */}
        <div className={sectionClass}>
          <h3 className={sectionHeaderClass}>Sending Channels</h3>
          <ChannelToggles
            emailEnabled={emailEnabled}
            whatsappEnabled={whatsappEnabled}
            smsEnabled={smsEnabled}
            onEmailToggle={setEmailEnabled}
            onWhatsappToggle={setWhatsappEnabled}
            onSmsToggle={setSmsEnabled}
            disabled={isFormDisabled}
            className="mt-4"
          />
        </div>

        {/* Campaign Notes */}
        <div className={sectionClass}>
          <h3 className={sectionHeaderClass}>Campaign Notes</h3>
          <div className={fieldGroupClass}>
            <div className="sm:col-span-2">
              <label htmlFor="remarks" className={labelClass}>
                Remarks
              </label>
              <textarea
                name="remarks"
                id="remarks"
                rows={3}
                value={formData.remarks}
                onChange={handleChange}
                className={inputClass}
                placeholder="Add any notes or remarks about this campaign..."
              />
              <p className="mt-1 text-sm text-gray-500">
                Optional notes for internal reference about this campaign.
              </p>
            </div>
          </div>
        </div>

        {/* Form buttons removed - now using floating action bar */}
        <div className="pb-20">
          {/* Add padding bottom to account for floating action bar */}
        </div>
        </fieldset>
      </form>
      

      
      {/* Enhanced Subscriber Selection Modal */}
      <EnhancedSubscriberSelectorModal
        isOpen={showEnhancedModal}
        onClose={() => setShowEnhancedModal(false)}
        onConfirm={handleEnhancedModalConfirm}
        preSelectedIds={selectedSubscriberIds}
        templateInterestAreaId={
          formData.template_id
            ? availableTemplates.find(t => t.id === formData.template_id)?.interest_area_id
            : undefined
        }
        selectionMode={subscriberSelectionMode}
        title={
          subscriberSelectionMode === 'segment'
            ? "Modify Template Recipients"
            : subscriberSelectionMode === 'manual'
            ? "Select Campaign Recipients"
            : "Select Campaign Recipients"
        }
      />

      {/* Delete Confirmation Modal */}
      {showDeleteModal && loadedCampaign && (
        <ConfirmationModal
          isOpen={showDeleteModal}
          title="Confirm Deletion"
          message={<>Are you sure you want to delete campaign: <strong>{loadedCampaign.name}</strong>?</>}
          onConfirm={confirmDelete}
          onCancel={() => setShowDeleteModal(false)}
          confirmText="Delete"
        />
      )}

      {/* Campaign Sending Status Modal - Replaced with dedicated page navigation */}

      {/* Campaign Creation Success Modal */}
      {showSuccessModal && createdCampaignInfo && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
            <div className="flex items-center mb-4">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                </div>
              </div>
              <div className="ml-3">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  Campaign Created Successfully!
                </h3>
              </div>
            </div>

            <div className="mb-6">
              <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                Your campaign <strong>"{createdCampaignInfo.name}"</strong> has been created with status: <strong>{createdCampaignInfo.status}</strong>
              </p>

              {createdCampaignInfo.status === 'draft' && (
                <div className="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-md p-3">
                  <p className="text-sm text-blue-800 dark:text-blue-200">
                    <strong>📋 To view your campaign:</strong><br/>
                    Go to the Campaigns page and click the <strong>"DRAFT"</strong> filter button to see all draft campaigns.
                  </p>
                </div>
              )}

              {createdCampaignInfo.status === 'scheduled' && (
                <div className="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-md p-3">
                  <p className="text-sm text-yellow-800 dark:text-yellow-200">
                    <strong>📅 To view your campaign:</strong><br/>
                    Go to the Campaigns page and click the <strong>"SCHEDULED_SENDING"</strong> filter button.
                  </p>
                </div>
              )}

              {createdCampaignInfo.status === 'sent' && (
                <div className="bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-md p-3">
                  <p className="text-sm text-green-800 dark:text-green-200">
                    <strong>✅ To view your campaign:</strong><br/>
                    Go to the Campaigns page and click the <strong>"COMPLETED"</strong> filter button.
                  </p>
                </div>
              )}
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => {
                  setShowSuccessModal(false);
                  setCreatedCampaignInfo(null);
                  // Navigate to campaigns with appropriate filter
                  const targetPath = isBirthdayCampaign ? '/birthday-automations' : '/campaigns';
                  const filterParam = createdCampaignInfo.status === 'draft' ? '&filter=DRAFT' :
                                    createdCampaignInfo.status === 'scheduled' ? '&filter=SCHEDULED_SENDING' :
                                    createdCampaignInfo.status === 'sent' ? '&filter=COMPLETED' : '';
                  navigate(`${targetPath}?refresh=${Date.now()}${filterParam}`);
                }}
                className="bg-primary hover:bg-opacity-80 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transition duration-150 ease-in-out"
              >
                Go to Campaigns
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Floating Action Bar */}
      <FloatingActionBar
        isEditMode={isEditMode}
        isDisabled={isFormDisabled}
        isSaving={isSaving}
        saveText={
          isEditMode && loadedCampaign && (loadedCampaign.status === CampaignStatus.SENT || loadedCampaign.status === CampaignStatus.FAILED)
            ? "Save as New Campaign"
            : isEditMode
            ? "Save Changes"
            : isBirthdayCampaign
            ? "Create Birthday Automation"
            : "Create Campaign"
        }
        onSave={handleFloatingSave}
        onCancel={handleFloatingCancel}
        onDelete={handleFloatingDelete}
        showDelete={isEditMode && loadedCampaign && canUserEditDeleteItem(loadedCampaign, currentUser)}
        saveVariant={
          isEditMode && loadedCampaign && (loadedCampaign.status === CampaignStatus.SENT || loadedCampaign.status === CampaignStatus.FAILED)
            ? "warning"
            : "primary"
        }
      />
    </div>
  );
};

export default AddEditCampaignPage;
