// Unified WhatsApp Service
// services/WhatsAppService.ts

export interface WhatsAppConfig {
  // Global control
  globalEnabled: boolean;
  
  // API Configuration
  apiEnabled: boolean;
  phoneNumberId: string;
  accessToken: string;
  webhookSecret: string;
  verifyToken: string;
  defaultCountryCode: string;
  
  // Desktop Automation Configuration
  desktopEnabled: boolean;
  batchDelay: number; // seconds between messages
  maxBatchSize: number;
  autoClose: boolean; // close WhatsApp after sending
}

export interface WhatsAppMessage {
  to: string;
  message: string;
  subscriberId?: string;
  subscriberName?: string;
}

export interface WhatsAppSendResult {
  success: boolean;
  messageId?: string;
  error?: string;
  method: 'api' | 'desktop';
}

export interface WhatsAppBulkResult {
  total: number;
  successful: number;
  failed: number;
  results: Array<{
    phone: string;
    success: boolean;
    messageId?: string;
    error?: string;
  }>;
}

class WhatsAppService {
  private config: WhatsAppConfig = {
    globalEnabled: true,
    apiEnabled: false,
    phoneNumberId: '',
    accessToken: '',
    webhookSecret: '',
    verifyToken: '',
    defaultCountryCode: '91',
    desktopEnabled: true,
    batchDelay: 3,
    maxBatchSize: 50,
    autoClose: false
  };

  private readonly CONFIG_KEY = 'whatsapp_config';

  constructor() {
    this.loadConfiguration();
  }

  // Configuration Management
  loadConfiguration(): void {
    try {
      const saved = localStorage.getItem(this.CONFIG_KEY);
      if (saved) {
        this.config = { ...this.config, ...JSON.parse(saved) };
      }
    } catch (error) {
      console.error('Failed to load WhatsApp configuration:', error);
    }
  }

  saveConfiguration(config: Partial<WhatsAppConfig>): void {
    this.config = { ...this.config, ...config };
    localStorage.setItem(this.CONFIG_KEY, JSON.stringify(this.config));
  }

  getConfiguration(): WhatsAppConfig {
    return { ...this.config };
  }

  // Global Control
  isGloballyEnabled(): boolean {
    return this.config.globalEnabled;
  }

  setGlobalEnabled(enabled: boolean): void {
    this.saveConfiguration({ globalEnabled: enabled });
  }

  // Phone Number Utilities
  isValidPhoneNumber(phone: string): boolean {
    if (!phone) return false;
    const digits = phone.replace(/\D/g, '');
    return digits.length === 10 || (digits.length === 12 && digits.startsWith('91'));
  }

  formatPhoneNumber(phone: string): string {
    if (!phone) return '';
    const digits = phone.replace(/\D/g, '');
    
    if (digits.length === 10) {
      return `${this.config.defaultCountryCode}${digits}`;
    }
    return digits;
  }

  // API Methods
  async sendViaAPI(message: WhatsAppMessage): Promise<WhatsAppSendResult> {
    if (!this.config.globalEnabled) {
      return { success: false, error: 'WhatsApp is globally disabled', method: 'api' };
    }

    if (!this.config.apiEnabled) {
      return { success: false, error: 'WhatsApp API is not enabled', method: 'api' };
    }

    if (!this.config.accessToken || !this.config.phoneNumberId) {
      return { success: false, error: 'WhatsApp API credentials not configured', method: 'api' };
    }

    try {
      const response = await fetch('/api/whatsapp/send-message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          to: this.formatPhoneNumber(message.to),
          message: message.message,
          phoneNumberId: this.config.phoneNumberId,
          accessToken: this.config.accessToken
        })
      });

      const result = await response.json();

      if (result.success) {
        return {
          success: true,
          messageId: result.messageId,
          method: 'api'
        };
      } else {
        return {
          success: false,
          error: result.error || 'API request failed',
          method: 'api'
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown API error',
        method: 'api'
      };
    }
  }

  // Desktop Methods
  async sendViaDesktop(message: WhatsAppMessage): Promise<WhatsAppSendResult> {
    if (!this.config.globalEnabled) {
      return { success: false, error: 'WhatsApp is globally disabled', method: 'desktop' };
    }

    if (!this.config.desktopEnabled) {
      return { success: false, error: 'WhatsApp Desktop is not enabled', method: 'desktop' };
    }

    try {
      const formattedPhone = this.formatPhoneNumber(message.to);
      const whatsappUrl = `https://web.whatsapp.com/send?phone=${formattedPhone}&text=${encodeURIComponent(message.message)}`;
      
      // Open WhatsApp Web
      window.open(whatsappUrl, '_blank');
      
      return {
        success: true,
        messageId: `desktop_${Date.now()}`,
        method: 'desktop'
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Desktop opening failed',
        method: 'desktop'
      };
    }
  }

  // Bulk Desktop Automation
  async sendBulkViaDesktop(messages: WhatsAppMessage[]): Promise<WhatsAppBulkResult> {
    if (!this.config.globalEnabled) {
      return {
        total: messages.length,
        successful: 0,
        failed: messages.length,
        results: messages.map(msg => ({
          phone: msg.to,
          success: false,
          error: 'WhatsApp is globally disabled'
        }))
      };
    }

    const results: WhatsAppBulkResult = {
      total: messages.length,
      successful: 0,
      failed: 0,
      results: []
    };

    try {
      // Send bulk messages via automation API with enhanced automation
      const response = await fetch('/api/whatsapp/send-bulk-desktop', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          messages: messages.map(msg => ({
            to: this.formatPhoneNumber(msg.to),
            message: msg.message,
            subscriberId: msg.subscriberId,
            subscriberName: msg.subscriberName
          })),
          config: {
            batchDelay: this.config.batchDelay,
            maxBatchSize: this.config.maxBatchSize,
            autoClose: this.config.autoClose,
            automationMethod: 'desktop' // Use desktop keyboard automation for better reliability
          }
        })
      });

      const result = await response.json();

      if (result.success) {
        results.successful = result.successful || 0;
        results.failed = result.failed || 0;
        results.results = result.results || [];
      } else {
        // If automation fails, fall back to manual opening
        for (const message of messages) {
          const singleResult = await this.sendViaDesktop(message);
          results.results.push({
            phone: message.to,
            success: singleResult.success,
            messageId: singleResult.messageId,
            error: singleResult.error
          });

          if (singleResult.success) {
            results.successful++;
          } else {
            results.failed++;
          }
        }
      }
    } catch (error) {
      console.error('Bulk desktop sending failed:', error);
      results.failed = messages.length;
      results.results = messages.map(msg => ({
        phone: msg.to,
        success: false,
        error: 'Bulk automation failed'
      }));
    }

    return results;
  }

  // Smart Sending (chooses best method)
  async sendMessage(message: WhatsAppMessage): Promise<WhatsAppSendResult> {
    if (!this.config.globalEnabled) {
      return { success: false, error: 'WhatsApp is globally disabled', method: 'desktop' };
    }

    // Prefer API if configured, otherwise use desktop
    if (this.config.apiEnabled && this.config.accessToken && this.config.phoneNumberId) {
      return this.sendViaAPI(message);
    } else if (this.config.desktopEnabled) {
      return this.sendViaDesktop(message);
    } else {
      return { success: false, error: 'No WhatsApp method is enabled', method: 'desktop' };
    }
  }

  // Test Methods
  async testAPI(): Promise<{ success: boolean; message?: string; error?: string }> {
    if (!this.config.apiEnabled || !this.config.accessToken || !this.config.phoneNumberId) {
      return { success: false, error: 'API credentials not configured' };
    }

    try {
      const response = await fetch('/api/whatsapp/test-connection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          phoneNumberId: this.config.phoneNumberId,
          accessToken: this.config.accessToken
        })
      });

      const result = await response.json();
      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Test failed'
      };
    }
  }

  async testDesktop(): Promise<{ success: boolean; message?: string; error?: string }> {
    try {
      const response = await fetch('/api/whatsapp/test-desktop', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();
      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Desktop test failed'
      };
    }
  }
}

// Export singleton instance
export const whatsappService = new WhatsAppService();
export default whatsappService;
