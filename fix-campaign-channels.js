import { database } from './backend/database/connection.js';

console.log('🔧 FIXING CAMPAIGN CHANNEL COLUMNS');
console.log('==================================\n');

async function fixCampaignChannels() {
  try {
    console.log('📁 Connecting to database...');
    await database.connect();
    
    // Check current schema
    console.log('📋 Checking campaigns table schema...');
    const tableInfo = await database.all('PRAGMA table_info(campaigns)');
    const existingColumns = tableInfo.map(col => col.name);
    
    console.log('Current columns:', existingColumns.length);
    
    // Define required columns
    const requiredColumns = [
      { name: 'email_recipients_count', type: 'INTEGER DEFAULT 0' },
      { name: 'whatsapp_recipients_count', type: 'INTEGER DEFAULT 0' },
      { name: 'sms_recipients_count', type: 'INTEGER DEFAULT 0' },
      { name: 'email_enabled', type: 'BOOLEAN DEFAULT 1' },
      { name: 'whatsapp_enabled', type: 'BOOLEAN DEFAULT 1' },
      { name: 'sms_enabled', type: 'BOOLEAN DEFAULT 1' }
    ];
    
    // Check which columns are missing
    const missingColumns = requiredColumns.filter(col => !existingColumns.includes(col.name));
    
    console.log('\nAnalysis:');
    for (const column of requiredColumns) {
      const exists = existingColumns.includes(column.name);
      console.log(`${exists ? '✅' : '❌'} ${column.name}: ${exists ? 'EXISTS' : 'MISSING'}`);
    }
    
    // Add missing columns
    if (missingColumns.length > 0) {
      console.log(`\n🔧 Adding ${missingColumns.length} missing columns...`);
      
      for (const column of missingColumns) {
        try {
          await database.run(`ALTER TABLE campaigns ADD COLUMN ${column.name} ${column.type}`);
          console.log(`✅ Added: ${column.name}`);
        } catch (error) {
          console.log(`⚠️ Error adding ${column.name}:`, error.message);
        }
      }
      
      // Update existing records with default values
      console.log('\n🔄 Updating existing campaigns with default values...');
      const updateQuery = `
        UPDATE campaigns 
        SET ${missingColumns.map(col => `${col.name} = ${col.name.includes('enabled') ? '1' : '0'}`).join(', ')}
        WHERE ${missingColumns.map(col => `${col.name} IS NULL`).join(' OR ')}
      `;
      
      const updateResult = await database.run(updateQuery);
      console.log(`✅ Updated ${updateResult.changes} existing campaigns`);
    } else {
      console.log('\n✅ All required columns already exist!');
    }
    
    // Verify final schema
    console.log('\n📋 Final verification:');
    const finalTableInfo = await database.all('PRAGMA table_info(campaigns)');
    const channelColumns = finalTableInfo.filter(col => 
      col.name.includes('recipients_count') || col.name.includes('_enabled')
    );
    
    channelColumns.forEach(col => {
      console.log(`✅ ${col.name}: ${col.type}`);
    });
    
    console.log('\n🎉 Campaign channel columns are now ready!');
    console.log('💡 Frontend can now send channel-specific counts and toggles.');
    
  } catch (error) {
    console.error('❌ Error fixing campaign channels:', error);
  }
  
  process.exit(0);
}

fixCampaignChannels();
