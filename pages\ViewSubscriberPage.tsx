import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Subscriber } from '../types';
import { subscriberService } from '../services/SubscriberService-API';
import { ArrowLeftIcon } from '../components/icons';

const ViewSubscriberPage: React.FC = () => {
  const { subscriberId } = useParams<{ subscriberId: string }>();
  const navigate = useNavigate();
  const [subscriber, setSubscriber] = useState<Subscriber | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadSubscriber = async () => {
      if (!subscriberId) {
        setError('No subscriber ID provided');
        setLoading(false);
        return;
      }

      try {
        const subscriberData = await subscriberService.getSubscriberById(subscriberId);
        setSubscriber(subscriberData);
      } catch (err) {
        console.error('Error loading subscriber:', err);
        setError('Failed to load subscriber details');
      } finally {
        setLoading(false);
      }
    };

    loadSubscriber();
  }, [subscriberId]);

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not set';
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString;
    }
  };

  const formatBirthDate = (birthDate: string | null) => {
    if (!birthDate) return 'Not set';
    // Handle both full dates and day/month format
    if (birthDate.includes('/')) {
      return birthDate;
    }
    return formatDate(birthDate);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-textPrimary">Loading subscriber details...</div>
      </div>
    );
  }

  if (error || !subscriber) {
    return (
      <div className="min-h-screen bg-background p-6">
        <div className="max-w-4xl mx-auto">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h2 className="text-lg font-semibold text-red-800 mb-2">Error</h2>
            <p className="text-red-600">{error || 'Subscriber not found'}</p>
            <button
              onClick={() => navigate('/subscribers')}
              className="mt-4 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
            >
              Back to Subscribers
            </button>
          </div>
        </div>
      </div>
    );
  }

  const fieldClass = "block text-sm font-medium text-textPrimary mb-1";
  const valueClass = "w-full p-3 border border-border rounded-lg bg-gray-50 text-textPrimary";

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-4xl mx-auto p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate('/subscribers')}
              className="flex items-center text-primary hover:text-opacity-80"
            >
              <ArrowLeftIcon className="h-5 w-5 mr-2" />
              Back to Subscribers
            </button>
            <h1 className="text-2xl font-bold text-textPrimary">View Subscriber</h1>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => navigate(`/subscribers/edit/${subscriber.id}`)}
              className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-opacity-90"
            >
              Edit Subscriber
            </button>
          </div>
        </div>

        {/* Subscriber Details */}
        <div className="bg-cardBackground border border-border rounded-lg shadow-sm">
          <div className="p-6">
            <h2 className="text-lg font-semibold text-textPrimary mb-6">Subscriber Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Basic Information */}
              <div>
                <label className={fieldClass}>Email Address</label>
                <div className={valueClass}>{subscriber.email}</div>
              </div>

              <div>
                <label className={fieldClass}>Status</label>
                <div className={valueClass}>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    subscriber.status === 'active' 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {subscriber.status}
                  </span>
                </div>
              </div>

              <div>
                <label className={fieldClass}>First Name</label>
                <div className={valueClass}>{subscriber.firstName || 'Not provided'}</div>
              </div>

              <div>
                <label className={fieldClass}>Last Name</label>
                <div className={valueClass}>{subscriber.lastName || 'Not provided'}</div>
              </div>

              <div>
                <label className={fieldClass}>Entity/Company Name</label>
                <div className={valueClass}>{subscriber.entityName || 'Not provided'}</div>
              </div>

              <div>
                <label className={fieldClass}>Phone Number</label>
                <div className={valueClass}>{subscriber.phone || 'Not provided'}</div>
              </div>

              <div>
                <label className={fieldClass}>Birth Date</label>
                <div className={valueClass}>{formatBirthDate(subscriber.birthDate)}</div>
              </div>

              <div>
                <label className={fieldClass}>WhatsApp Number</label>
                <div className={valueClass}>{subscriber.whatsapp_number || 'Not provided'}</div>
              </div>

              {/* Communication Preferences */}
              <div>
                <label className={fieldClass}>Allow WhatsApp</label>
                <div className={valueClass}>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    subscriber.allowWhatsApp 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {subscriber.allowWhatsApp ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>

              <div>
                <label className={fieldClass}>Allow SMS</label>
                <div className={valueClass}>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    subscriber.allowSms 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {subscriber.allowSms ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>

              {/* Timestamps */}
              <div>
                <label className={fieldClass}>Subscribed At</label>
                <div className={valueClass}>{formatDate(subscriber.subscribed_at)}</div>
              </div>

              <div>
                <label className={fieldClass}>Last Updated</label>
                <div className={valueClass}>{formatDate(subscriber.updated_at)}</div>
              </div>
            </div>

            {/* Remarks Section */}
            <div className="mt-6">
              <label className={fieldClass}>Remarks</label>
              <div className="w-full p-3 border border-border rounded-lg bg-gray-50 text-textPrimary min-h-[100px] whitespace-pre-wrap">
                {subscriber.remarks || 'No remarks added'}
              </div>
            </div>

            {/* Custom Fields */}
            {subscriber.customFields && (
              <div className="mt-6">
                <label className={fieldClass}>Custom Fields</label>
                <div className="w-full p-3 border border-border rounded-lg bg-gray-50 text-textPrimary">
                  <pre className="text-sm">{JSON.stringify(
                    typeof subscriber.customFields === 'string'
                      ? JSON.parse(subscriber.customFields)
                      : subscriber.customFields,
                    null, 2
                  )}</pre>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewSubscriberPage;
