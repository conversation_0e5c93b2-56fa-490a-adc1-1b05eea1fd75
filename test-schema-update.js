console.log('Testing database schema update...');

// Simple inline test - add columns directly
import('../../backend/database/connection.js').then(async ({ database }) => {
  try {
    console.log('Connecting to database...');
    await database.connect();
    
    console.log('Checking if columns exist...');
    const tableInfo = await database.all('PRAGMA table_info(campaigns)');
    const columns = tableInfo.map(col => col.name);
    
    const requiredColumns = [
      'email_recipients_count',
      'whatsapp_recipients_count', 
      'sms_recipients_count',
      'email_enabled',
      'whatsapp_enabled',
      'sms_enabled'
    ];
    
    console.log('Current columns:', columns);
    
    for (const col of requiredColumns) {
      if (!columns.includes(col)) {
        console.log(`Adding missing column: ${col}`);
        try {
          if (col.includes('recipients_count')) {
            await database.run(`ALTER TABLE campaigns ADD COLUMN ${col} INTEGER DEFAULT 0`);
          } else {
            await database.run(`ALTER TABLE campaigns ADD COLUMN ${col} BOOLEAN DEFAULT 1`);
          }
          console.log(`✅ Added ${col}`);
        } catch (error) {
          console.log(`⚠️ Error adding ${col}:`, error.message);
        }
      } else {
        console.log(`✅ ${col} already exists`);
      }
    }
    
    console.log('Schema update completed!');
    process.exit(0);
    
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}).catch(error => {
  console.error('Import error:', error);
  process.exit(1);
});
