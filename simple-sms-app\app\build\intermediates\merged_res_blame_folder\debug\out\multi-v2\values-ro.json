{"logs": [{"outputFile": "com.crmsms.app-mergeDebugResources-27:/values-ro/values-ro.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\23842c59899d74c6854e6a450173091d\\transformed\\material-1.8.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,321,413,501,588,684,774,875,996,1080,1146,1241,1315,1375,1459,1521,1587,1645,1718,1781,1837,1956,2013,2074,2130,2204,2349,2435,2519,2652,2734,2817,2907,2962,3013,3079,3152,3230,3318,3389,3466,3540,3612,3703,3777,3872,3970,4044,4124,4225,4278,4344,4433,4523,4585,4649,4712,4824,4937,5047,5159,5218,5273", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "endColumns": "12,91,87,86,95,89,100,120,83,65,94,73,59,83,61,65,57,72,62,55,118,56,60,55,73,144,85,83,132,81,82,89,54,50,65,72,77,87,70,76,73,71,90,73,94,97,73,79,100,52,65,88,89,61,63,62,111,112,109,111,58,54,78", "endOffsets": "316,408,496,583,679,769,870,991,1075,1141,1236,1310,1370,1454,1516,1582,1640,1713,1776,1832,1951,2008,2069,2125,2199,2344,2430,2514,2647,2729,2812,2902,2957,3008,3074,3147,3225,3313,3384,3461,3535,3607,3698,3772,3867,3965,4039,4119,4220,4273,4339,4428,4518,4580,4644,4707,4819,4932,5042,5154,5213,5268,5347"}, "to": {"startLines": "2,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3117,3209,3297,3384,3480,3570,3671,3792,3876,3942,4037,4111,4171,4255,4317,4383,4441,4514,4577,4633,4752,4809,4870,4926,5000,5145,5231,5315,5448,5530,5613,5703,5758,5809,5875,5948,6026,6114,6185,6262,6336,6408,6499,6573,6668,6766,6840,6920,7021,7074,7140,7229,7319,7381,7445,7508,7620,7733,7843,7955,8014,8069", "endLines": "6,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95", "endColumns": "12,91,87,86,95,89,100,120,83,65,94,73,59,83,61,65,57,72,62,55,118,56,60,55,73,144,85,83,132,81,82,89,54,50,65,72,77,87,70,76,73,71,90,73,94,97,73,79,100,52,65,88,89,61,63,62,111,112,109,111,58,54,78", "endOffsets": "366,3204,3292,3379,3475,3565,3666,3787,3871,3937,4032,4106,4166,4250,4312,4378,4436,4509,4572,4628,4747,4804,4865,4921,4995,5140,5226,5310,5443,5525,5608,5698,5753,5804,5870,5943,6021,6109,6180,6257,6331,6403,6494,6568,6663,6761,6835,6915,7016,7069,7135,7224,7314,7376,7440,7503,7615,7728,7838,7950,8009,8064,8143"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\94b9c9510706c6dc301febc3dfcf0a64\\transformed\\appcompat-1.6.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,330,443,527,631,752,837,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1855,1938,2050,2158,2258,2372,2478,2584,2748,2851", "endColumns": "120,103,112,83,103,120,84,79,90,92,94,93,99,92,94,93,90,91,82,111,107,99,113,105,105,163,102,83", "endOffsets": "221,325,438,522,626,747,832,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1850,1933,2045,2153,2253,2367,2473,2579,2743,2846,2930"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "371,492,596,709,793,897,1018,1103,1183,1274,1367,1462,1556,1656,1749,1844,1938,2029,2121,2204,2316,2424,2524,2638,2744,2850,3014,8148", "endColumns": "120,103,112,83,103,120,84,79,90,92,94,93,99,92,94,93,90,91,82,111,107,99,113,105,105,163,102,83", "endOffsets": "487,591,704,788,892,1013,1098,1178,1269,1362,1457,1551,1651,1744,1839,1933,2024,2116,2199,2311,2419,2519,2633,2739,2845,3009,3112,8227"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7da3e27adb8c02368eb46247c342376b\\transformed\\core-1.9.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "97", "startColumns": "4", "startOffsets": "8232", "endColumns": "100", "endOffsets": "8328"}}]}]}