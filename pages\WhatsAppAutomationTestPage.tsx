import React, { useState, useEffect } from 'react';
import Header from '../components/Header';
import { CheckCircleIcon, XCircleIcon, CogIcon } from '../components/icons';

interface TestResult {
  success: boolean;
  message: string;
  details?: any;
}

const WhatsAppAutomationTestPage: React.FC = () => {
  const [systemCheck, setSystemCheck] = useState<TestResult | null>(null);
  const [automationTest, setAutomationTest] = useState<TestResult | null>(null);
  const [preferences, setPreferences] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    runSystemCheck();
    loadPreferences();
  }, []);

  const runSystemCheck = async () => {
    try {
      const response = await fetch('/api/whatsapp-automation/system-check');
      const result = await response.json();
      
      setSystemCheck({
        success: result.robotjsAvailable && result.systemCompatible,
        message: result.robotjsAvailable 
          ? 'System is ready for automation' 
          : 'robotjs not available',
        details: result
      });
    } catch (error) {
      setSystemCheck({
        success: false,
        message: `System check failed: ${error.message}`
      });
    }
  };

  const runAutomationTest = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/whatsapp-automation/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      const result = await response.json();
      setAutomationTest(result);
    } catch (error) {
      setAutomationTest({
        success: false,
        message: `Test failed: ${error.message}`
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadPreferences = async () => {
    try {
      const response = await fetch('/api/whatsapp-automation/preferences');
      const result = await response.json();
      setPreferences(result);
    } catch (error) {
      console.error('Failed to load preferences:', error);
    }
  };

  const enableAutomation = async () => {
    try {
      const response = await fetch('/api/whatsapp-automation/preferences', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...preferences,
          enableRobotjsAutomation: true
        })
      });
      
      const result = await response.json();
      if (result.success) {
        setPreferences(result.preferences);
      }
    } catch (error) {
      console.error('Failed to enable automation:', error);
    }
  };

  const testSingleMessage = async () => {
    if (!window.confirm('This will test sending a message to your own WhatsApp. Make sure WhatsApp Desktop is open and logged in. Continue?')) {
      return;
    }

    setIsLoading(true);
    try {
      // This would integrate with the robotjs service
      const testContact = {
        phone: '+1234567890', // Replace with test number
        message: 'Test message from CRM automation',
        name: 'Test Contact'
      };

      // For now, just show a message
      alert('Single message test would be implemented here. For safety, this is disabled in the test page.');
      
    } catch (error) {
      console.error('Test message failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">WhatsApp Automation Test</h1>
          <p className="text-gray-600 mt-2">
            Test and verify robotjs WhatsApp automation functionality
          </p>
        </div>

        {/* System Check */}
        <div className="bg-white shadow rounded-lg mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">System Check</h2>
          </div>
          <div className="px-6 py-4">
            {systemCheck ? (
              <div className={`flex items-center space-x-3 ${
                systemCheck.success ? 'text-green-800' : 'text-red-800'
              }`}>
                {systemCheck.success ? <CheckCircleIcon className="h-5 w-5" /> : <XCircleIcon className="h-5 w-5" />}
                <span>{systemCheck.message}</span>
              </div>
            ) : (
              <div className="text-gray-600">Running system check...</div>
            )}
            
            {systemCheck?.details && (
              <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className={`p-3 rounded-lg ${systemCheck.details.robotjsAvailable ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
                  <div className="flex items-center">
                    <span className={`text-sm font-medium ${systemCheck.details.robotjsAvailable ? 'text-green-800' : 'text-red-800'}`}>
                      {systemCheck.details.robotjsAvailable ? '✅' : '❌'} robotjs Available
                    </span>
                  </div>
                </div>
                <div className={`p-3 rounded-lg ${systemCheck.details.systemCompatible ? 'bg-green-50 border border-green-200' : 'bg-yellow-50 border border-yellow-200'}`}>
                  <div className="flex items-center">
                    <span className={`text-sm font-medium ${systemCheck.details.systemCompatible ? 'text-green-800' : 'text-yellow-800'}`}>
                      {systemCheck.details.systemCompatible ? '✅' : '⚠️'} System Compatible
                    </span>
                  </div>
                </div>
                <div className={`p-3 rounded-lg ${systemCheck.details.whatsappDesktopDetected ? 'bg-green-50 border border-green-200' : 'bg-yellow-50 border border-yellow-200'}`}>
                  <div className="flex items-center">
                    <span className={`text-sm font-medium ${systemCheck.details.whatsappDesktopDetected ? 'text-green-800' : 'text-yellow-800'}`}>
                      {systemCheck.details.whatsappDesktopDetected ? '✅' : '⚠️'} WhatsApp Desktop
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Automation Test */}
        <div className="bg-white shadow rounded-lg mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Automation Test</h2>
          </div>
          <div className="px-6 py-4">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-base font-medium text-gray-900">Basic Automation Test</h3>
                <p className="text-sm text-gray-600">
                  Test if robotjs automation is working correctly
                </p>
              </div>
              <button
                onClick={runAutomationTest}
                disabled={isLoading}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'Testing...' : 'Run Test'}
              </button>
            </div>
            
            {automationTest && (
              <div className={`flex items-center space-x-3 ${
                automationTest.success ? 'text-green-800' : 'text-red-800'
              }`}>
                {automationTest.success ? <CheckCircleIcon className="h-5 w-5" /> : <XCircleIcon className="h-5 w-5" />}
                <span>{automationTest.message}</span>
              </div>
            )}
          </div>
        </div>

        {/* Preferences */}
        <div className="bg-white shadow rounded-lg mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Automation Preferences</h2>
          </div>
          <div className="px-6 py-4">
            {preferences ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-base font-medium text-gray-900">robotjs Automation</h3>
                    <p className="text-sm text-gray-600">
                      Enable automated WhatsApp messaging via robotjs
                    </p>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className={`text-sm font-medium ${preferences.enableRobotjsAutomation ? 'text-green-600' : 'text-red-600'}`}>
                      {preferences.enableRobotjsAutomation ? 'ENABLED' : 'DISABLED'}
                    </span>
                    {!preferences.enableRobotjsAutomation && (
                      <button
                        onClick={enableAutomation}
                        className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700"
                      >
                        Enable
                      </button>
                    )}
                  </div>
                </div>
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">Max Batch Size:</span>
                    <span className="ml-2 text-gray-900">{preferences.maxBatchSize}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Safe Mode:</span>
                    <span className="ml-2 text-gray-900">{preferences.safeMode ? 'Yes' : 'No'}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Auto Retry:</span>
                    <span className="ml-2 text-gray-900">{preferences.autoRetryFailures ? 'Yes' : 'No'}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Screenshots:</span>
                    <span className="ml-2 text-gray-900">{preferences.screenshotLogging ? 'Yes' : 'No'}</span>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-gray-600">Loading preferences...</div>
            )}
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-3">Testing Instructions</h3>
          <ol className="text-sm text-blue-800 space-y-2">
            <li><strong>1.</strong> Ensure WhatsApp Desktop is installed and logged in</li>
            <li><strong>2.</strong> Run the system check to verify robotjs availability</li>
            <li><strong>3.</strong> Run the automation test to verify basic functionality</li>
            <li><strong>4.</strong> Enable automation in preferences if not already enabled</li>
            <li><strong>5.</strong> Go to Settings → WhatsApp Configuration to configure automation</li>
            <li><strong>6.</strong> Create a test campaign with 1-2 recipients to test full automation</li>
          </ol>
          
          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
            <p className="text-sm text-yellow-800">
              <strong>Note:</strong> For safety, actual message sending tests should be done through the main campaign interface with your own phone number or consenting contacts.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WhatsAppAutomationTestPage;
