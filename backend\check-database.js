// Check database structure
import Database from 'better-sqlite3';

const db = new Database('../crm4ca.db');

console.log('📊 Database Tables:');
const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all();
tables.forEach(table => {
  console.log(`  - ${table.name}`);
});

console.log('\n📋 Campaign-related tables:');
const campaignTables = tables.filter(t => t.name.toLowerCase().includes('campaign'));
campaignTables.forEach(table => {
  console.log(`\n🔍 Table: ${table.name}`);
  const columns = db.prepare(`PRAGMA table_info(${table.name})`).all();
  columns.forEach(col => {
    console.log(`    ${col.name}: ${col.type}`);
  });
});

console.log('\n📱 Checking for SMS-related data:');
try {
  const campaigns = db.prepare("SELECT id, name, status FROM campaigns LIMIT 5").all();
  console.log('Campaigns:', campaigns);
} catch (error) {
  console.log('Error reading campaigns:', error.message);
}

db.close();
