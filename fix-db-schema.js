const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = 'E:\\Projects\\CRM-AIstudio\\crm4ca.db';

console.log('🔧 Checking and fixing campaign channel columns...');
console.log('Database path:', dbPath);

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ Error opening database:', err.message);
    process.exit(1);
  }
  console.log('✅ Connected to SQLite database');
});

// Check current schema
db.all("PRAGMA table_info(campaigns)", (err, rows) => {
  if (err) {
    console.error('❌ Error checking schema:', err.message);
    db.close();
    process.exit(1);
  }
  
  const existingColumns = rows.map(row => row.name);
  console.log('📋 Current columns count:', existingColumns.length);
  
  const requiredColumns = [
    'email_recipients_count',
    'whatsapp_recipients_count', 
    'sms_recipients_count',
    'email_enabled',
    'whatsapp_enabled',
    'sms_enabled'
  ];
  
  const missingColumns = requiredColumns.filter(col => !existingColumns.includes(col));
  
  console.log('\n📊 Column Status:');
  requiredColumns.forEach(col => {
    const exists = existingColumns.includes(col);
    console.log(`${exists ? '✅' : '❌'} ${col}`);
  });
  
  if (missingColumns.length === 0) {
    console.log('\n🎉 All columns already exist! No migration needed.');
    db.close();
    process.exit(0);
  }
  
  console.log(`\n🔧 Need to add ${missingColumns.length} columns...`);
  
  // Add missing columns one by one
  let completed = 0;
  
  const addColumn = (column, callback) => {
    const isCountColumn = column.includes('recipients_count');
    const columnDef = isCountColumn ? 'INTEGER DEFAULT 0' : 'BOOLEAN DEFAULT 1';
    
    db.run(`ALTER TABLE campaigns ADD COLUMN ${column} ${columnDef}`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.log(`❌ Error adding ${column}:`, err.message);
      } else {
        console.log(`✅ Added column: ${column}`);
      }
      callback();
    });
  };
  
  // Add columns sequentially
  const addNextColumn = (index) => {
    if (index >= missingColumns.length) {
      // Update existing campaigns with default values
      console.log('\n🔄 Updating existing campaigns...');
      db.run(`
        UPDATE campaigns 
        SET 
          email_recipients_count = COALESCE(email_recipients_count, total_recipients),
          whatsapp_recipients_count = COALESCE(whatsapp_recipients_count, 0),
          sms_recipients_count = COALESCE(sms_recipients_count, 0),
          email_enabled = COALESCE(email_enabled, 1),
          whatsapp_enabled = COALESCE(whatsapp_enabled, 1),
          sms_enabled = COALESCE(sms_enabled, 1)
        WHERE email_recipients_count IS NULL OR whatsapp_recipients_count IS NULL OR sms_recipients_count IS NULL
      `, (err) => {
        if (err) {
          console.log('⚠️ Error updating campaigns:', err.message);
        } else {
          console.log('✅ Updated existing campaigns with default values');
        }
        
        console.log('\n🎉 Migration completed successfully!');
        console.log('💡 Backend can now save channel-specific data.');
        console.log('💡 Frontend channel counting will now work properly.');
        
        db.close();
        process.exit(0);
      });
      return;
    }
    
    addColumn(missingColumns[index], () => {
      addNextColumn(index + 1);
    });
  };
  
  addNextColumn(0);
});
