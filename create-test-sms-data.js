// Create test SMS data for testing the SMS gateway
const baseUrl = 'http://localhost:3001/api';

async function createTestSMSData() {
  console.log('🧪 Creating test SMS data...\n');

  try {
    // 1. Create a test subscriber
    console.log('1. Creating test subscriber...');
    const subscriberResponse = await fetch(`${baseUrl}/subscribers`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        firstName: 'SMS',
        lastName: 'Test',
        phone: '+1234567890',
        allowSms: true,
        status: 'active'
      })
    });
    
    if (!subscriberResponse.ok) {
      const error = await subscriberResponse.text();
      console.log('   ⚠️ Subscriber creation response:', error);
    } else {
      const subscriber = await subscriberResponse.json();
      console.log('   ✅ Test subscriber created:', subscriber.id);
    }

    // 2. Create a test campaign
    console.log('\n2. Creating test SMS campaign...');
    const campaignResponse = await fetch(`${baseUrl}/campaigns`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        name: 'Test SMS Campaign',
        subject: 'Test SMS',
        email_content: 'Test email content',
        sms_content: 'Hello {{firstName}}, this is a test SMS from your CRM system!',
        status: 'draft',
        campaign_type: 'other',
        template_id: 'test-template'
      })
    });

    if (!campaignResponse.ok) {
      const error = await campaignResponse.text();
      console.log('   ⚠️ Campaign creation response:', error);
    } else {
      const campaign = await campaignResponse.json();
      console.log('   ✅ Test campaign created:', campaign.id);
    }

    console.log('\n3. Testing SMS endpoints...');
    
    // Test pending messages
    const pendingResponse = await fetch(`${baseUrl}/sms/pending`);
    const pendingData = await pendingResponse.json();
    console.log('   📱 Pending SMS messages:', pendingData);

    // Test statistics
    const statsResponse = await fetch(`${baseUrl}/sms/stats`);
    const statsData = await statsResponse.json();
    console.log('   📊 SMS statistics:', statsData);

  } catch (error) {
    console.error('❌ Error creating test data:', error);
  }
}

// Run the test
createTestSMSData();
