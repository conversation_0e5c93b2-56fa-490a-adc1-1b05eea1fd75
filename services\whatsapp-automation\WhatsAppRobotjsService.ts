/**
 * WhatsApp Desktop Automation Service using robotjs
 * Provides automated bulk messaging through WhatsApp Desktop application
 * 
 * @fileoverview Pure robotjs implementation for WhatsApp Desktop automation
 * <AUTHOR> Development Team
 * @version 2.0.0 - Pure robotjs implementation
 */

import * as robot from 'robotjs';

// Configure robotjs for optimal performance
robot.setKeyboardDelay(50);
robot.setMouseDelay(50);

export interface WhatsAppAutomationConfig {
  messageDelay: number; // Delay between messages (ms)
  searchDelay: number; // Delay for contact search (ms)
  typeDelay: number; // Delay between keystrokes (ms)
  retryAttempts: number; // Retry attempts for failed operations
  safeMode: boolean; // Extra validation and delays
}

export interface ContactMessage {
  phone: string;
  message: string;
  name?: string;
}

export interface AutomationResult {
  success: boolean;
  messageId?: string;
  error?: string;
  timestamp: Date;
}

export interface BulkAutomationResult {
  total: number;
  successful: number;
  failed: number;
  results: AutomationResult[];
  sessionId: string;
}

/**
 * WhatsApp Desktop Automation Service using robotjs
 * Provides automated bulk messaging through WhatsApp Desktop application
 */
export class WhatsAppRobotjsService {
  private config: WhatsAppAutomationConfig;
  private isRunning: boolean = false;
  private currentSession: string | null = null;
  private progressCallback?: (current: number, total: number, contact?: ContactMessage) => void;

  constructor(config: Partial<WhatsAppAutomationConfig> = {}) {
    this.config = {
      messageDelay: 3000, // 3 seconds between messages
      searchDelay: 2000, // 2 seconds for search
      typeDelay: 100, // 100ms between keystrokes
      retryAttempts: 3,
      safeMode: true,
      ...config
    };
  }

  /**
   * Update automation configuration
   */
  updateConfig(newConfig: Partial<WhatsAppAutomationConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Update robotjs delays
    robot.setKeyboardDelay(this.config.typeDelay);
    robot.setMouseDelay(50);
  }

  /**
   * Check if WhatsApp Desktop is available and accessible
   */
  async checkWhatsAppDesktop(): Promise<{ available: boolean; message: string }> {
    try {
      // Try to find WhatsApp window
      const screenSize = robot.getScreenSize();
      console.log(`🖥️ Screen size: ${screenSize.width}x${screenSize.height}`);
      
      // Basic system check
      if (screenSize.width < 800 || screenSize.height < 600) {
        return {
          available: false,
          message: 'Screen resolution too small for automation'
        };
      }

      return {
        available: true,
        message: 'System ready for WhatsApp Desktop automation'
      };
    } catch (error) {
      return {
        available: false,
        message: `System check failed: ${error.message}`
      };
    }
  }

  /**
   * Send a single message to a contact
   */
  async sendSingleMessage(contact: ContactMessage): Promise<AutomationResult> {
    const startTime = Date.now();

    try {
      console.log(`🤖 Starting automation for ${contact.phone}`);
      
      // Step 1: Focus WhatsApp Desktop
      await this.focusWhatsAppDesktop();
      
      // Step 2: Open new chat or search for contact
      await this.openNewChat();
      
      // Step 3: Search for contact
      await this.searchContact(contact.phone);
      
      // Step 4: Type and send message
      await this.typeMessage(contact.message);
      await this.sendMessage();

      // Step 5: Wait for message to be sent
      await this.waitForMessageSent();

      return {
        success: true,
        messageId: this.generateMessageId(),
        timestamp: new Date()
      };

    } catch (error) {
      console.error(`❌ Automation failed for ${contact.phone}:`, error);
      return {
        success: false,
        error: error.message,
        timestamp: new Date()
      };
    }
  }

  /**
   * Send bulk messages with progress tracking
   */
  async sendBulkMessages(
    contacts: ContactMessage[],
    progressCallback?: (current: number, total: number, contact?: ContactMessage) => void
  ): Promise<BulkAutomationResult> {
    this.isRunning = true;
    this.currentSession = this.generateSessionId();
    this.progressCallback = progressCallback;

    const results: AutomationResult[] = [];
    let successful = 0;
    let failed = 0;

    console.log(`🚀 Starting bulk automation for ${contacts.length} contacts`);

    try {
      for (let i = 0; i < contacts.length && this.isRunning; i++) {
        const contact = contacts[i];
        
        // Update progress
        if (this.progressCallback) {
          this.progressCallback(i + 1, contacts.length, contact);
        }

        console.log(`📱 Processing ${i + 1}/${contacts.length}: ${contact.phone}`);

        // Send message with retry logic
        let result: AutomationResult;
        let attempts = 0;
        
        do {
          attempts++;
          result = await this.sendSingleMessage(contact);
          
          if (!result.success && attempts < this.config.retryAttempts) {
            console.log(`🔄 Retry ${attempts}/${this.config.retryAttempts} for ${contact.phone}`);
            await this.delay(1000); // Wait before retry
          }
        } while (!result.success && attempts < this.config.retryAttempts);

        results.push(result);
        
        if (result.success) {
          successful++;
          console.log(`✅ Message sent to ${contact.phone}`);
        } else {
          failed++;
          console.log(`❌ Failed to send to ${contact.phone}: ${result.error}`);
        }

        // Delay between messages (except for the last one)
        if (i < contacts.length - 1) {
          console.log(`⏳ Waiting ${this.config.messageDelay}ms before next message...`);
          await this.delay(this.config.messageDelay);
        }
      }

    } catch (error) {
      console.error('❌ Bulk automation failed:', error);
    } finally {
      this.isRunning = false;
      this.currentSession = null;
    }

    const finalResult = {
      total: contacts.length,
      successful,
      failed,
      results,
      sessionId: this.currentSession || 'unknown'
    };

    console.log(`🏁 Bulk automation completed: ${successful} successful, ${failed} failed`);
    return finalResult;
  }

  /**
   * Stop the current automation session
   */
  stopAutomation(): void {
    console.log('🛑 Stopping automation...');
    this.isRunning = false;
  }

  /**
   * Focus WhatsApp Desktop application
   */
  private async focusWhatsAppDesktop(): Promise<void> {
    try {
      // Try to focus WhatsApp using Alt+Tab or clicking on taskbar
      // This is a simplified approach - in production, you might want to use window detection
      
      // Method 1: Try Ctrl+Shift+W (WhatsApp Web shortcut if available)
      robot.keyTap('w', ['ctrl', 'shift']);
      await this.delay(1000);
      
      // Method 2: Try clicking on center of screen (assuming WhatsApp is open)
      const screenSize = robot.getScreenSize();
      robot.moveMouse(screenSize.width / 2, screenSize.height / 2);
      robot.mouseClick();
      
      await this.delay(this.config.safeMode ? 2000 : 1000);
      
    } catch (error) {
      throw new Error(`Failed to focus WhatsApp Desktop: ${error.message}`);
    }
  }

  /**
   * Open new chat dialog
   */
  private async openNewChat(): Promise<void> {
    try {
      // Use Ctrl+N to open new chat (WhatsApp Desktop shortcut)
      robot.keyTap('n', 'ctrl');
      await this.delay(this.config.searchDelay);
      
    } catch (error) {
      throw new Error(`Failed to open new chat: ${error.message}`);
    }
  }

  /**
   * Search for a contact by phone number
   */
  private async searchContact(phone: string): Promise<void> {
    try {
      // Clear any existing text in search box
      robot.keyTap('a', 'ctrl'); // Select all
      await this.delay(100);
      
      // Type the phone number
      const formattedPhone = this.formatPhoneForSearch(phone);
      await this.typeText(formattedPhone);
      
      // Wait for search results
      await this.delay(this.config.searchDelay);
      
      // Press Enter to select first result
      robot.keyTap('enter');
      await this.delay(1000);
      
    } catch (error) {
      throw new Error(`Failed to search for contact ${phone}: ${error.message}`);
    }
  }

  /**
   * Type message content
   */
  private async typeMessage(message: string): Promise<void> {
    try {
      // Wait for chat to load
      await this.delay(1000);
      
      // Type the message
      await this.typeText(message);
      
      // Small delay before sending
      await this.delay(500);
      
    } catch (error) {
      throw new Error(`Failed to type message: ${error.message}`);
    }
  }

  /**
   * Send the typed message
   */
  private async sendMessage(): Promise<void> {
    try {
      // Press Enter to send message
      robot.keyTap('enter');
      await this.delay(500);
      
    } catch (error) {
      throw new Error(`Failed to send message: ${error.message}`);
    }
  }

  /**
   * Wait for message to be sent (check for delivery indicators)
   */
  private async waitForMessageSent(): Promise<void> {
    // Simple delay - in production, you might want to check for visual indicators
    await this.delay(this.config.safeMode ? 2000 : 1000);
  }

  /**
   * Type text with configurable delay between characters
   */
  private async typeText(text: string): Promise<void> {
    for (const char of text) {
      robot.typeString(char);
      if (this.config.typeDelay > 0) {
        await this.delay(this.config.typeDelay);
      }
    }
  }

  /**
   * Format phone number for search
   */
  private formatPhoneForSearch(phone: string): string {
    // Remove all non-digit characters except +
    let formatted = phone.replace(/[^\d+]/g, '');
    
    // Ensure it starts with + if it doesn't already
    if (!formatted.startsWith('+')) {
      formatted = '+' + formatted;
    }
    
    return formatted;
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Generate a unique message ID for tracking
   */
  private generateMessageId(): string {
    return `wa_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  /**
   * Generate a unique session ID for bulk operations
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  /**
   * Get current automation status
   */
  getStatus(): { isRunning: boolean; currentSession: string | null } {
    return {
      isRunning: this.isRunning,
      currentSession: this.currentSession
    };
  }
}

// Export singleton instance
export const whatsappRobotjsService = new WhatsAppRobotjsService();
export default whatsappRobotjsService;
