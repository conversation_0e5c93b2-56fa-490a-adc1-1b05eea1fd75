<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="20dp"
    android:gravity="center">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="CRM SMS Sender"
        android:textSize="24sp"
        android:textStyle="bold"
        android:layout_marginBottom="20dp" />

    <TextView
        android:id="@+id/statusText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Ready to send SMS"
        android:textSize="16sp"
        android:textAlignment="center"
        android:padding="15dp"
        android:background="#f0f0f0"
        android:layout_marginBottom="30dp" />

    <Button
        android:id="@+id/sendButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="📱 Send Pending SMS"
        android:textSize="18sp"
        android:padding="15dp"
        android:background="#007bff"
        android:textColor="white" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Instructions:\n1. Make sure your phone is connected to WiFi\n2. Grant SMS permissions when prompted\n3. Click 'Send Pending SMS' to process messages\n4. Messages will be sent automatically"
        android:textSize="14sp"
        android:layout_marginTop="30dp"
        android:padding="15dp"
        android:background="#e9ecef"
        android:textColor="#6c757d" />

</LinearLayout>
