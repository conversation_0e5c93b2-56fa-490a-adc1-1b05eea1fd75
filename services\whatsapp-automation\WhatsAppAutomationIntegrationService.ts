import { whatsappModalService } from '../WhatsAppModalService';

interface ContactMessage {
  phone: string;
  message: string;
  name?: string;
}

interface CampaignAutomationRequest {
  campaignId: string;
  campaignName?: string;
  subscribers: Array<{
    phone: string;
    name?: string;
  }>;
  messageTemplate: string;
  placeholderData?: Record<string, any>;
}

interface BulkAutomationResult {
  total: number;
  successful: number;
  failed: number;
  results: Array<{
    contact: ContactMessage;
    result: {
      success: boolean;
      messageId?: string;
      error?: string;
      timestamp: Date;
    };
  }>;
  duration: number;
  avgTimePerMessage: number;
}

interface AutomationProgress {
  status: 'initializing' | 'running' | 'completed' | 'error';
  current: number;
  total: number;
  successCount: number;
  failureCount: number;
  currentContact?: ContactMessage;
}

interface AutomationPreferences {
  enableNutjsAutomation: boolean;
  fallbackToManual: boolean;
  autoRetryFailures: boolean;
  maxBatchSize: number;
  confirmBeforeSending: boolean;
  screenshotLogging: boolean;
  safeMode: boolean;
  messageDelay: number;
  searchDelay: number;
  typeDelay: number;
  retryAttempts: number;
}

class WhatsAppAutomationIntegrationService {
  private currentProgress: AutomationProgress | null = null;
  private progressCallbacks: Array<(progress: AutomationProgress) => void> = [];
  private preferences: AutomationPreferences;

  constructor() {
    this.preferences = this.getDefaultPreferences();
    this.loadPreferences();
  }

  async sendCampaign(request: CampaignAutomationRequest): Promise<BulkAutomationResult> {
    try {
      this.validateCampaignRequest(request);
      const contacts = this.prepareContactMessages(request);
      const useAutomation = this.shouldUseAutomation(contacts.length);

      // Initialize progress tracking
      this.currentProgress = {
        status: 'initializing',
        current: 0,
        total: contacts.length,
        successCount: 0,
        failureCount: 0
      };

      // Show confirmation if enabled
      if (this.preferences.confirmBeforeSending) {
        const confirmed = await this.showSendConfirmation(request, useAutomation);
        if (!confirmed) {
          throw new Error('Campaign cancelled by user');
        }
      }

      let result: BulkAutomationResult;
      
      if (useAutomation) {
        // Use WhatsApp Web automation via API
        result = await this.sendWithWhatsAppWebAutomation(contacts, request);
      } else {
        result = await this.sendWithManualMethod(contacts, request);
      }

      // Update final progress
      if (this.currentProgress) {
        this.currentProgress.status = 'completed';
        this.currentProgress.successCount = result.successful;
        this.currentProgress.failureCount = result.failed;
        this.notifyProgressListeners();
      }

      // Log campaign results
      await this.logCampaignResults(request, result);

      return result;
    } catch (error) {
      if (this.currentProgress) {
        this.currentProgress.status = 'error';
        this.notifyProgressListeners();
      }
      throw error;
    } finally {
      // Clear progress after delay
      setTimeout(() => {
        this.currentProgress = null;
      }, 5000);
    }
  }

  /**
   * Send campaign using WhatsApp Web automation
   */
  private async sendWithWhatsAppWebAutomation(
    contacts: ContactMessage[],
    request: CampaignAutomationRequest
  ): Promise<BulkAutomationResult> {
    const startTime = Date.now();
    const results: BulkAutomationResult['results'] = [];
    let successful = 0;
    let failed = 0;

    console.log(`🤖 Starting WhatsApp Web automation for ${contacts.length} messages`);

    try {
      // Initialize WhatsApp Web automation
      const initResponse = await fetch('/api/whatsapp-automation/initialize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!initResponse.ok) {
        throw new Error('Failed to initialize WhatsApp Web automation');
      }

      // Start automation session
      const sessionResponse = await fetch('/api/whatsapp-automation/session/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          campaignId: request.campaignId,
          messageCount: contacts.length,
          estimatedDuration: contacts.length * 5000 // 5 seconds per message
        })
      });

      const sessionData = await sessionResponse.json();
      const sessionId = sessionData.sessionId;

      // Process each message
      for (let i = 0; i < contacts.length; i++) {
        const contact = contacts[i];

        try {
          // Update progress
          if (this.currentProgress) {
            this.currentProgress.current = i + 1;
            this.currentProgress.total = contacts.length;
            this.currentProgress.currentContact = contact;
            this.notifyProgressListeners();
          }

          // Send message via WhatsApp Web automation
          const sendResponse = await fetch('/api/whatsapp-automation/send-message', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              sessionId,
              phone: contact.phone,
              message: contact.message
            })
          });

          const sendResult = await sendResponse.json();

          if (sendResult.success) {
            results.push({
              contact,
              result: {
                success: true,
                messageId: this.generateMessageId(),
                timestamp: new Date()
              }
            });
            successful++;
          } else {
            throw new Error(sendResult.message || 'Failed to send message');
          }

        } catch (error) {
          results.push({
            contact,
            result: {
              success: false,
              error: error instanceof Error ? error.message : 'WhatsApp Web automation failed',
              timestamp: new Date()
            }
          });
          failed++;
        }

        // Update session progress
        await fetch(`/api/whatsapp-automation/session/${sessionId}/progress`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            current: i + 1,
            successful,
            failed,
            status: 'running',
            currentContact: contact
          })
        });
      }

      // Complete session
      await fetch(`/api/whatsapp-automation/session/${sessionId}/complete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          results: { successful, failed, total: contacts.length },
          duration: Date.now() - startTime
        })
      });

    } catch (error) {
      console.error('WhatsApp Web automation error:', error);
      // Mark remaining messages as failed
      for (let i = results.length; i < contacts.length; i++) {
        results.push({
          contact: contacts[i],
          result: {
            success: false,
            error: 'Automation stopped due to error',
            timestamp: new Date()
          }
        });
        failed++;
      }
    }

    const duration = Date.now() - startTime;
    const avgTimePerMessage = duration / contacts.length;

    return {
      total: contacts.length,
      successful,
      failed,
      results,
      duration,
      avgTimePerMessage
    };
  }

  /**
   * Send campaign using manual method (existing modal system)
   */
  private async sendWithManualMethod(
    contacts: ContactMessage[],
    request: CampaignAutomationRequest
  ): Promise<BulkAutomationResult> {
    const startTime = Date.now();
    const results: BulkAutomationResult['results'] = [];
    let successful = 0;
    let failed = 0;

    console.log(`📱 Starting manual WhatsApp sending for ${contacts.length} messages`);

    try {
      // Show method selection modal
      const method = await whatsappModalService.showMethodSelection(contacts.length);
      
      if (method === 'cancel') {
        throw new Error('Campaign cancelled by user');
      }

      // Process each message manually
      for (let i = 0; i < contacts.length; i++) {
        const contact = contacts[i];
        
        try {
          // Show message info modal
          await whatsappModalService.showMessageInfo(
            i + 1,
            contacts.length,
            contact.phone,
            contact.message.substring(0, 100) + '...'
          );

          // Open WhatsApp with message
          const whatsappUrl = `https://wa.me/${contact.phone.replace(/\D/g, '')}?text=${encodeURIComponent(contact.message)}`;
          window.open(whatsappUrl, '_blank');

          // Show progress modal with delay
          await whatsappModalService.showProgress(
            i + 1,
            contacts.length,
            `Message ${i + 1} of ${contacts.length} opened in WhatsApp`,
            5
          );

          results.push({
            contact,
            result: {
              success: true,
              messageId: this.generateMessageId(),
              timestamp: new Date()
            }
          });
          successful++;

        } catch (error) {
          results.push({
            contact,
            result: {
              success: false,
              error: error instanceof Error ? error.message : 'Manual sending failed',
              timestamp: new Date()
            }
          });
          failed++;
        }
      }

      // Show completion modal
      await whatsappModalService.showCompletion(
        'Campaign Completed',
        `Successfully processed ${successful} messages, ${failed} failed.`
      );

    } catch (error) {
      // Mark remaining messages as failed
      for (let i = results.length; i < contacts.length; i++) {
        results.push({
          contact: contacts[i],
          result: {
            success: false,
            error: 'Campaign stopped due to error',
            timestamp: new Date()
          }
        });
        failed++;
      }
    }

    const duration = Date.now() - startTime;
    const avgTimePerMessage = duration / contacts.length;

    return {
      total: contacts.length,
      successful,
      failed,
      results,
      duration,
      avgTimePerMessage
    };
  }

  /**
   * Validate campaign request
   */
  private validateCampaignRequest(request: CampaignAutomationRequest): void {
    if (!request.subscribers || request.subscribers.length === 0) {
      throw new Error('No subscribers provided for campaign');
    }

    if (!request.messageTemplate || request.messageTemplate.trim() === '') {
      throw new Error('Message template is required');
    }

    // Validate phone numbers
    const invalidPhones = request.subscribers.filter(sub => !this.isValidPhoneNumber(sub.phone));
    if (invalidPhones.length > 0) {
      throw new Error(`Invalid phone numbers found: ${invalidPhones.map(s => s.phone).join(', ')}`);
    }

    // Check batch size limits
    if (request.subscribers.length > this.preferences.maxBatchSize) {
      throw new Error(`Campaign size (${request.subscribers.length}) exceeds maximum batch size (${this.preferences.maxBatchSize})`);
    }
  }

  /**
   * Prepare contact messages from campaign request
   */
  private prepareContactMessages(request: CampaignAutomationRequest): ContactMessage[] {
    return request.subscribers.map(subscriber => {
      // Process message template with placeholders
      let processedMessage = request.messageTemplate;
      
      // Replace common placeholders
      processedMessage = processedMessage.replace(/\{name\}/g, subscriber.name || 'Client');
      processedMessage = processedMessage.replace(/\{phone\}/g, subscriber.phone);
      
      // Replace custom placeholders if provided
      if (request.placeholderData) {
        Object.entries(request.placeholderData).forEach(([key, value]) => {
          const placeholder = new RegExp(`\\{${key}\\}`, 'g');
          processedMessage = processedMessage.replace(placeholder, String(value));
        });
      }

      return {
        phone: this.formatPhoneNumber(subscriber.phone),
        message: processedMessage,
        name: subscriber.name
      };
    });
  }

  /**
   * Determine if automation should be used based on message count and preferences
   */
  private shouldUseAutomation(messageCount: number): boolean {
    if (!this.preferences.enableNutjsAutomation) {
      return false;
    }

    // Use automation for larger batches
    if (messageCount >= 5) {
      return true;
    }

    // For smaller batches, check if manual fallback is preferred
    return !this.preferences.fallbackToManual;
  }

  /**
   * Show confirmation dialog before sending
   */
  private async showSendConfirmation(
    request: CampaignAutomationRequest,
    useAutomation: boolean
  ): Promise<boolean> {
    const method = useAutomation ? 'Automated (WhatsApp Web)' : 'Manual';
    const message = {
      type: 'send-confirmation',
      campaign: request.campaignName || 'Unnamed Campaign',
      recipients: request.subscribers.length,
      method,
      preview: request.messageTemplate.substring(0, 150) + '...'
    };

    try {
      await whatsappModalService.showInstruction(
        'Confirm Campaign Sending',
        message
      );
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Log campaign results for audit trail
   */
  private async logCampaignResults(
    request: CampaignAutomationRequest,
    result: BulkAutomationResult
  ): Promise<void> {
    try {
      const logEntry = {
        timestamp: new Date().toISOString(),
        campaignId: request.campaignId,
        campaignName: request.campaignName,
        method: 'automation',
        totalMessages: result.total,
        successful: result.successful,
        failed: result.failed,
        duration: result.duration,
        avgTimePerMessage: result.avgTimePerMessage
      };

      // Log to backend if available
      const response = await fetch('/api/campaigns/log-sending', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(logEntry)
      });

      if (!response.ok) {
        console.warn('Failed to log campaign results to backend');
      }

    } catch (error) {
      console.error('Error logging campaign results:', error);
    }
  }

  /**
   * Notify progress listeners
   */
  private notifyProgressListeners(): void {
    if (this.currentProgress) {
      this.progressCallbacks.forEach(callback => {
        try {
          callback(this.currentProgress!);
        } catch (error) {
          console.error('Error in progress callback:', error);
        }
      });
    }
  }

  /**
   * Format phone number for WhatsApp
   */
  private formatPhoneNumber(phone: string): string {
    // Remove all non-digit characters
    const cleaned = phone.replace(/\D/g, '');
    
    // Add country code if not present (assuming India +91)
    if (cleaned.length === 10) {
      return `+91${cleaned}`;
    } else if (cleaned.length === 12 && cleaned.startsWith('91')) {
      return `+${cleaned}`;
    } else if (cleaned.startsWith('91') && cleaned.length === 12) {
      return `+${cleaned}`;
    }
    
    return cleaned.startsWith('+') ? phone : `+${cleaned}`;
  }

  /**
   * Validate phone number format
   */
  private isValidPhoneNumber(phone: string): boolean {
    const cleaned = phone.replace(/\D/g, '');
    return cleaned.length >= 10 && cleaned.length <= 15;
  }

  /**
   * Generate unique message ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Get default preferences
   */
  private getDefaultPreferences(): AutomationPreferences {
    return {
      enableNutjsAutomation: false, // Disabled by default for safety
      fallbackToManual: true,
      autoRetryFailures: true,
      maxBatchSize: 100,
      confirmBeforeSending: true,
      screenshotLogging: true,
      safeMode: true,
      messageDelay: 3000,
      searchDelay: 2000,
      typeDelay: 50,
      retryAttempts: 3
    };
  }

  /**
   * Load preferences from storage
   */
  private async loadPreferences(): Promise<void> {
    try {
      const response = await fetch('/api/settings/whatsapp-automation');
      if (response.ok) {
        const saved = await response.json();
        this.preferences = { ...this.preferences, ...saved };
      }
    } catch (error) {
      console.warn('Failed to load automation preferences, using defaults');
    }
  }

  /**
   * Save preferences to storage
   */
  private async savePreferences(): Promise<void> {
    try {
      await fetch('/api/settings/whatsapp-automation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(this.preferences)
      });
    } catch (error) {
      console.error('Failed to save automation preferences:', error);
    }
  }

  /**
   * Get automation statistics
   */
  async getPreferences(): Promise<AutomationPreferences> {
    await this.loadPreferences();
    return this.preferences;
  }

  async updatePreferences(newPreferences: Partial<AutomationPreferences>): Promise<void> {
    this.preferences = { ...this.preferences, ...newPreferences };
    await this.savePreferences();
  }

  async getAutomationStats(): Promise<{
    totalCampaigns: number;
    totalMessages: number;
    successRate: number;
    automationUsageRate: number;
  }> {
    try {
      const response = await fetch('/api/campaigns/automation-stats');
      if (response.ok) {
        return await response.json();
      }
    } catch (error) {
      console.error('Failed to fetch automation stats:', error);
    }

    return {
      totalCampaigns: 0,
      totalMessages: 0,
      successRate: 0,
      automationUsageRate: 0
    };
  }

  /**
   * Check if nut.js dependencies are available
   */
  static async checkDependencies(): Promise<{
    nutjsAvailable: boolean;
    systemCompatible: boolean;
    whatsappDesktopDetected: boolean;
    recommendations: string[];
  }> {
    const recommendations: string[] = [];
    let nutjsAvailable = false;
    let systemCompatible = false;
    let whatsappDesktopDetected = false;

    try {
      // Check if nut.js is available
      await import('@nut-tree-fork/nut-js');
      nutjsAvailable = true;
    } catch (error) {
      recommendations.push('Install nut.js: npm install @nut-tree/nut-js');
    }

    // Check system compatibility
    const platform = process.platform;
    systemCompatible = ['win32', 'darwin', 'linux'].includes(platform);
    
    if (!systemCompatible) {
      recommendations.push(`Platform ${platform} may not be fully supported`);
    }

    // Basic WhatsApp Desktop detection
    try {
      // This is a simplified check - actual implementation would be more sophisticated
      whatsappDesktopDetected = true; // Placeholder
    } catch (error) {
      recommendations.push('Install WhatsApp Desktop application');
    }

    if (nutjsAvailable && !whatsappDesktopDetected) {
      recommendations.push('Ensure WhatsApp Desktop is running before using automation');
    }

    return {
      nutjsAvailable,
      systemCompatible,
      whatsappDesktopDetected,
      recommendations
    };
  }
}

// Create singleton instance
export const whatsappAutomationIntegration = new WhatsAppAutomationIntegrationService();

export default whatsappAutomationIntegration;
