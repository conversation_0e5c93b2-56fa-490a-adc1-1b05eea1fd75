/**
 * WhatsApp Automation API Routes
 * Backend routes for nut.js WhatsApp automation functionality
 * 
 * @fileoverview API endpoints for WhatsApp Desktop automation
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import express from 'express';
import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import { whatsappWebAutomation } from '../services/WhatsAppWebAutomationService.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const router = express.Router();

// Store automation sessions and preferences in memory
// In production, consider using a database or Redis
let automationSessions = new Map();
let automationPreferences = {
  enableRobotjsAutomation: false,
  fallbackToManual: true,
  autoRetryFailures: true,
  maxBatchSize: 100,
  confirmBeforeSending: true,
  screenshotLogging: true,
  safeMode: true
};

let automationStats = {
  totalCampaigns: 0,
  totalMessages: 0,
  successfulMessages: 0,
  failedMessages: 0,
  totalSessions: 0
};

/**
 * Get automation preferences
 */
router.get('/preferences', async (req, res) => {
  try {
    // Try to load from file if it exists
    const prefsPath = path.join(__dirname, '../../data/automation-preferences.json');
    try {
      const data = await fs.readFile(prefsPath, 'utf8');
      automationPreferences = { ...automationPreferences, ...JSON.parse(data) };
    } catch (error) {
      // File doesn't exist or is invalid, use defaults
    }

    res.json(automationPreferences);
  } catch (error) {
    console.error('Error getting automation preferences:', error);
    res.status(500).json({ error: 'Failed to get preferences' });
  }
});

/**
 * Update automation preferences
 */
router.post('/preferences', async (req, res) => {
  try {
    const newPreferences = req.body;
    
    // Validate preferences
    if (typeof newPreferences !== 'object') {
      return res.status(400).json({ error: 'Invalid preferences format' });
    }

    // Update preferences
    automationPreferences = { ...automationPreferences, ...newPreferences };

    // Save to file
    const dataDir = path.join(__dirname, '../../data');
    await fs.mkdir(dataDir, { recursive: true });
    
    const prefsPath = path.join(dataDir, 'automation-preferences.json');
    await fs.writeFile(prefsPath, JSON.stringify(automationPreferences, null, 2));

    res.json({ success: true, preferences: automationPreferences });
  } catch (error) {
    console.error('Error updating automation preferences:', error);
    res.status(500).json({ error: 'Failed to update preferences' });
  }
});

/**
 * Check system dependencies and requirements
 */
router.get('/system-check', async (req, res) => {
  try {
    const results = {
      robotjsAvailable: false,
      systemCompatible: false,
      whatsappDesktopDetected: false,
      recommendations: []
    };

    // Check if WhatsApp Web automation is available
    try {
      // Check if Puppeteer is available
      const puppeteer = await import('puppeteer');
      results.robotjsAvailable = true; // Keep same property name for compatibility
      results.puppeteerAvailable = true;
      results.automationType = 'WhatsApp Web (Puppeteer)';
      console.log('✅ WhatsApp Web automation available');
    } catch (error) {
      console.error('❌ Puppeteer import failed:', error.message);
      results.recommendations.push('Install puppeteer: npm install puppeteer');
      results.recommendations.push(`Error: ${error.message}`);
    }

    // Check system compatibility
    const platform = process.platform;
    results.systemCompatible = ['win32', 'darwin', 'linux'].includes(platform);
    
    if (!results.systemCompatible) {
      results.recommendations.push(`Platform ${platform} may have limited support`);
    }

    // Basic WhatsApp Desktop detection (placeholder - actual implementation would be more sophisticated)
    results.whatsappDesktopDetected = true; // Simplified for now

    if (results.robotjsAvailable && !results.whatsappDesktopDetected) {
      results.recommendations.push('Ensure WhatsApp Desktop is installed and running');
    }

    // Additional system checks
    if (process.platform === 'linux') {
      results.recommendations.push('Linux users may need to install additional dependencies for screen automation');
    }

    res.json(results);
  } catch (error) {
    console.error('Error performing system check:', error);
    res.status(500).json({ error: 'Failed to perform system check' });
  }
});

/**
 * Initialize WhatsApp Web automation
 */
router.post('/initialize', async (req, res) => {
  try {
    console.log('🚀 Initializing WhatsApp Web automation...');

    const result = await whatsappWebAutomation.initialize();

    res.json({
      success: true,
      message: 'WhatsApp Web automation initialized. Please scan QR code if prompted.',
      initialized: result
    });
  } catch (error) {
    console.error('❌ Failed to initialize WhatsApp Web automation:', error);
    res.status(500).json({
      success: false,
      message: `Initialization failed: ${error.message}`
    });
  }
});

/**
 * Send a single message via WhatsApp Web automation
 */
router.post('/send-message', async (req, res) => {
  try {
    const { sessionId, phone, message } = req.body;

    if (!phone || !message) {
      return res.status(400).json({
        success: false,
        message: 'Phone number and message are required'
      });
    }

    console.log(`📱 Sending message to ${phone} via WhatsApp Web`);

    const result = await whatsappWebAutomation.sendMessage(phone, message);

    res.json({
      success: result.success,
      message: result.success ? 'Message sent successfully' : result.error,
      result: result
    });
  } catch (error) {
    console.error('❌ Failed to send message:', error);
    res.status(500).json({
      success: false,
      message: `Failed to send message: ${error.message}`
    });
  }
});

/**
 * Test automation functionality
 */
router.post('/test', async (req, res) => {
  try {
    if (!automationPreferences.enableRobotjsAutomation) {
      return res.json({
        success: false,
        message: 'Automation is disabled in preferences'
      });
    }

    // Basic test - check if WhatsApp Web automation is available
    try {
      const status = await whatsappWebAutomation.getStatus();
      console.log('✅ WhatsApp Web automation test successful:', status);

      res.json({
        success: true,
        message: 'WhatsApp Web automation system is functional',
        automationType: 'WhatsApp Web (Puppeteer)',
        status: status
      });
    } catch (error) {
      console.error('❌ WhatsApp Web automation test failed:', error.message);
      res.json({
        success: false,
        message: `Automation test failed: ${error.message}`
      });
    }
  } catch (error) {
    console.error('Error testing automation:', error);
    res.status(500).json({ error: 'Failed to test automation' });
  }
});

/**
 * Start automation session
 */
router.post('/session/start', async (req, res) => {
  try {
    const { campaignId, messageCount, estimatedDuration } = req.body;
    
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    
    const session = {
      id: sessionId,
      campaignId,
      messageCount,
      estimatedDuration,
      startTime: new Date(),
      status: 'initializing',
      progress: {
        current: 0,
        total: messageCount,
        successful: 0,
        failed: 0
      }
    };

    automationSessions.set(sessionId, session);
    automationStats.totalSessions++;

    res.json({
      success: true,
      sessionId,
      session
    });
  } catch (error) {
    console.error('Error starting automation session:', error);
    res.status(500).json({ error: 'Failed to start session' });
  }
});

/**
 * Update session progress
 */
router.post('/session/:sessionId/progress', async (req, res) => {
  try {
    const { sessionId } = req.params;
    const { current, successful, failed, status, currentContact } = req.body;

    const session = automationSessions.get(sessionId);
    if (!session) {
      return res.status(404).json({ error: 'Session not found' });
    }

    // Update session progress
    session.progress.current = current;
    session.progress.successful = successful;
    session.progress.failed = failed;
    session.status = status;
    session.currentContact = currentContact;
    session.lastUpdate = new Date();

    automationSessions.set(sessionId, session);

    res.json({ success: true, session });
  } catch (error) {
    console.error('Error updating session progress:', error);
    res.status(500).json({ error: 'Failed to update progress' });
  }
});

/**
 * Complete automation session
 */
router.post('/session/:sessionId/complete', async (req, res) => {
  try {
    const { sessionId } = req.params;
    const { results, duration } = req.body;

    const session = automationSessions.get(sessionId);
    if (!session) {
      return res.status(404).json({ error: 'Session not found' });
    }

    // Update session with final results
    session.status = 'completed';
    session.endTime = new Date();
    session.duration = duration;
    session.results = results;

    // Update global stats
    automationStats.totalCampaigns++;
    automationStats.totalMessages += session.progress.total;
    automationStats.successfulMessages += session.progress.successful;
    automationStats.failedMessages += session.progress.failed;

    automationSessions.set(sessionId, session);

    // Archive session after some time (optional)
    setTimeout(() => {
      automationSessions.delete(sessionId);
    }, 24 * 60 * 60 * 1000); // 24 hours

    res.json({ success: true, session });
  } catch (error) {
    console.error('Error completing session:', error);
    res.status(500).json({ error: 'Failed to complete session' });
  }
});

/**
 * Get session status
 */
router.get('/session/:sessionId', async (req, res) => {
  try {
    const { sessionId } = req.params;
    const session = automationSessions.get(sessionId);

    if (!session) {
      return res.status(404).json({ error: 'Session not found' });
    }

    res.json(session);
  } catch (error) {
    console.error('Error getting session:', error);
    res.status(500).json({ error: 'Failed to get session' });
  }
});

/**
 * Stop automation session
 */
router.post('/session/:sessionId/stop', async (req, res) => {
  try {
    const { sessionId } = req.params;
    const session = automationSessions.get(sessionId);

    if (!session) {
      return res.status(404).json({ error: 'Session not found' });
    }

    session.status = 'cancelled';
    session.endTime = new Date();
    automationSessions.set(sessionId, session);

    res.json({ success: true, session });
  } catch (error) {
    console.error('Error stopping session:', error);
    res.status(500).json({ error: 'Failed to stop session' });
  }
});

/**
 * Get automation statistics
 */
router.get('/stats', async (req, res) => {
  try {
    const successRate = automationStats.totalMessages > 0 
      ? (automationStats.successfulMessages / automationStats.totalMessages) * 100 
      : 0;

    const stats = {
      ...automationStats,
      successRate: parseFloat(successRate.toFixed(2)),
      activeSessions: automationSessions.size,
      averageMessagesPerCampaign: automationStats.totalCampaigns > 0 
        ? Math.round(automationStats.totalMessages / automationStats.totalCampaigns)
        : 0
    };

    res.json(stats);
  } catch (error) {
    console.error('Error getting automation stats:', error);
    res.status(500).json({ error: 'Failed to get stats' });
  }
});

/**
 * Get active sessions
 */
router.get('/sessions', async (req, res) => {
  try {
    const sessions = Array.from(automationSessions.values());
    res.json(sessions);
  } catch (error) {
    console.error('Error getting sessions:', error);
    res.status(500).json({ error: 'Failed to get sessions' });
  }
});

/**
 * Log campaign sending result
 */
router.post('/log-campaign', async (req, res) => {
  try {
    const logEntry = req.body;
    
    // Validate log entry
    if (!logEntry.campaignId || !logEntry.totalMessages) {
      return res.status(400).json({ error: 'Invalid log entry' });
    }

    // Save to log file
    const logsDir = path.join(__dirname, '../../logs');
    await fs.mkdir(logsDir, { recursive: true });
    
    const logFile = path.join(logsDir, 'automation-campaigns.log');
    const logLine = JSON.stringify({
      ...logEntry,
      timestamp: new Date().toISOString()
    }) + '\n';
    
    await fs.appendFile(logFile, logLine);

    // Update stats
    if (logEntry.method === 'automation') {
      automationStats.totalCampaigns++;
      automationStats.totalMessages += logEntry.totalMessages;
      automationStats.successfulMessages += logEntry.successful || 0;
      automationStats.failedMessages += logEntry.failed || 0;
    }

    res.json({ success: true });
  } catch (error) {
    console.error('Error logging campaign:', error);
    res.status(500).json({ error: 'Failed to log campaign' });
  }
});

/**
 * Get automation logs
 */
router.get('/logs', async (req, res) => {
  try {
    const { limit = 50, offset = 0 } = req.query;
    const logFile = path.join(__dirname, '../../logs/automation-campaigns.log');
    
    try {
      const data = await fs.readFile(logFile, 'utf8');
      const lines = data.trim().split('\n').filter(line => line.length > 0);
      
      const logs = lines
        .map(line => {
          try {
            return JSON.parse(line);
          } catch {
            return null;
          }
        })
        .filter(log => log !== null)
        .reverse() // Most recent first
        .slice(parseInt(offset), parseInt(offset) + parseInt(limit));

      res.json({
        logs,
        total: lines.length,
        limit: parseInt(limit),
        offset: parseInt(offset)
      });
    } catch (error) {
      // Log file doesn't exist yet
      res.json({
        logs: [],
        total: 0,
        limit: parseInt(limit),
        offset: parseInt(offset)
      });
    }
  } catch (error) {
    console.error('Error getting logs:', error);
    res.status(500).json({ error: 'Failed to get logs' });
  }
});

/**
 * Clear automation data (for maintenance)
 */
router.post('/clear', async (req, res) => {
  try {
    const { clearSessions, clearStats, clearLogs } = req.body;

    if (clearSessions) {
      automationSessions.clear();
    }

    if (clearStats) {
      automationStats = {
        totalCampaigns: 0,
        totalMessages: 0,
        successfulMessages: 0,
        failedMessages: 0,
        totalSessions: 0
      };
    }

    if (clearLogs) {
      const logFile = path.join(__dirname, '../../logs/automation-campaigns.log');
      try {
        await fs.unlink(logFile);
      } catch (error) {
        // File doesn't exist, ignore
      }
    }

    res.json({ success: true, message: 'Data cleared successfully' });
  } catch (error) {
    console.error('Error clearing data:', error);
    res.status(500).json({ error: 'Failed to clear data' });
  }
});

export default router;
