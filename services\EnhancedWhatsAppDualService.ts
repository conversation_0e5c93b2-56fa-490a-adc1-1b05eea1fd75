/**
 * Enhanced WhatsApp Dual Service with nut.js Integration
 * Replaces manual desktop method with automated nut.js sending
 * 
 * @fileoverview Updated dual service that uses nut.js for desktop automation
 * <AUTHOR> Development Team
 * @version 2.0.0
 */

import { whatsappService } from './WhatsAppService-API';
import { whatsappAutomationIntegration } from './whatsapp-automation/WhatsAppAutomationIntegrationService';
import { whatsappModalService } from './WhatsAppModalService';

// Enhanced types
export interface WhatsAppSendOptions {
  method: 'api' | 'desktop' | 'automation';
  to: string;
  message: string;
  templateName?: string;
  parameters?: string[];
}

export interface WhatsAppSendResult {
  success: boolean;
  messageId?: string;
  status?: string;
  error?: string;
  method: 'api' | 'desktop' | 'automation';
}

export interface WhatsAppBulkResult {
  total: number;
  successful: number;
  failed: number;
  results: Array<{
    phone: string;
    success: boolean;
    messageId?: string;
    error?: string;
    method: 'api' | 'desktop' | 'automation';
  }>;
}

export interface WhatsAppConfiguration {
  preferredMethod: 'api' | 'desktop' | 'automation' | 'both';
  apiConfig: {
    enabled: boolean;
    phoneNumberId: string;
    accessToken: string;
    webhookSecret: string;
    verifyToken?: string;
    defaultCountryCode?: string;
  };
  desktopConfig: {
    enabled: boolean;
    autoLaunch: boolean;
    batchDelay: number;
    confirmEach: boolean;
    maxBatchSize: number;
  };
  automationConfig: {
    enabled: boolean;
    useNutjs: boolean;
    safeMode: boolean;
    maxBatchSize: number;
  };
}

/**
 * Enhanced WhatsApp Service with nut.js automation
 */
class EnhancedWhatsAppDualService {
  private config: WhatsAppConfiguration;

  constructor() {
    this.config = this.getDefaultConfiguration();
    this.loadConfiguration();
  }

  private getDefaultConfiguration(): WhatsAppConfiguration {
    return {
      preferredMethod: 'automation', // Default to automation
      apiConfig: {
        enabled: false,
        phoneNumberId: '',
        accessToken: '',
        webhookSecret: '',
        verifyToken: '',
        defaultCountryCode: '91'
      },
      desktopConfig: {
        enabled: true,
        autoLaunch: true,
        batchDelay: 5,
        confirmEach: true,
        maxBatchSize: 20
      },
      automationConfig: {
        enabled: true,
        useNutjs: true,
        safeMode: true,
        maxBatchSize: 50
      }
    };
  }

  /**
   * Send a single message using the best available method
   */
  async sendMessage(options: WhatsAppSendOptions): Promise<WhatsAppSendResult> {
    try {
      // Auto-select method if not specified
      if (!options.method) {
        options.method = this.selectBestMethod(1);
      }

      switch (options.method) {
        case 'automation':
          return await this.sendViaAutomation(options);
        case 'api':
          return await this.sendViaAPI(options);
        case 'desktop':
          return await this.sendViaDesktop(options);
        default:
          throw new Error('Invalid sending method specified');
      }
    } catch (error) {
      console.error('Failed to send WhatsApp message:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        method: options.method
      };
    }
  }

  /**
   * Send bulk messages with intelligent method selection
   */
  async sendBulkMessages(messages: WhatsAppSendOptions[]): Promise<WhatsAppBulkResult> {
    if (messages.length === 0) {
      return { total: 0, successful: 0, failed: 0, results: [] };
    }

    // Determine the best method for bulk sending
    const bestMethod = this.selectBestMethod(messages.length);
    
    // Update all messages to use the selected method
    const enhancedMessages = messages.map(msg => ({
      ...msg,
      method: msg.method || bestMethod
    }));

    // Group messages by method
    const automationMessages = enhancedMessages.filter(m => m.method === 'automation');
    const apiMessages = enhancedMessages.filter(m => m.method === 'api');
    const desktopMessages = enhancedMessages.filter(m => m.method === 'desktop');

    const results: WhatsAppBulkResult['results'] = [];

    // Send automation messages (preferred for bulk)
    if (automationMessages.length > 0) {
      console.log(`📱 Sending ${automationMessages.length} messages via nut.js automation`);
      try {
        const automationResult = await this.sendBulkViaAutomation(automationMessages);
        results.push(...automationResult.results);
      } catch (error) {
        console.error('Automation bulk sending failed:', error);
        // Mark all automation messages as failed
        results.push(...automationMessages.map(msg => ({
          phone: msg.to,
          success: false,
          error: `Automation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
          method: 'automation' as const
        })));
      }
    }

    // Send API messages
    if (apiMessages.length > 0 && this.config.apiConfig.enabled) {
      console.log(`📡 Sending ${apiMessages.length} messages via WhatsApp API`);
      try {
        const apiResult = await this.sendBulkViaAPI(apiMessages);
        results.push(...apiResult.results);
      } catch (error) {
        console.error('API bulk sending failed:', error);
        results.push(...apiMessages.map(msg => ({
          phone: msg.to,
          success: false,
          error: `API failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
          method: 'api' as const
        })));
      }
    }

    // Send desktop messages (manual fallback)
    if (desktopMessages.length > 0) {
      console.log(`🖥️ Sending ${desktopMessages.length} messages via manual desktop`);
      try {
        const desktopResult = await this.sendBulkViaDesktop(desktopMessages);
        results.push(...desktopResult.results);
      } catch (error) {
        console.error('Desktop bulk sending failed:', error);
        results.push(...desktopMessages.map(msg => ({
          phone: msg.to,
          success: false,
          error: `Desktop failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
          method: 'desktop' as const
        })));
      }
    }

    const successful = results.filter(r => r.success).length;

    return {
      total: messages.length,
      successful,
      failed: messages.length - successful,
      results
    };
  }

  /**
   * Send via nut.js automation
   */
  private async sendViaAutomation(options: WhatsAppSendOptions): Promise<WhatsAppSendResult> {
    try {
      console.log(`🤖 Sending message to ${options.to} via nut.js automation`);
      
      const result = await whatsappAutomationIntegration.sendSingleMessage(
        options.to,
        options.message,
        'Contact' // Default name
      );

      return {
        success: result.success,
        messageId: result.messageId,
        error: result.error,
        method: 'automation'
      };
    } catch (error) {
      console.error('Automation sending failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Automation failed',
        method: 'automation'
      };
    }
  }

  /**
   * Send bulk messages via nut.js automation
   */
  private async sendBulkViaAutomation(messages: WhatsAppSendOptions[]): Promise<WhatsAppBulkResult> {
    try {
      console.log(`🤖 Starting bulk automation for ${messages.length} messages`);
      
      // Prepare campaign request for automation
      const campaignRequest = {
        subscribers: messages.map((msg, index) => ({
          id: `temp_${index}`,
          name: 'Contact',
          phone: msg.to
        })),
        messageTemplate: messages[0].message, // Assuming same message for all
        campaignId: `bulk_${Date.now()}`,
        campaignName: 'Bulk WhatsApp Campaign'
      };

      const automationResult = await whatsappAutomationIntegration.sendCampaign(campaignRequest);

      // Convert automation result to WhatsApp bulk result format
      const results = messages.map((msg, index) => {
        const automationRes = automationResult.results[index];
        return {
          phone: msg.to,
          success: automationRes?.result.success || false,
          messageId: automationRes?.result.messageId,
          error: automationRes?.result.error,
          method: 'automation' as const
        };
      });

      return {
        total: automationResult.total,
        successful: automationResult.successful,
        failed: automationResult.failed,
        results
      };
    } catch (error) {
      console.error('Bulk automation failed:', error);
      throw error;
    }
  }

  /**
   * Send via WhatsApp API
   */
  private async sendViaAPI(options: WhatsAppSendOptions): Promise<WhatsAppSendResult> {
    try {
      if (!this.config.apiConfig.enabled) {
        throw new Error('WhatsApp API is not enabled');
      }

      console.log(`📡 Sending message to ${options.to} via WhatsApp API`);
      
      const result = await whatsappService.sendMessage({
        to: options.to,
        message: options.message,
        type: 'text'
      });

      return {
        success: result.success,
        messageId: result.messageId,
        status: result.status,
        error: result.error,
        method: 'api'
      };
    } catch (error) {
      console.error('API sending failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'API failed',
        method: 'api'
      };
    }
  }

  /**
   * Send bulk messages via API
   */
  private async sendBulkViaAPI(messages: WhatsAppSendOptions[]): Promise<WhatsAppBulkResult> {
    try {
      const recipients = messages.map(msg => ({
        phone: msg.to,
        message: msg.message
      }));

      const result = await whatsappService.sendBulkMessages(recipients);
      
      return {
        total: result.total,
        successful: result.successful,
        failed: result.failed,
        results: result.results.map(r => ({
          ...r,
          method: 'api' as const
        }))
      };
    } catch (error) {
      console.error('API bulk sending failed:', error);
      throw error;
    }
  }

  /**
   * Send via manual desktop (fallback method)
   */
  private async sendViaDesktop(options: WhatsAppSendOptions): Promise<WhatsAppSendResult> {
    try {
      console.log(`🖥️ Opening WhatsApp Desktop for ${options.to}`);
      
      // Format phone number
      const formattedPhone = this.formatPhoneNumber(options.to);
      
      // Create WhatsApp URL
      const whatsappUrl = `https://wa.me/${formattedPhone}?text=${encodeURIComponent(options.message)}`;
      
      // Open WhatsApp
      window.open(whatsappUrl, '_blank');

      return {
        success: true,
        messageId: `desktop_${Date.now()}`,
        status: 'opened_manually',
        method: 'desktop'
      };
    } catch (error) {
      console.error('Desktop sending failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Desktop failed',
        method: 'desktop'
      };
    }
  }

  /**
   * Send bulk messages via manual desktop
   */
  private async sendBulkViaDesktop(messages: WhatsAppSendOptions[]): Promise<WhatsAppBulkResult> {
    const results: WhatsAppBulkResult['results'] = [];

    // Show user that manual desktop mode is being used
    const confirmed = window.confirm(
      `Manual Desktop Mode: ${messages.length} messages will open in WhatsApp Desktop/Web.\n\n` +
      `Each message will open in a new tab. You'll need to manually send each one.\n\n` +
      `Continue?`
    );

    if (!confirmed) {
      return {
        total: messages.length,
        successful: 0,
        failed: messages.length,
        results: messages.map(msg => ({
          phone: msg.to,
          success: false,
          error: 'User cancelled manual sending',
          method: 'desktop'
        }))
      };
    }

    // Open each message in WhatsApp
    for (const message of messages) {
      try {
        const result = await this.sendViaDesktop(message);
        results.push({
          phone: message.to,
          success: result.success,
          messageId: result.messageId,
          error: result.error,
          method: 'desktop'
        });

        // Small delay between opening tabs
        await this.delay(1000);
      } catch (error) {
        results.push({
          phone: message.to,
          success: false,
          error: error instanceof Error ? error.message : 'Failed to open',
          method: 'desktop'
        });
      }
    }

    const successful = results.filter(r => r.success).length;

    return {
      total: messages.length,
      successful,
      failed: messages.length - successful,
      results
    };
  }

  /**
   * Select the best method based on message count and configuration
   */
  private selectBestMethod(messageCount: number): 'api' | 'desktop' | 'automation' {
    // Priority 1: If automation is enabled and available, use it for any bulk (2+)
    if (this.config.automationConfig.enabled && messageCount >= 2) {
      return 'automation';
    }

    // Priority 2: If automation is enabled, use it for single messages too
    if (this.config.automationConfig.enabled) {
      return 'automation';
    }

    // Priority 3: Use API for high volume if available
    if (this.config.apiConfig.enabled && messageCount > 10) {
      return 'api';
    }

    // Priority 4: Use API for single messages if available
    if (this.config.apiConfig.enabled) {
      return 'api';
    }

    // Fallback: Manual desktop
    return 'desktop';
  }

  /**
   * Test connection for a specific method
   */
  async testConnection(method: 'api' | 'desktop' | 'automation'): Promise<{ success: boolean; message: string }> {
    try {
      switch (method) {
        case 'automation':
          return await whatsappAutomationIntegration.testAutomation();
        case 'api':
          return await whatsappService.testConnection();
        case 'desktop':
          return {
            success: true,
            message: 'Desktop method available (will open WhatsApp Desktop/Web manually)'
          };
        default:
          throw new Error('Invalid method for connection test');
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Connection test failed'
      };
    }
  }

  /**
   * Get smart method recommendation
   */
  recommendMethod(messageCount: number, priority: 'low' | 'medium' | 'high' | 'urgent'): { method: 'api' | 'desktop' | 'automation'; reason: string } {
    // Always recommend automation if available
    if (this.config.automationConfig.enabled) {
      if (messageCount >= 5) {
        return {
          method: 'automation',
          reason: 'nut.js automation recommended for bulk messaging - completely free and reliable'
        };
      } else {
        return {
          method: 'automation',
          reason: 'nut.js automation recommended - free, fast, and professional'
        };
      }
    }

    // Fallback to API for high volume
    if (this.config.apiConfig.enabled && messageCount > 10) {
      return {
        method: 'api',
        reason: 'API recommended for high volume messaging with delivery tracking'
      };
    }

    // Fallback to API for urgent messages
    if (this.config.apiConfig.enabled && priority === 'urgent') {
      return {
        method: 'api',
        reason: 'API recommended for urgent messages requiring immediate delivery'
      };
    }

    // Default fallback
    return {
      method: 'desktop',
      reason: 'Manual desktop sending - requires manual interaction for each message'
    };
  }

  /**
   * Configuration management
   */
  getConfiguration(): WhatsAppConfiguration {
    return { ...this.config };
  }

  async saveConfiguration(config: WhatsAppConfiguration): Promise<void> {
    try {
      localStorage.setItem('whatsapp_enhanced_config', JSON.stringify(config));
      this.config = { ...config };
      
      // Update automation integration preferences
      if (config.automationConfig.enabled) {
        await whatsappAutomationIntegration.updatePreferences({
          enableNutjsAutomation: config.automationConfig.enabled,
          safeMode: config.automationConfig.safeMode,
          maxBatchSize: config.automationConfig.maxBatchSize
        });
      }
    } catch (error) {
      throw new Error('Failed to save WhatsApp configuration');
    }
  }

  private loadConfiguration(): void {
    try {
      const saved = localStorage.getItem('whatsapp_enhanced_config');
      if (saved) {
        this.config = { ...this.getDefaultConfiguration(), ...JSON.parse(saved) };
      }
    } catch (error) {
      console.warn('Failed to load WhatsApp configuration, using defaults');
    }
  }

  /**
   * Utility methods
   */
  private formatPhoneNumber(phone: string): string {
    // Remove all non-digits
    const digits = phone.replace(/\D/g, '');
    
    // Add country code if needed (assuming India +91)
    if (digits.length === 10) {
      return `91${digits}`;
    } else if (digits.length === 12 && digits.startsWith('91')) {
      return digits;
    }
    
    return digits;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get current status of all methods
   */
  async getMethodStatus(): Promise<{
    automation: { available: boolean; message: string };
    api: { available: boolean; message: string };
    desktop: { available: boolean; message: string };
  }> {
    const [automationStatus, apiStatus, desktopStatus] = await Promise.all([
      this.testConnection('automation'),
      this.testConnection('api'),
      this.testConnection('desktop')
    ]);

    return {
      automation: {
        available: automationStatus.success,
        message: automationStatus.message
      },
      api: {
        available: apiStatus.success,
        message: apiStatus.message
      },
      desktop: {
        available: desktopStatus.success,
        message: desktopStatus.message
      }
    };
  }
}

// Create and export the enhanced service instance
export const enhancedWhatsAppService = new EnhancedWhatsAppDualService();

// Export for backward compatibility
export default enhancedWhatsAppService;