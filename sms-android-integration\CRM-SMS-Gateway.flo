{"name": "CRM SMS Gateway", "description": "Automatically sends SMS messages from CRM system", "version": "1.0", "author": "CRM System", "blocks": [{"id": 1, "type": "flow_beginning", "x": 100, "y": 100, "next": 2}, {"id": 2, "type": "http_request", "x": 100, "y": 200, "config": {"url": "http://*************:3001/api/sms/pending", "method": "GET", "headers": {"Content-Type": "application/json"}, "variable_name": "pendingResponse", "timeout": 10000}, "next": 3, "error": 10}, {"id": 3, "type": "expression", "x": 100, "y": 300, "config": {"expression": "json(pendingResponse)", "variable_name": "messageList"}, "next": 4}, {"id": 4, "type": "condition", "x": 100, "y": 400, "config": {"condition": "length(messageList) > 0"}, "true": 5, "false": 9}, {"id": 5, "type": "for_each", "x": 100, "y": 500, "config": {"list": "messageList", "variable_name": "currentMessage"}, "body": 6, "next": 9}, {"id": 6, "type": "send_sms", "x": 200, "y": 600, "config": {"phone_number": "currentMessage.recipient", "message": "currentMessage.message", "sim": "default"}, "next": 7, "error": 8}, {"id": 7, "type": "http_request", "x": 200, "y": 700, "config": {"url": "http://*************:3001/api/sms/status", "method": "POST", "headers": {"Content-Type": "application/json"}, "body": "{\"messageId\": \"{currentMessage.id}\", \"status\": \"sent\", \"timestamp\": \"{datetime()}\"}", "timeout": 5000}, "next": null, "error": null}, {"id": 8, "type": "http_request", "x": 300, "y": 700, "config": {"url": "http://*************:3001/api/sms/status", "method": "POST", "headers": {"Content-Type": "application/json"}, "body": "{\"messageId\": \"{currentMessage.id}\", \"status\": \"failed\", \"error\": \"SMS sending failed\", \"timestamp\": \"{datetime()}\"}", "timeout": 5000}, "next": null}, {"id": 9, "type": "delay", "x": 100, "y": 800, "config": {"duration": 5000}, "next": 2}, {"id": 10, "type": "toast", "x": 200, "y": 200, "config": {"message": "Network error: Cannot connect to CRM server", "duration": "short"}, "next": 9}, {"id": 11, "type": "notification", "x": 100, "y": 900, "config": {"title": "CRM SMS Gateway", "message": "Processed {length(messageList)} messages", "icon": "sms", "priority": "low"}, "next": null}], "variables": {"serverUrl": "http://*************:3001", "pollInterval": 5000, "maxRetries": 3}, "settings": {"run_in_background": true, "wake_device": false, "keep_device_awake": false, "auto_start": true}}