# Tasker Setup for CRM SMS Integration (Alternative to Automate)

## Overview
If you prefer <PERSON><PERSON> over <PERSON>mate, this guide shows how to set up SMS automation using Tasker. Tasker is a more mature automation app with extensive community support.

## Step 1: Install Required Apps

### Install Tasker
1. **Download Tasker** from Google Play Store (paid app ~$3)
2. **Install and open** Tasker
3. **Grant all permissions** when prompted
4. **Enable accessibility service** if requested

### Install AutoInput Plugin (Optional)
1. **Search "AutoInput"** in Play Store
2. **Install the plugin** for advanced automation
3. **This helps with** complex UI interactions

## Step 2: Create SMS Polling Profile

### Create Time-Based Profile
1. **Open Tasker**
2. **Tap "Profiles" tab**
3. **Tap "+" to add new profile**
4. **Select "Time"**
5. **Configure**:
   - **From**: 00:00
   - **To**: 23:59
   - **Repeat**: Every 5 minutes
6. **Tap checkmark** to save

### Alternative: Event-Based Profile
For more responsive automation:
1. **Create profile** → **Event** → **Variable Set**
2. **Variable**: `%SMSCheck`
3. **This allows** manual triggering and better control

## Step 3: Create SMS Sending Task

### Create New Task
1. **When prompted** after creating profile, tap "New Task"
2. **Name it**: "CRM SMS Sender"
3. **Tap checkmark**

### Add Actions to Task

#### Action 1: HTTP Get (Check for Messages)
1. **Tap "+" to add action**
2. **Select "Net" → "HTTP Get"**
3. **Configure**:
   - **Server:Port**: `*************:3001`
   - **Path**: `/api/sms/pending`
   - **Headers**: `Content-Type:application/json`
   - **Output File**: `%SMSMessages`
4. **Tap checkmark**

#### Action 2: JavaScriptlet (Parse JSON)
1. **Add action** → **Code** → **JavaScriptlet**
2. **Code**:
   ```javascript
   var messages = JSON.parse(global('%SMSMessages'));
   setGlobal('MessageCount', messages.length);
   setGlobal('MessageList', JSON.stringify(messages));
   ```
3. **This parses** the JSON response

#### Action 3: For Loop (Process Each Message)
1. **Add action** → **Task** → **For**
2. **Configure**:
   - **Variable**: `%msg`
   - **Items**: `1:%MessageCount`
3. **This creates** a loop for each message

#### Action 4: JavaScriptlet (Get Current Message)
1. **Add action** → **Code** → **JavaScriptlet**
2. **Code**:
   ```javascript
   var messages = JSON.parse(global('MessageList'));
   var currentMsg = messages[%msg - 1];
   setGlobal('CurrentRecipient', currentMsg.recipient);
   setGlobal('CurrentMessage', currentMsg.message);
   setGlobal('CurrentID', currentMsg.id);
   ```

#### Action 5: Send SMS
1. **Add action** → **Phone** → **Send SMS**
2. **Configure**:
   - **Number**: `%CurrentRecipient`
   - **Message**: `%CurrentMessage`
   - **Store in Messaging App**: Checked
3. **This sends** the actual SMS

#### Action 6: HTTP Post (Report Success)
1. **Add action** → **Net** → "HTTP Post"**
2. **Configure**:
   - **Server:Port**: `*************:3001`
   - **Path**: `/api/sms/status`
   - **Data/File**: 
     ```json
     {"messageId":"%CurrentID","status":"sent","timestamp":"%DATE %TIME"}
     ```
   - **Content Type**: `application/json`

#### Action 7: End For Loop
1. **Add action** → **Task** → **End For**
2. **This closes** the message processing loop

## Step 4: Add Error Handling

### HTTP Error Handling
1. **Long press** on HTTP Get action
2. **Select "Add" → "If"**
3. **Configure condition**: `%HTTPR !~ 200`
4. **Add "Flash" action**: "Network Error - Cannot reach CRM"
5. **Add "Stop" action** to prevent further processing

### SMS Error Handling
1. **Long press** on Send SMS action
2. **Add "If" condition**: `%SMSRF !~ success`
3. **Add HTTP Post** to report failure:
   ```json
   {"messageId":"%CurrentID","status":"failed","error":"SMS send failed","timestamp":"%DATE %TIME"}
   ```

## Step 5: Advanced Configuration

### Background Operation
1. **Go to Tasker settings**
2. **Enable "Run in Foreground"**
3. **Disable battery optimization**:
   - Settings → Battery → Battery Optimization
   - Find Tasker → Don't optimize

### Notification Setup
1. **Add "Notify" action** after processing messages
2. **Configure**:
   - **Title**: "CRM SMS"
   - **Text**: "Sent %MessageCount messages"
   - **Icon**: SMS icon

### Variable Monitoring
1. **Create scene** to display current status
2. **Show variables**:
   - `%MessageCount` - Messages processed
   - `%HTTPR` - Last HTTP response code
   - `%SMSRF` - Last SMS result

## Step 6: Testing and Debugging

### Test Individual Components
1. **Test HTTP Get**:
   - Run task manually
   - Check `%SMSMessages` variable
   - Verify JSON format

2. **Test SMS Sending**:
   - Set test variables manually
   - Run SMS action only
   - Verify message is sent

3. **Test Full Flow**:
   - Create test campaign in CRM
   - Run complete task
   - Check CRM for status updates

### Debug Tools
1. **Use "Flash" actions** to show variable values
2. **Enable "Run Log"** in Tasker settings
3. **Check "Variables" tab** for current values
4. **Use "Test" button** on individual actions

## Step 7: Optimization

### Reduce Battery Usage
1. **Increase polling interval** if not time-critical
2. **Use "WiFi Connected" condition** to only run on WiFi
3. **Add "Device Idle" detection** to pause during sleep

### Improve Reliability
1. **Add retry logic** for failed HTTP requests
2. **Implement exponential backoff** for errors
3. **Add "Airplane Mode" detection** to pause when offline

### Multiple SIM Support
1. **Add "SIM Selection" logic** for dual-SIM devices
2. **Use different SIMs** for different campaigns
3. **Load balance** across available SIMs

## Step 8: Export and Backup

### Export Configuration
1. **Long press** on profile name
2. **Select "Export"**
3. **Choose "Description to Clipboard"**
4. **Save to file** for backup

### Share Configuration
1. **Export as XML** for sharing
2. **Create QR code** for easy import
3. **Document custom variables** and settings

## Tasker vs Automate Comparison

### Tasker Advantages
- ✅ **More mature** and stable
- ✅ **Extensive plugin ecosystem**
- ✅ **Better community support**
- ✅ **More advanced scripting**
- ✅ **Better variable handling**

### Tasker Disadvantages
- ❌ **Paid app** (~$3)
- ❌ **Steeper learning curve**
- ❌ **More complex setup**
- ❌ **Requires more configuration**

### Automate Advantages
- ✅ **Free app**
- ✅ **Visual flow editor**
- ✅ **Easier for beginners**
- ✅ **Simpler setup**

## Troubleshooting

### Common Tasker Issues
1. **Profile not triggering**:
   - Check profile is enabled (green)
   - Verify time conditions
   - Check battery optimization

2. **HTTP requests failing**:
   - Test URL in browser
   - Check network connectivity
   - Verify server is running

3. **SMS not sending**:
   - Check SMS permissions
   - Verify SIM card status
   - Test manual SMS sending

4. **Variables not updating**:
   - Check variable names (case sensitive)
   - Use "Flash" to debug values
   - Check action order

### Performance Issues
1. **High battery usage**:
   - Increase polling interval
   - Add idle detection
   - Optimize HTTP requests

2. **Memory issues**:
   - Clear old variables
   - Limit message history
   - Restart Tasker periodically

## Alternative: Simple Tasker Setup

For a simpler approach:

### Minimal Configuration
1. **Profile**: Time every 5 minutes
2. **Task**: 
   - HTTP Get pending messages
   - Send each SMS
   - HTTP Post status updates
3. **No complex error handling** initially

### Gradual Enhancement
1. **Start with basic** functionality
2. **Add error handling** once working
3. **Implement optimizations** as needed
4. **Add monitoring** and notifications

This Tasker setup provides the same functionality as Automate but with more advanced features and customization options.
