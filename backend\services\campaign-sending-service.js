import { database } from '../database/connection.js';
import cron from 'node-cron';
import { DateTime } from 'luxon';
import nodemailer from 'nodemailer';
import crypto from 'crypto';
// Note: SMS service will be imported when available

/**
 * Campaign Sending Service
 * Automatically processes campaigns with 'sending' status and sends emails/WhatsApp messages
 */
class CampaignSendingService {
  constructor() {
    this.timezone = 'Asia/Calcutta';
    this.schedule = '*/5 * * * *'; // Run every 5 minutes
    this.isRunning = false;
    this.activeSends = new Map(); // Track active sending operations
    this.whatsappService = null;
    this.smsService = null;
    this.emailService = null;
    this.databaseReady = false;

    // Initialize database and ensure tables exist
    this.initializeDatabase();
  }

  /**
   * Initialize database and create required tables
   */
  async initializeDatabase() {
    try {
      console.log('🔧 Initializing campaign sending database...');

      // Ensure database connection exists
      if (!database.db) {
        await database.connect();
      }

      // Create campaign_sending_logs table if it doesn't exist
      await database.run(`
        CREATE TABLE IF NOT EXISTS campaign_sending_logs (
          id TEXT PRIMARY KEY,
          campaign_id TEXT NOT NULL,
          campaign_name TEXT,
          recipient_id TEXT,
          recipient_name TEXT,
          recipient_email TEXT,
          recipient_phone TEXT,
          recipient_whatsapp TEXT,
          channel TEXT NOT NULL,
          status TEXT NOT NULL,
          sent_at TEXT NOT NULL,
          message_id TEXT,
          error_message TEXT,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
      `);

      this.databaseReady = true;
      console.log('✅ Campaign sending database initialized');

    } catch (error) {
      console.error('❌ Failed to initialize campaign sending database:', error);
      this.databaseReady = false;
    }
  }

  /**
   * Start the campaign sending scheduler
   */
  async start() {
    console.log('📧 Starting Campaign Sending Service...');

    // Initialize real sending services
    await this.initializeServices();

    // Run immediately on start
    await this.processSendingCampaigns();

    // Schedule recurring checks
    cron.schedule(this.schedule, () => this.processSendingCampaigns(), {
      timezone: this.timezone
    });

    console.log('✅ Campaign Sending Service started - checking every 5 minutes');
  }

  /**
   * Initialize real sending services
   */
  async initializeServices() {
    try {
      // WhatsApp service is now handled by frontend integration
      this.whatsappService = null;
      console.log('💬 WhatsApp service handled by frontend integration');

      // Initialize SMS service (placeholder for now)
      try {
        // TODO: Initialize SMS service when available
        this.smsService = null;
        console.log('⚠️ SMS service not configured - using simulation');
      } catch (error) {
        console.log('⚠️ SMS service initialization failed:', error.message);
      }

      console.log('✅ Sending services initialized');

    } catch (error) {
      console.error('❌ Error initializing sending services:', error);
    }
  }



  /**
   * Log a sending attempt to the database
   */
  async logSendingAttempt(campaignId, campaignName, recipient, channel, status, messageId = null, errorMessage = null) {
    try {
      // Ensure database is ready
      if (!this.databaseReady) {
        console.log(`📝 [PENDING] Database not ready, queuing log: ${channel} ${status} for ${recipient.name || recipient.email}`);
        return;
      }

      const logId = crypto.randomUUID();
      const now = DateTime.now().setZone(this.timezone).toISO();

      await database.run(`
        INSERT INTO campaign_sending_logs (
          id, campaign_id, campaign_name, recipient_id, recipient_name,
          recipient_email, recipient_phone, recipient_whatsapp,
          channel, status, sent_at, message_id, error_message
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        logId,
        campaignId,
        campaignName,
        recipient.id || null,
        recipient.name || (recipient.firstName + ' ' + recipient.lastName) || 'Unknown',
        recipient.email || null,
        recipient.phone || null,
        recipient.whatsapp_number || recipient.phone || null,
        channel,
        status,
        now,
        messageId,
        errorMessage
      ]);

      console.log(`📝 [LOGGED] ${channel} ${status} for ${recipient.name || recipient.email}: ${messageId || errorMessage || 'No details'}`);

    } catch (error) {
      console.error('❌ Error logging sending attempt:', error);
      console.error('   Database ready:', this.databaseReady);
      console.error('   Database connection:', !!database.db);
    }
  }

  /**
   * Process all campaigns that are in 'sending' status
   */
  async processSendingCampaigns() {
    if (this.isRunning) {
      console.log('⏭️ Campaign sending already running, skipping...');
      return;
    }

    try {
      this.isRunning = true;
      console.log('📧 Checking for campaigns to send...');
      
      // Get all campaigns in 'sending' status
      const activeCampaignIds = Array.from(this.activeSends.keys());
      let sendingCampaigns;

      if (activeCampaignIds.length > 0) {
        const placeholders = activeCampaignIds.map(() => '?').join(',');
        sendingCampaigns = await database.all(`
          SELECT c.*, ct.template_name, ct.display_name as template_display_name
          FROM campaigns c
          LEFT JOIN campaign_templates ct ON c.template_id = ct.id
          WHERE c.status = 'sending'
            AND c.id NOT IN (${placeholders})
          ORDER BY c.created_at ASC
        `, activeCampaignIds);
      } else {
        sendingCampaigns = await database.all(`
          SELECT c.*, ct.template_name, ct.display_name as template_display_name
          FROM campaigns c
          LEFT JOIN campaign_templates ct ON c.template_id = ct.id
          WHERE c.status = 'sending'
          ORDER BY c.created_at ASC
        `);
      }

      console.log(`🔍 Query result: Found ${sendingCampaigns.length} campaigns in 'sending' status`);

      if (sendingCampaigns.length === 0) {
        console.log('📭 No campaigns to send');
        return;
      }

      console.log(`📋 Processing ${sendingCampaigns.length} campaigns:`);
      sendingCampaigns.forEach(campaign => {
        console.log(`  📧 ${campaign.id}: ${campaign.name} (Status: ${campaign.status})`);
        console.log(`    Email content: ${campaign.email_content ? 'Present' : 'Missing'}`);
        console.log(`    WhatsApp content: ${campaign.whatsapp_content ? 'Present' : 'Missing'}`);
        console.log(`    SMS content: ${campaign.sms_content ? 'Present' : 'Missing'}`);
      });

      // Process campaigns in parallel (but limit concurrency)
      const maxConcurrent = 3;
      for (let i = 0; i < sendingCampaigns.length; i += maxConcurrent) {
        const batch = sendingCampaigns.slice(i, i + maxConcurrent);
        await Promise.all(batch.map(campaign => this.sendCampaign(campaign)));
      }

    } catch (error) {
      console.error('❌ Error in campaign sending service:', error);
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Send a single campaign
   */
  async sendCampaign(campaign) {
    const campaignId = campaign.id;

    if (this.activeSends.has(campaignId)) {
      console.log(`⏭️ Campaign ${campaignId} already being sent, skipping...`);
      return;
    }

    // Check if campaign already has logs (prevent duplicate execution)
    try {
      const existingLogs = await database.all(
        'SELECT COUNT(*) as count FROM campaign_sending_logs WHERE campaign_id = ?',
        [campaignId]
      );

      if (existingLogs[0].count > 0) {
        console.log(`⏭️ Campaign ${campaignId} already has ${existingLogs[0].count} logs, skipping duplicate execution...`);
        // Mark as sent if not already marked
        if (campaign.status === 'sending') {
          await this.markCampaignComplete(campaignId, 'sent', 0, 0);
        }
        return;
      }
    } catch (error) {
      console.error(`❌ Error checking existing logs for campaign ${campaignId}:`, error);
    }

    try {
      this.activeSends.set(campaignId, { startTime: new Date(), status: 'processing' });

      console.log(`📧 Sending campaign: ${campaign.name} (ID: ${campaignId})`);

      // Get campaign recipients
      const recipients = await this.getCampaignRecipients(campaignId);
      
      if (recipients.length === 0) {
        console.log(`⚠️ No recipients found for campaign ${campaignId}`);
        await this.markCampaignComplete(campaignId, 'sent', 0, 0);
        return;
      }

      console.log(`👥 Found ${recipients.length} recipients for campaign ${campaignId}`);

      // Send via multiple channels if enabled
      let totalSent = 0;
      let totalFailed = 0;

      // Send Email if enabled and content exists
      if (campaign.email_enabled !== false && campaign.email_content) {
        console.log(`📧 Sending email channel for campaign: ${campaign.name}`);
        // Filter recipients who have valid email addresses
        const emailRecipients = recipients.filter(r => r.email && r.email.trim() !== '');
        console.log(`📧 Found ${emailRecipients.length} email recipients out of ${recipients.length} total`);
        const emailResult = await this.sendEmailCampaign(campaign, emailRecipients);
        totalSent += emailResult.sent;
        totalFailed += emailResult.failed;
      }

      // Send WhatsApp if enabled and content exists
      if (campaign.whatsapp_enabled !== false && campaign.whatsapp_content) {
        console.log(`💬 Sending WhatsApp channel for campaign: ${campaign.name}`);
        // Filter recipients who have phone numbers AND allow WhatsApp
        const whatsappRecipients = recipients.filter(r => {
          const hasPhone = r.phone && r.phone.trim() !== '';
          const allowsWhatsApp = r.allowWhatsApp === 1 || r.allowWhatsApp === true;
          console.log(`💬 Recipient ${r.email}: hasPhone=${hasPhone}, allowsWhatsApp=${allowsWhatsApp}, phone="${r.phone}"`);
          return hasPhone && allowsWhatsApp;
        });
        console.log(`💬 Found ${whatsappRecipients.length} WhatsApp recipients out of ${recipients.length} total`);
        const whatsappResult = await this.sendWhatsAppCampaign(campaign, whatsappRecipients);
        totalSent += whatsappResult.sent;
        totalFailed += whatsappResult.failed;
      }

      // Send SMS if enabled and content exists
      if (campaign.sms_enabled !== false && campaign.sms_content) {
        console.log(`📱 Sending SMS channel for campaign: ${campaign.name}`);
        // Filter recipients who have phone numbers AND allow SMS
        const smsRecipients = recipients.filter(r => {
          const hasPhone = r.phone && r.phone.trim() !== '';
          const allowsSms = r.allowSms === 1 || r.allowSms === true;
          console.log(`📱 Recipient ${r.email}: hasPhone=${hasPhone}, allowsSms=${allowsSms}, phone="${r.phone}"`);
          return hasPhone && allowsSms;
        });
        console.log(`📱 Found ${smsRecipients.length} SMS recipients out of ${recipients.length} total`);
        const smsResult = await this.sendSMSCampaign(campaign, smsRecipients);
        totalSent += smsResult.sent;
        totalFailed += smsResult.failed;
      }

      // Check if any channel was enabled
      const hasAnyChannel = (campaign.email_enabled !== false && campaign.email_content) ||
                           (campaign.whatsapp_enabled !== false && campaign.whatsapp_content) ||
                           (campaign.sms_enabled !== false && campaign.sms_content);

      if (!hasAnyChannel) {
        throw new Error('Campaign has no enabled channels or content to send');
      }

      const sendResult = { sent: totalSent, failed: totalFailed };

      // Check for pending WhatsApp messages that require user intervention
      const pendingWhatsAppCount = await this.getPendingWhatsAppCount(campaignId);

      // Check for SMS failures due to service not configured
      const smsFailures = await this.getSMSFailureCount(campaignId);

      // Determine final status based on delivery results
      let finalStatus;
      if (pendingWhatsAppCount > 0) {
        finalStatus = 'partially_sent'; // Has pending WhatsApp messages
      } else if (sendResult.failed === 0) {
        finalStatus = 'sent'; // All messages sent successfully
      } else if (sendResult.sent === 0) {
        finalStatus = 'failed'; // No messages sent
      } else {
        finalStatus = 'partially_sent'; // Some sent, some failed
      }

      console.log(`📊 Campaign ${campaignId} delivery summary:`);
      console.log(`  - Total sent: ${sendResult.sent}`);
      console.log(`  - Total failed: ${sendResult.failed}`);
      console.log(`  - Pending WhatsApp: ${pendingWhatsAppCount}`);
      console.log(`  - SMS failures: ${smsFailures}`);
      console.log(`  - Final status: ${finalStatus}`);

      await this.markCampaignComplete(campaignId, finalStatus, sendResult.sent, sendResult.failed);
      
      console.log(`✅ Campaign ${campaignId} completed: ${sendResult.sent} sent, ${sendResult.failed} failed`);

    } catch (error) {
      console.error(`❌ Error sending campaign ${campaignId}:`, error);
      
      // Mark campaign as failed
      await this.markCampaignComplete(campaignId, 'failed', 0, 0, error.message);
    } finally {
      this.activeSends.delete(campaignId);
    }
  }

  /**
   * Get recipients for a campaign
   */
  async getCampaignRecipients(campaignId) {
    try {
      // Get campaign details with template information
      const campaign = await database.get(`
        SELECT c.*, t.interest_area_id as template_interest_area_id
        FROM campaigns c
        LEFT JOIN campaign_templates t ON c.template_id = t.id
        WHERE c.id = ?
      `, [campaignId]);

      if (!campaign) {
        throw new Error(`Campaign ${campaignId} not found`);
      }

      console.log(`🎯 [RECIPIENTS] Getting recipients for campaign: ${campaign.name}`);
      console.log(`🎯 [RECIPIENTS] Selection mode: ${campaign.subscriber_selection_mode}`);
      console.log(`🎯 [RECIPIENTS] Template interest area: ${campaign.template_interest_area_id}`);

      // Get recipients based on campaign configuration
      let recipients = [];

      // Check different subscriber selection modes
      if (campaign.subscriber_selection_mode === 'specific' && campaign.selected_subscriber_ids) {
        // Specific subscribers selected
        const subscriberIds = JSON.parse(campaign.selected_subscriber_ids);
        if (subscriberIds.length > 0) {
          const placeholders = subscriberIds.map(() => '?').join(',');
          recipients = await database.all(`
            SELECT * FROM subscribers
            WHERE id IN (${placeholders}) AND status = 'active'
          `, subscriberIds);
        }
        console.log(`🎯 [RECIPIENTS] Found ${recipients.length} specific subscribers`);
      } else if (campaign.subscriber_selection_mode === 'areas' && campaign.area_of_interest_ids) {
        // By areas of interest
        const areaIds = JSON.parse(campaign.area_of_interest_ids);
        if (areaIds.length > 0) {
          const placeholders = areaIds.map(() => '?').join(',');
          recipients = await database.all(`
            SELECT DISTINCT s.* FROM subscribers s
            JOIN subscriber_areas_of_interest sa ON s.id = sa.subscriber_id
            WHERE sa.area_of_interest_id IN (${placeholders}) AND s.status = 'active'
          `, areaIds);
        }
        console.log(`🎯 [RECIPIENTS] Found ${recipients.length} subscribers by areas`);
      } else if (campaign.template_interest_area_id) {
        // Use template's interest area (most common case)
        console.log(`🎯 [RECIPIENTS] Using template interest area: ${campaign.template_interest_area_id}`);
        recipients = await database.all(`
          SELECT DISTINCT s.* FROM subscribers s
          JOIN subscriber_areas_of_interest sa ON s.id = sa.subscriber_id
          WHERE sa.area_of_interest_id = ? AND s.status = 'active'
        `, [campaign.template_interest_area_id]);
        console.log(`🎯 [RECIPIENTS] Found ${recipients.length} subscribers for template interest area`);
      } else if (campaign.subscriber_ids) {
        // Legacy: Specific subscribers (old format)
        const subscriberIds = JSON.parse(campaign.subscriber_ids);
        if (subscriberIds.length > 0) {
          const placeholders = subscriberIds.map(() => '?').join(',');
          recipients = await database.all(`
            SELECT * FROM subscribers
            WHERE id IN (${placeholders}) AND status = 'active'
          `, subscriberIds);
        }
        console.log(`🎯 [RECIPIENTS] Found ${recipients.length} legacy subscribers`);
      } else if (campaign.area_of_interest_ids) {
        // Legacy: By areas of interest (old format)
        const areaIds = JSON.parse(campaign.area_of_interest_ids);
        if (areaIds.length > 0) {
          const placeholders = areaIds.map(() => '?').join(',');
          recipients = await database.all(`
            SELECT DISTINCT s.* FROM subscribers s
            JOIN subscriber_areas_of_interest sa ON s.id = sa.subscriber_id
            WHERE sa.area_of_interest_id IN (${placeholders}) AND s.status = 'active'
          `, areaIds);
        }
        console.log(`🎯 [RECIPIENTS] Found ${recipients.length} legacy area subscribers`);
      } else {
        // All active subscribers (default for 'all' mode or when no specific selection)
        console.log(`🎯 [RECIPIENTS] Using all active subscribers (fallback)`);
        recipients = await database.all('SELECT * FROM subscribers WHERE status = "active"');
        console.log(`🎯 [RECIPIENTS] Found ${recipients.length} total active subscribers`);
      }

      console.log(`✅ [RECIPIENTS] Final recipient count: ${recipients.length}`);
      return recipients;
    } catch (error) {
      console.error(`Error getting recipients for campaign ${campaignId}:`, error);
      return [];
    }
  }

  /**
   * Send email campaign
   */
  async sendEmailCampaign(campaign, recipients) {
    console.log(`📧 Sending email campaign: ${campaign.name}`);

    let sent = 0;
    let failed = 0;

    // Get email configuration from database
    const emailConfig = await database.get(`
      SELECT * FROM email_settings
      ORDER BY id DESC
      LIMIT 1
    `);

    // Check if email configuration is valid
    const hasValidConfig = emailConfig &&
                          emailConfig.username &&
                          (emailConfig.password || emailConfig.password_encrypted);

    console.log('📧 Email config check:', {
      configExists: !!emailConfig,
      hasUsername: !!emailConfig?.username,
      hasPassword: !!(emailConfig?.password || emailConfig?.password_encrypted),
      smtpServer: emailConfig?.smtpServer,
      smtpPort: emailConfig?.smtpPort
    });

    if (!hasValidConfig) {
      console.error('❌ No valid email configuration found - cannot send emails');
      for (const recipient of recipients) {
        failed++;
        await this.logSendingAttempt(campaign.id, campaign.name, recipient, 'email', 'failed', null, 'No valid email configuration');
      }
      return { sent, failed };
    }

    // Real email sending using working SMTP configuration
    console.log(`📧 [REAL] Using SMTP configuration: ${emailConfig.smtpServer}:${emailConfig.smtpPort}`);

    // Create SMTP transporter
    const password = emailConfig.password_encrypted || emailConfig.password;
    console.log('📧 Creating SMTP transporter with:', {
      host: emailConfig.smtpServer,
      port: emailConfig.smtpPort,
      user: emailConfig.username,
      hasPassword: !!password
    });

    const transporter = nodemailer.createTransport({
      host: emailConfig.smtpServer,
      port: emailConfig.smtpPort,
      secure: emailConfig.smtpPort === 465, // true for 465, false for other ports
      auth: {
        user: emailConfig.username,
        pass: password
      },
      tls: {
        rejectUnauthorized: false // For development, adjust for production
      }
    });

    // Verify SMTP connection
    try {
      await transporter.verify();
      console.log('✅ SMTP connection verified successfully');
    } catch (verifyError) {
      console.error('❌ SMTP verification failed:', verifyError);
      for (const recipient of recipients) {
        failed++;
        await this.logSendingAttempt(campaign.id, campaign.name, recipient, 'email', 'failed', null, 'SMTP verification failed');
      }
      return { sent, failed };
    }

    // Check if campaign has attachments and BCC is enabled
    const hasAttachments = campaign.attachments && campaign.attachments.length > 0;
    const useBccForAttachments = emailConfig.useBccForAttachments || false;

    console.log('📧 Email sending strategy:', {
      hasAttachments,
      useBccForAttachments,
      recipientCount: recipients.length,
      strategy: hasAttachments && useBccForAttachments ? 'BCC_SINGLE_EMAIL' : 'INDIVIDUAL_EMAILS'
    });

    // If campaign has attachments and BCC is enabled, send one email with all recipients in BCC
    if (hasAttachments && useBccForAttachments && recipients.length > 0) {
      return await this.sendBccEmailWithAttachments(campaign, recipients, emailConfig, transporter);
    }

    // Send individual emails to recipients (normal flow)
    for (const recipient of recipients) {
      try {
        if (!recipient.email) {
          failed++;
          continue;
        }

        console.log(`📧 [REAL] Sending individual email to ${recipient.email}: ${campaign.subject}`);

        // Parse campaign-specific placeholder values
        let placeholderValues = {};
        if (campaign.campaign_specific_placeholder_values) {
          try {
            placeholderValues = typeof campaign.campaign_specific_placeholder_values === 'string' 
              ? JSON.parse(campaign.campaign_specific_placeholder_values)
              : campaign.campaign_specific_placeholder_values;
          } catch (e) {
            console.error('❌ Error parsing campaign placeholder values:', e);
            placeholderValues = {};
          }
        }

        console.log('🔧 Campaign placeholder values:', placeholderValues);

        // Prepare email content with all placeholder replacements
        let personalizedContent = campaign.email_content;
        let personalizedSubject = campaign.subject;

        // Replace recipient-specific placeholders
        personalizedContent = personalizedContent.replace(/\{name\}/g, recipient.name || 'there');
        personalizedSubject = personalizedSubject.replace(/\{name\}/g, recipient.name || 'there');

        // Append email signature if specified
        if (campaign.email_signature_id) {
          try {
            const signature = await database.get(`
              SELECT content, html_content FROM message_signatures
              WHERE id = ? AND channel = 'email' AND is_active = 1
            `, [campaign.email_signature_id]);

            if (signature) {
              console.log(`📧 Appending email signature: ${campaign.email_signature_id}`);

              // Use HTML signature if available, otherwise plain text
              const signatureContent = signature.html_content || signature.content;

              // Append signature with proper formatting
              if (signatureContent) {
                // Add line breaks before signature
                personalizedContent += '\n\n' + signatureContent;
              }
            } else {
              console.warn(`⚠️ Email signature not found or inactive: ${campaign.email_signature_id}`);
            }
          } catch (error) {
            console.error('❌ Error fetching email signature:', error);
          }
        }

        // Replace campaign-specific placeholders (including system placeholders)
        for (const [placeholder, value] of Object.entries(placeholderValues)) {
          const placeholderRegex = new RegExp(`\\{\\{\\s*${placeholder}\\s*\\}\\}`, 'gi');
          personalizedContent = personalizedContent.replace(placeholderRegex, value || '');
          personalizedSubject = personalizedSubject.replace(placeholderRegex, value || '');
        }

        console.log('📧 Final personalized content preview:', personalizedContent.substring(0, 200) + '...');
        console.log('📧 Final personalized subject:', personalizedSubject);

        // Determine sender details - use campaign-specific if available, otherwise fall back to email config
        const senderName = campaign.sender_name || emailConfig.senderName;
        const senderEmail = campaign.sender_email || emailConfig.senderEmail;

        console.log(`📧 Using sender: "${senderName}" <${senderEmail}> (Campaign: ${campaign.sender_name ? 'Yes' : 'No'}, Config: ${emailConfig.senderName})`);

        // Mail options
        const mailOptions = {
          from: `"${senderName}" <${senderEmail}>`,
          to: recipient.email,
          subject: personalizedSubject,
          html: personalizedContent,
          text: personalizedContent.replace(/<[^>]*>/g, '') // Strip HTML for text version
        };

        // Send the email
        const info = await transporter.sendMail(mailOptions);

        console.log(`✅ Email sent successfully to ${recipient.email}: ${info.messageId}`);
        sent++;

        // Log successful sending attempt
        await this.logSendingAttempt(campaign.id, campaign.name, recipient, 'email', 'sent', info.messageId);

        // Rate limiting - reduced delay for better performance
        await new Promise(resolve => setTimeout(resolve, 200)); // Reduced from 1000ms to 200ms

      } catch (error) {
        failed++;
        console.error(`❌ Error sending email to ${recipient.email}:`, error);
        await this.logSendingAttempt(campaign.id, campaign.name, recipient, 'email', 'failed', null, error.message);
      }
    }

    return { sent, failed };
  }

  /**
   * Send BCC email with attachments to reduce SMTP load
   */
  async sendBccEmailWithAttachments(campaign, recipients, emailConfig, transporter) {
    console.log(`📧 [BCC] Sending BCC email with attachments to ${recipients.length} recipients`);

    let sent = 0;
    let failed = 0;

    try {
      // Determine sender details
      const senderName = campaign.sender_name || emailConfig.senderName;
      const senderEmail = campaign.bccSenderEmail || campaign.sender_email || emailConfig.senderEmail;

      console.log(`📧 [BCC] Using sender: "${senderName}" <${senderEmail}>`);

      // Parse campaign-specific placeholder values (no personalization for BCC)
      let placeholderValues = {};
      if (campaign.campaign_specific_placeholder_values) {
        try {
          placeholderValues = typeof campaign.campaign_specific_placeholder_values === 'string'
            ? JSON.parse(campaign.campaign_specific_placeholder_values)
            : campaign.campaign_specific_placeholder_values;
        } catch (e) {
          console.error('❌ Error parsing campaign placeholder values for BCC:', e);
          placeholderValues = {};
        }
      }

      // Prepare email content (no personalization for BCC emails)
      let emailContent = campaign.email_content;
      let emailSubject = campaign.subject;

      // Replace campaign-specific placeholders only (no recipient-specific placeholders)
      for (const [placeholder, value] of Object.entries(placeholderValues)) {
        const placeholderRegex = new RegExp(`\\{\\{\\s*${placeholder}\\s*\\}\\}`, 'gi');
        emailContent = emailContent.replace(placeholderRegex, value || '');
        emailSubject = emailSubject.replace(placeholderRegex, value || '');
      }

      // Remove any remaining recipient-specific placeholders
      emailContent = emailContent.replace(/\{name\}/g, '[Recipient]');
      emailSubject = emailSubject.replace(/\{name\}/g, '[Recipients]');

      // Append email signature if specified
      if (campaign.email_signature_id) {
        try {
          const signature = await database.get(`
            SELECT content, html_content FROM message_signatures
            WHERE id = ? AND channel = 'email' AND is_active = 1
          `, [campaign.email_signature_id]);

          if (signature) {
            console.log(`📧 [BCC] Appending email signature: ${campaign.email_signature_id}`);
            const signatureContent = signature.html_content || signature.content;
            if (signatureContent) {
              emailContent += '\n\n' + signatureContent;
            }
          }
        } catch (error) {
          console.error('❌ Error fetching email signature for BCC:', error);
        }
      }

      // Process attachments
      let attachments = [];
      if (campaign.attachments) {
        try {
          const attachmentData = typeof campaign.attachments === 'string'
            ? JSON.parse(campaign.attachments)
            : campaign.attachments;

          attachments = attachmentData.map(att => ({
            filename: att.filename,
            content: att.content,
            contentType: att.contentType,
            encoding: 'base64'
          }));

          console.log(`📧 [BCC] Processing ${attachments.length} attachments`);
        } catch (error) {
          console.error('❌ Error processing attachments for BCC:', error);
        }
      }

      // Prepare BCC recipient list
      const bccRecipients = recipients
        .filter(r => r.email)
        .map(r => r.email);

      console.log(`📧 [BCC] Sending to ${bccRecipients.length} recipients in BCC`);

      // Mail options for BCC email
      const mailOptions = {
        from: `"${senderName}" <${senderEmail}>`,
        to: senderEmail, // Send to sender as primary recipient
        bcc: bccRecipients.join(', '), // All recipients in BCC
        subject: emailSubject,
        html: emailContent,
        text: emailContent.replace(/<[^>]*>/g, ''), // Strip HTML for text version
        attachments: attachments
      };

      // Send the BCC email
      const info = await transporter.sendMail(mailOptions);
      console.log(`✅ [BCC] Email sent successfully with ${bccRecipients.length} BCC recipients: ${info.messageId}`);

      // Log successful sending for all recipients
      for (const recipient of recipients) {
        if (recipient.email) {
          await this.logSendingAttempt(campaign.id, campaign.name, recipient, 'email', 'sent', info.messageId, 'Sent via BCC with attachments');
          sent++;
        } else {
          await this.logSendingAttempt(campaign.id, campaign.name, recipient, 'email', 'failed', null, 'No email address');
          failed++;
        }
      }

    } catch (error) {
      console.error('❌ [BCC] Error sending BCC email with attachments:', error);

      // Log failure for all recipients
      for (const recipient of recipients) {
        await this.logSendingAttempt(campaign.id, campaign.name, recipient, 'email', 'failed', null, `BCC sending failed: ${error.message}`);
        failed++;
      }
    }

    return { sent, failed };
  }

  /**
   * Send WhatsApp campaign - delegate to frontend integration
   */
  async sendWhatsAppCampaign(campaign, recipients) {
    console.log(`💬 Sending WhatsApp campaign: ${campaign.name}`);

    let sent = 0;
    let failed = 0;

    // WhatsApp sending must be handled by frontend since backend cannot open whatsapp:// URLs
    console.log(`💬 [INTEGRATION] WhatsApp campaign requires frontend integration`);
    console.log(`💬 [INTEGRATION] Campaign: ${campaign.name} (ID: ${campaign.id})`);
    console.log(`💬 [INTEGRATION] Recipients: ${recipients.length}`);

    // Prepare campaign data for frontend integration
    const campaignData = {
      id: campaign.id,
      name: campaign.name,
      whatsapp_content: campaign.whatsapp_content,
      recipients: recipients.map(recipient => ({
        id: recipient.id,
        name: recipient.name,
        phone: recipient.phone,
        whatsapp_number: recipient.whatsapp_number
      }))
    };

    // Parse campaign-specific placeholder values for frontend
    let placeholderValues = {};
    if (campaign.campaign_specific_placeholder_values) {
      try {
        placeholderValues = typeof campaign.campaign_specific_placeholder_values === 'string'
          ? JSON.parse(campaign.campaign_specific_placeholder_values)
          : campaign.campaign_specific_placeholder_values;
      } catch (e) {
        console.error('❌ Error parsing campaign placeholder values for WhatsApp:', e);
        placeholderValues = {};
      }
    }

    // Add placeholder values to campaign data
    campaignData.placeholderValues = placeholderValues;

    console.log(`💬 [INTEGRATION] Campaign data prepared for frontend:`, {
      id: campaignData.id,
      name: campaignData.name,
      recipientCount: campaignData.recipients.length,
      hasPlaceholders: Object.keys(placeholderValues).length > 0
    });

    // For now, mark all as requiring frontend processing
    // The frontend integration service should handle the actual sending
    // Note: Recipients are already filtered for phone numbers and WhatsApp permission at the main level
    for (const recipient of recipients) {
      const phoneNumber = recipient.whatsapp_number || recipient.phone;
      // This check should not be needed since we filter at the main level, but keeping for safety
      if (!phoneNumber) {
        failed++;
        await this.logSendingAttempt(campaign.id, campaign.name, recipient, 'whatsapp', 'failed', null, 'No phone number');
        continue;
      }

      // Generate WhatsApp URL and log as pending frontend processing
      const formattedPhone = this.formatPhoneNumber(phoneNumber);

      // Clean HTML content and personalize message
      let personalizedMessage = campaign.whatsapp_content.replace(/\{name\}/g, recipient.name);

      // Append WhatsApp signature if specified
      if (campaign.whatsapp_signature_id) {
        try {
          const signature = await database.get(`
            SELECT content FROM message_signatures
            WHERE id = ? AND channel = 'whatsapp' AND is_active = 1
          `, [campaign.whatsapp_signature_id]);

          if (signature && signature.content) {
            console.log(`💬 Appending WhatsApp signature: ${campaign.whatsapp_signature_id}`);
            personalizedMessage += '\n\n' + signature.content;
          } else {
            console.warn(`⚠️ WhatsApp signature not found or inactive: ${campaign.whatsapp_signature_id}`);
          }
        } catch (error) {
          console.error('❌ Error fetching WhatsApp signature:', error);
        }
      }

      // Parse campaign-specific placeholder values
      let placeholderValues = {};
      if (campaign.campaign_specific_placeholder_values) {
        try {
          placeholderValues = typeof campaign.campaign_specific_placeholder_values === 'string'
            ? JSON.parse(campaign.campaign_specific_placeholder_values)
            : campaign.campaign_specific_placeholder_values;
        } catch (e) {
          console.error('❌ Error parsing campaign placeholder values for WhatsApp:', e);
          placeholderValues = {};
        }
      }

      // Replace campaign-specific placeholders (including system placeholders)
      for (const [placeholder, value] of Object.entries(placeholderValues)) {
        const placeholderRegex = new RegExp(`\\{\\{\\s*${placeholder}\\s*\\}\\}`, 'gi');
        personalizedMessage = personalizedMessage.replace(placeholderRegex, value || '');
      }

      // Remove HTML tags for WhatsApp and clean up (preserve line breaks)
      personalizedMessage = personalizedMessage
        .replace(/<[^>]*>/g, '')           // Remove HTML tags
        .replace(/&nbsp;/g, ' ')          // Replace &nbsp; with space
        .replace(/&amp;/g, '&')           // Replace &amp; with &
        .replace(/&lt;/g, '<')            // Replace &lt; with <
        .replace(/&gt;/g, '>')            // Replace &gt; with >
        .replace(/&quot;/g, '"')          // Replace &quot; with "
        .replace(/&#39;/g, "'")           // Replace &#39; with '
        .replace(/[ \t]+/g, ' ')          // Replace multiple spaces/tabs with single space (preserve newlines)
        .replace(/\n\s+/g, '\n')          // Remove spaces after newlines
        .replace(/\s+\n/g, '\n')          // Remove spaces before newlines
        .trim();

      const whatsappUrl = `whatsapp://send?phone=${formattedPhone}&text=${encodeURIComponent(personalizedMessage)}`;

      await this.logSendingAttempt(campaign.id, campaign.name, recipient, 'whatsapp', 'pending', whatsappUrl, 'WhatsApp URL generated - requires frontend to open');
      sent++; // Count as sent since URL is ready for frontend
    }

    console.log(`💬 [INTEGRATION] WhatsApp campaign logged - requires frontend integration to complete`);
    console.log(`💬 [INTEGRATION] Frontend should use CampaignSendingIntegration.handleWhatsAppCampaignSending()`);

    return { sent, failed };
  }

  /**
   * Format phone number for WhatsApp
   */
  formatPhoneNumber(phone) {
    // Remove all non-digits
    const digits = phone.replace(/\D/g, '');

    // Add country code if not present (assuming India +91)
    if (digits.length === 10) {
      return `91${digits}`;
    }

    // Remove leading + if present
    if (digits.startsWith('91') && digits.length === 12) {
      return digits;
    }

    return digits;
  }

  /**
   * Send SMS campaign
   */
  async sendSMSCampaign(campaign, recipients) {
    console.log(`📱 Sending SMS campaign: ${campaign.name}`);

    let sent = 0;
    let failed = 0;

    if (!this.smsService) {
      console.log('⚠️ SMS service not initialized - skipping SMS sending');
      for (const recipient of recipients) {
        failed++;
        await this.logSendingAttempt(campaign.id, campaign.name, recipient, 'sms', 'failed', null, 'SMS service not configured');
      }
      return { sent, failed };
    }

    // Real SMS sending
    // Note: Recipients are already filtered for phone numbers and SMS permission at the main level
    for (const recipient of recipients) {
      try {
        // This check should not be needed since we filter at the main level, but keeping for safety
        if (!recipient.phone) {
          failed++;
          await this.logSendingAttempt(campaign.id, campaign.name, recipient, 'sms', 'failed', null, 'No phone number');
          continue;
        }

        console.log(`📱 Sending SMS to ${recipient.phone}`);

        // Parse campaign-specific placeholder values
        let placeholderValues = {};
        if (campaign.campaign_specific_placeholder_values) {
          try {
            placeholderValues = typeof campaign.campaign_specific_placeholder_values === 'string' 
              ? JSON.parse(campaign.campaign_specific_placeholder_values)
              : campaign.campaign_specific_placeholder_values;
          } catch (e) {
            console.error('❌ Error parsing campaign placeholder values for SMS:', e);
            placeholderValues = {};
          }
        }

        // Personalize SMS message with all placeholders
        let personalizedMessage = campaign.sms_content;

        // Replace recipient-specific placeholders
        personalizedMessage = personalizedMessage.replace(/\{name\}/g, recipient.name || 'there');

        // Replace campaign-specific placeholders (including system placeholders)
        for (const [placeholder, value] of Object.entries(placeholderValues)) {
          const placeholderRegex = new RegExp(`\\{\\{\\s*${placeholder}\\s*\\}\\}`, 'gi');
          personalizedMessage = personalizedMessage.replace(placeholderRegex, value || '');
        }

        // Append SMS signature if specified
        if (campaign.sms_signature_id) {
          try {
            const signature = await database.get(`
              SELECT content FROM message_signatures
              WHERE id = ? AND channel = 'sms' AND is_active = 1
            `, [campaign.sms_signature_id]);

            if (signature && signature.content) {
              console.log(`📱 Appending SMS signature: ${campaign.sms_signature_id}`);
              personalizedMessage += '\n\n' + signature.content;
            } else {
              console.warn(`⚠️ SMS signature not found or inactive: ${campaign.sms_signature_id}`);
            }
          } catch (error) {
            console.error('❌ Error fetching SMS signature:', error);
          }
        }

        console.log('📱 Final personalized SMS message preview:', personalizedMessage.substring(0, 100) + '...');

        // Use real SMS service
        const result = await this.smsService.sendSMS({
          to: recipient.phone,
          message: personalizedMessage,
          campaignId: campaign.id
        });

        if (result.success) {
          sent++;
          console.log(`✅ SMS sent to ${recipient.phone}: ${result.messageId}`);
        } else {
          failed++;
          console.log(`❌ Failed to send SMS to ${recipient.phone}: ${result.error}`);
        }

        // Rate limiting - reduced delay for better performance
        await new Promise(resolve => setTimeout(resolve, 300)); // Reduced from 1000ms to 300ms

      } catch (error) {
        failed++;
        console.error(`❌ Error sending SMS to ${recipient.phone}:`, error);
      }
    }

    return { sent, failed };
  }

  /**
   * Mark campaign as complete
   */
  async markCampaignComplete(campaignId, status, sent, failed, errorMessage = null) {
    try {
      const now = DateTime.now().setZone(this.timezone);

      // Check if new columns exist
      const tableInfo = await database.all("PRAGMA table_info(campaigns)");
      const columnNames = tableInfo.map(col => col.name);

      const hasCompletedDate = columnNames.includes('completed_date');
      const hasSentCount = columnNames.includes('sent_count');
      const hasFailedCount = columnNames.includes('failed_count');
      const hasErrorMessage = columnNames.includes('error_message');

      // Build update query based on available columns
      let updateFields = ['status = ?', 'updated_at = ?'];
      let values = [status, now.toISO()];

      if (hasSentCount) {
        updateFields.push('sent_count = ?');
        values.push(sent);
      }

      if (hasFailedCount) {
        updateFields.push('failed_count = ?');
        values.push(failed);
      }

      if (hasCompletedDate) {
        updateFields.push('completed_date = ?');
        values.push(now.toISO());
      }

      if (hasErrorMessage && errorMessage) {
        updateFields.push('error_message = ?');
        values.push(errorMessage);
      }

      values.push(campaignId);

      const query = `UPDATE campaigns SET ${updateFields.join(', ')} WHERE id = ?`;

      await database.run(query, values);

      console.log(`📊 Campaign ${campaignId} marked as ${status}: ${sent} sent, ${failed} failed`);
    } catch (error) {
      console.error(`❌ Error updating campaign ${campaignId} status:`, error);
    }
  }

  /**
   * Get count of pending WhatsApp messages for a campaign
   */
  async getPendingWhatsAppCount(campaignId) {
    try {
      const result = await database.get(`
        SELECT COUNT(*) as count
        FROM campaign_sending_logs
        WHERE campaign_id = ? AND channel = 'whatsapp' AND status = 'pending'
      `, [campaignId]);

      console.log(`📊 Pending WhatsApp count for campaign ${campaignId}: ${result?.count || 0}`);
      return result?.count || 0;
    } catch (error) {
      console.error('❌ Error getting pending WhatsApp count:', error);
      return 0;
    }
  }

  /**
   * Get count of SMS failures for a campaign
   */
  async getSMSFailureCount(campaignId) {
    try {
      const result = await database.get(`
        SELECT COUNT(*) as count
        FROM campaign_sending_logs
        WHERE campaign_id = ? AND channel = 'sms' AND status = 'failed'
      `, [campaignId]);

      console.log(`📊 SMS failure count for campaign ${campaignId}: ${result?.count || 0}`);
      return result?.count || 0;
    } catch (error) {
      console.error('❌ Error getting SMS failure count:', error);
      return 0;
    }
  }

  /**
   * Get detailed delivery status for a campaign
   */
  async getCampaignDeliveryStatus(campaignId) {
    try {
      const stats = await database.all(`
        SELECT
          channel,
          status,
          COUNT(*) as count
        FROM campaign_sending_logs
        WHERE campaign_id = ?
        GROUP BY channel, status
        ORDER BY channel, status
      `, [campaignId]);

      const deliveryStatus = {
        email: { sent: 0, failed: 0, pending: 0 },
        whatsapp: { sent: 0, failed: 0, pending: 0 },
        sms: { sent: 0, failed: 0, pending: 0 }
      };

      stats.forEach(stat => {
        if (deliveryStatus[stat.channel]) {
          deliveryStatus[stat.channel][stat.status] = stat.count;
        }
      });

      return deliveryStatus;
    } catch (error) {
      console.error('❌ Error getting campaign delivery status:', error);
      return null;
    }
  }

  /**
   * Get sending statistics
   */
  async getSendingStats() {
    try {
      // Check if new columns exist first
      const tableInfo = await database.all("PRAGMA table_info(campaigns)");
      const columnNames = tableInfo.map(col => col.name);

      const hasCompletedDate = columnNames.includes('completed_date');
      const hasSentCount = columnNames.includes('sent_count');
      const hasFailedCount = columnNames.includes('failed_count');

      // Build query based on available columns
      let query = `
        SELECT
          COUNT(CASE WHEN status = 'sending' THEN 1 END) as currently_sending,
          COUNT(CASE WHEN status = 'sent' AND (
            (sent_date IS NOT NULL AND DATE(sent_date) = DATE('now', 'localtime')) OR
            (sent_date IS NULL AND DATE(${hasCompletedDate ? 'completed_date' : 'updated_at'}) = DATE('now', 'localtime'))
          ) THEN 1 END) as sent_today,
          COUNT(CASE WHEN status = 'failed' AND DATE(updated_at) = DATE('now', 'localtime') THEN 1 END) as failed_today,
          COUNT(CASE WHEN status = 'partially_sent' THEN 1 END) as partially_sent
      `;

      if (hasSentCount && hasFailedCount) {
        query += `,
          SUM(CASE WHEN status IN ('sent', 'partially_sent') THEN sent_count ELSE 0 END) as total_sent_today,
          SUM(CASE WHEN status IN ('failed', 'partially_sent') THEN failed_count ELSE 0 END) as total_failed_today
        `;
      } else {
        query += `,
          0 as total_sent_today,
          0 as total_failed_today
        `;
      }

      query += ` FROM campaigns`;

      const stats = await database.get(query);

      // Get pending message counts from sending logs (ALL pending, not just today)
      const pendingStats = await database.get(`
        SELECT
          COUNT(CASE WHEN channel = 'whatsapp' AND status = 'pending' THEN 1 END) as pending_whatsapp,
          COUNT(CASE WHEN channel = 'sms' AND status = 'pending' THEN 1 END) as pending_sms,
          COUNT(CASE WHEN channel = 'email' AND status = 'pending' THEN 1 END) as pending_email,
          COUNT(CASE WHEN status = 'pending' THEN 1 END) as total_pending
        FROM campaign_sending_logs
      `);

      console.log('📊 Pending message counts (all time):', pendingStats);

      return {
        currently_sending: stats?.currently_sending || 0,
        active_sends: this.activeSends.size,
        sent_today: stats?.sent_today || 0,
        failed_today: stats?.failed_today || 0,
        partially_sent: stats?.partially_sent || 0,
        total_sent_today: stats?.total_sent_today || 0,
        total_failed_today: stats?.total_failed_today || 0,
        pending_whatsapp: pendingStats?.pending_whatsapp || 0,
        pending_sms: pendingStats?.pending_sms || 0,
        pending_email: pendingStats?.pending_email || 0,
        total_pending: pendingStats?.total_pending || 0
      };
    } catch (error) {
      console.error('❌ Error getting sending stats:', error);
      return {
        currently_sending: 0,
        active_sends: 0,
        sent_today: 0,
        failed_today: 0,
        partially_sent: 0,
        total_sent_today: 0,
        total_failed_today: 0,
        pending_whatsapp: 0,
        pending_sms: 0,
        pending_email: 0,
        total_pending: 0
      };
    }
  }

  /**
   * Get active sending operations
   */
  getActiveSends() {
    return Array.from(this.activeSends.entries()).map(([campaignId, info]) => ({
      campaignId,
      startTime: info.startTime,
      status: info.status,
      duration: Date.now() - info.startTime.getTime()
    }));
  }

  /**
   * Manually trigger sending check
   */
  async triggerSending() {
    console.log('👆 Manual trigger: Processing sending campaigns...');
    await this.processSendingCampaigns();
  }
}

export const campaignSendingService = new CampaignSendingService();
