package com.crmsms.simple;

import android.Manifest;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.telephony.SmsManager;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import org.json.JSONArray;
import org.json.JSONObject;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class MainActivity extends AppCompatActivity {
    private static final int SMS_PERMISSION_CODE = 101;
    private TextView statusText;
    private Button sendButton;
    private ExecutorService executor;
    private String serverUrl = "http://************:3001"; // Update with your server IP

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        statusText = findViewById(R.id.statusText);
        sendButton = findViewById(R.id.sendButton);
        executor = Executors.newSingleThreadExecutor();

        sendButton.setOnClickListener(v -> {
            if (checkSMSPermission()) {
                processPendingSMS();
            } else {
                requestSMSPermission();
            }
        });

        updateStatus("Ready to send SMS", false);
    }

    private boolean checkSMSPermission() {
        return ContextCompat.checkSelfPermission(this, Manifest.permission.SEND_SMS) 
               == PackageManager.PERMISSION_GRANTED;
    }

    private void requestSMSPermission() {
        ActivityCompat.requestPermissions(this, 
            new String[]{Manifest.permission.SEND_SMS}, SMS_PERMISSION_CODE);
    }

    private void processPendingSMS() {
        updateStatus("Fetching pending SMS...", true);
        
        executor.execute(() -> {
            try {
                // Fetch pending messages from CRM
                URL url = new URL(serverUrl + "/api/sms/pending");
                HttpURLConnection conn = (HttpURLConnection) url.openConnection();
                conn.setRequestMethod("GET");
                conn.setConnectTimeout(5000);
                conn.setReadTimeout(5000);
                
                if (conn.getResponseCode() == 200) {
                    BufferedReader reader = new BufferedReader(
                        new InputStreamReader(conn.getInputStream()));
                    StringBuilder response = new StringBuilder();
                    String line;
                    
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    reader.close();
                    
                    // Parse and send SMS messages
                    JSONArray messages = new JSONArray(response.toString());
                    runOnUiThread(() -> updateStatus("Found " + messages.length() + " messages", true));
                    
                    int sent = 0;
                    int failed = 0;
                    
                    for (int i = 0; i < messages.length(); i++) {
                        JSONObject msg = messages.getJSONObject(i);
                        String recipient = msg.getString("recipient");
                        String message = msg.getString("message");
                        String messageId = msg.getString("id");
                        
                        runOnUiThread(() -> updateStatus("Sending to " + recipient + "...", true));
                        
                        try {
                            SmsManager smsManager = SmsManager.getDefault();
                            smsManager.sendTextMessage(recipient, null, message, null, null);
                            sent++;
                            
                            // Report success to server
                            reportStatus(messageId, "sent", null);
                            
                            Thread.sleep(1000); // Delay between messages
                            
                        } catch (Exception e) {
                            failed++;
                            reportStatus(messageId, "failed", e.getMessage());
                        }
                    }
                    
                    final int finalSent = sent;
                    final int finalFailed = failed;
                    runOnUiThread(() -> {
                        updateStatus("Complete: " + finalSent + " sent, " + finalFailed + " failed", false);
                        Toast.makeText(this, "SMS sending complete!", Toast.LENGTH_LONG).show();
                    });
                    
                } else {
                    runOnUiThread(() -> updateStatus("Server error: " + conn.getResponseCode(), false));
                }
                
            } catch (Exception e) {
                runOnUiThread(() -> updateStatus("Error: " + e.getMessage(), false));
            }
        });
    }

    private void reportStatus(String messageId, String status, String error) {
        executor.execute(() -> {
            try {
                URL url = new URL(serverUrl + "/api/sms/status");
                HttpURLConnection conn = (HttpURLConnection) url.openConnection();
                conn.setRequestMethod("POST");
                conn.setRequestProperty("Content-Type", "application/json");
                conn.setDoOutput(true);
                
                JSONObject data = new JSONObject();
                data.put("messageId", messageId);
                data.put("status", status);
                if (error != null) data.put("error", error);
                data.put("timestamp", System.currentTimeMillis());
                
                conn.getOutputStream().write(data.toString().getBytes());
                conn.getOutputStream().flush();
                conn.getOutputStream().close();
                
                conn.getResponseCode(); // Trigger the request
                
            } catch (Exception e) {
                // Ignore reporting errors
            }
        });
    }

    private void updateStatus(String message, boolean isProcessing) {
        statusText.setText(message);
        sendButton.setEnabled(!isProcessing);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == SMS_PERMISSION_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                processPendingSMS();
            } else {
                Toast.makeText(this, "SMS permission required", Toast.LENGTH_SHORT).show();
            }
        }
    }
}
