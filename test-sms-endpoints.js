// Test SMS endpoints
const baseUrl = 'http://localhost:3001/api/sms';

async function testSMSEndpoints() {
  console.log('🧪 Testing SMS Gateway Endpoints...\n');

  // Test 1: Status endpoint
  try {
    console.log('1. Testing /api/sms/status...');
    const response = await fetch(`${baseUrl}/status`);
    const data = await response.json();
    console.log('   ✅ Status:', data);
  } catch (error) {
    console.log('   ❌ Status error:', error.message);
  }

  // Test 2: Pending messages
  try {
    console.log('\n2. Testing /api/sms/pending...');
    const response = await fetch(`${baseUrl}/pending`);
    const data = await response.json();
    console.log('   ✅ Pending messages:', data.length, 'found');
    if (data.length > 0) {
      console.log('   📱 First message:', data[0]);
    }
  } catch (error) {
    console.log('   ❌ Pending error:', error.message);
  }

  // Test 3: Statistics
  try {
    console.log('\n3. Testing /api/sms/stats...');
    const response = await fetch(`${baseUrl}/stats`);
    const data = await response.json();
    console.log('   ✅ Statistics:', data);
  } catch (error) {
    console.log('   ❌ Stats error:', error.message);
  }

  // Test 4: Traccar config (should fail initially)
  try {
    console.log('\n4. Testing /api/sms/traccar/test...');
    const response = await fetch(`${baseUrl}/traccar/test`);
    const data = await response.json();
    console.log('   📱 Traccar test:', data);
  } catch (error) {
    console.log('   ❌ Traccar error:', error.message);
  }

  console.log('\n🏁 SMS endpoint testing complete!');
}

// Run the test
testSMSEndpoints().catch(console.error);
