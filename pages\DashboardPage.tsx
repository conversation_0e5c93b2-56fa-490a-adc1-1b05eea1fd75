import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import Header from '../components/Header';
import Button from '../components/Button';
import ToastNotification from '../components/ToastNotification';
import {
  CampaignIcon,
  TemplateIcon,
  UserIcon,
  SignatureIcon,
  CalendarDaysIcon,
  CheckCircleIcon,
  ClockIcon,
  CakeIcon,
  SubscribersIcon,
  SunIcon,
  CloudIcon,
  CloudRainIcon,
  InformationCircleIcon,
} from '../components/icons';
import {
  Campaign,
  CampaignStatus,
  CampaignType,
  TemplateStatus,
  User,
  UserRole,
  MessageSignature,
  Channel,
  Subscriber,
  SubscriberProfileStatus,
  CampaignTemplate,
  RecurrenceType,
  AreaOfInterest,
  HolidayHandlingRule,
  RegistrationDetails,
  DisplaySettings,
} from '../types';
import { initialRegistrationDetails } from '../constants';
import { templateService } from '../services/TemplateService-API';
import { userService } from '../services/userService-API';
import { signatureService } from '../services/SignatureService-API';
import { subscriberService } from '../services/SubscriberService-API';
import { areaOfInterestService } from '../services/AreaOfInterestService-API';
import BirthdayPromptModal from '../components/BirthdayPromptModal';
import {
  calculateEffectiveSendDate,
  formatDateToYyyyMmDd,
  getNextBirthday,
  formatDateForDisplay,
} from '../utils/dateUtils';
import { formatEnumValueForDisplay } from '../utils/displayUtils';
import WeatherDisplay from '../components/WeatherDisplay';
import { useDisplaySettings } from '../contexts/DisplaySettingsContext';
import { useAuth } from '../contexts/AuthContextDB';
import { canUserViewItem, AccessibleItem } from '../utils/accessControlUtils';
import { campaignService } from '../services/campaignService-API';
import { birthdayScheduler } from '../services/BirthdaySchedulerService';
import { runBirthdayAutomation } from '../services/BirthdayAutomationService';
import { apiClient } from '../services/apiClient';
import CampaignExecutionStatus from '../components/CampaignExecutionStatus';

interface PopoverItem {
  id: string;
  name: string;
  path: string;
}

interface StatCardProps {
  title: string;
  value: number | string;
  icon: React.ReactNode;
  borderColorClass: string;
  iconColorClass: string;
  onDoubleClick?: () => void;
  popoverTitle?: string;
  popoverItems?: PopoverItem[];
}

interface WeatherData {
  locationName: string;
  temperature?: string;
  feelsLike?: string;
  description?: string;
  humidity?: string;
  windSpeed?: string;
  icon?: React.ReactNode;
}

interface Notification {
  type: 'success' | 'error';
  message: string;
}

const DashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { displaySettings } = useDisplaySettings();
  const { currentUser } = useAuth();
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [templates, setTemplates] = useState<CampaignTemplate[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [signatures, setSignatures] = useState<MessageSignature[]>([]);
  const [subscribers, setSubscribers] = useState<Subscriber[]>([]);
  const [areasOfInterest, setAreasOfInterest] = useState<AreaOfInterest[]>([]);
  const [showBirthdayPrompt, setShowBirthdayPrompt] = useState(false);
  const [todaysBirthdayCount, setTodaysBirthdayCount] = useState(0);
  const [weatherData, setWeatherData] = useState<WeatherData | null>(null);
  const [isLoadingWeather, setIsLoadingWeather] = useState<boolean>(true);
  const [weatherError, setWeatherError] = useState<string | null>(null);
  const [weatherLocation, setWeatherLocation] = useState<string>('411002, India');
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [isBackgroundRefreshing, setIsBackgroundRefreshing] = useState(false);

  const [isBirthdayAutomationLoading, setIsBirthdayAutomationLoading] = useState(false);
  const [notification, setNotification] = useState<Notification | null>(null);

  // 🎨 Get Left Shadow Based on Border Class - Each card uses its own colored shadow
  const getLeftShadowStyle = (borderColorClass: string) => {
    switch (borderColorClass) {
      case 'border-primary':
        return {
          boxShadow: '-5px 0 8px -2px rgba(59, 130, 246, 0.5), 0 1px 3px 0 rgba(0, 0, 0, 0.1)'
        };
      case 'border-emerald-500':
        return {
          boxShadow: '-5px 0 8px -2px rgba(16, 185, 129, 0.5), 0 1px 3px 0 rgba(0, 0, 0, 0.1)'
        };
      case 'border-cyan-500':
        return {
          boxShadow: '-5px 0 8px -2px rgba(6, 182, 212, 0.5), 0 1px 3px 0 rgba(0, 0, 0, 0.1)'
        };
      case 'border-purple-500':
        return {
          boxShadow: '-5px 0 8px -2px rgba(168, 85, 247, 0.5), 0 1px 3px 0 rgba(0, 0, 0, 0.1)'
        };
      case 'border-red-500':
        return {
          boxShadow: '-5px 0 8px -2px rgba(239, 68, 68, 0.5), 0 1px 3px 0 rgba(0, 0, 0, 0.1)'
        };
      case 'border-orange-500':
        return {
          boxShadow: '-5px 0 8px -2px rgba(249, 115, 22, 0.5), 0 1px 3px 0 rgba(0, 0, 0, 0.1)'
        };
      default:
        return {
          boxShadow: '-5px 0 8px -2px rgba(0, 0, 0, 0.3), 0 1px 3px 0 rgba(0, 0, 0, 0.1)'
        };
    }
  };

  // 🎨 Get Icon Background Color Based on Border Class - Colored background like 4th card
  const getIconBackgroundClass = (borderColorClass: string) => {
    switch (borderColorClass) {
      case 'border-primary':
        return 'bg-blue-50 dark:bg-blue-900/20';
      case 'border-emerald-500':
        return 'bg-emerald-50 dark:bg-emerald-900/20';
      case 'border-cyan-500':
        return 'bg-cyan-50 dark:bg-cyan-900/20';
      case 'border-purple-500':
        return 'bg-purple-50 dark:bg-purple-900/20';
      case 'border-red-500':
        return 'bg-red-50 dark:bg-red-900/20';
      case 'border-orange-500':
        return 'bg-orange-50 dark:bg-orange-900/20';
      default:
        return 'bg-surface';
    }
  };

  // 🎨 Get Hover Shadow Based on Border Class - Each card uses its own colored hover shadow
  const getHoverShadowStyle = (borderColorClass: string) => {
    switch (borderColorClass) {
      case 'border-primary':
        return {
          boxShadow: '-8px 0 16px -3px rgba(59, 130, 246, 0.6), 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
        };
      case 'border-emerald-500':
        return {
          boxShadow: '-8px 0 16px -3px rgba(16, 185, 129, 0.6), 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
        };
      case 'border-cyan-500':
        return {
          boxShadow: '-8px 0 16px -3px rgba(6, 182, 212, 0.6), 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
        };
      case 'border-purple-500':
        return {
          boxShadow: '-8px 0 16px -3px rgba(168, 85, 247, 0.6), 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
        };
      case 'border-red-500':
        return {
          boxShadow: '-8px 0 16px -3px rgba(239, 68, 68, 0.6), 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
        };
      case 'border-orange-500':
        return {
          boxShadow: '-8px 0 16px -3px rgba(249, 115, 22, 0.6), 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
        };
      default:
        return {
          boxShadow: '-8px 0 16px -3px rgba(0, 0, 0, 0.4), 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
        };
    }
  };

  // 💠 StatCard Component
  const StatCard: React.FC<StatCardProps> = ({
    title,
    value,
    icon,
    borderColorClass,
    iconColorClass,
    onDoubleClick,
    popoverTitle,
    popoverItems,
  }) => {
    const [showPopover, setShowPopover] = useState(false);
    const [isHovered, setIsHovered] = useState(false);
    const shadowStyle = isHovered ? getHoverShadowStyle(borderColorClass) : getLeftShadowStyle(borderColorClass);

    return (
      <div
        className={`relative bg-background p-4 rounded-lg border-l-4 ${borderColorClass} transition-all duration-200 cursor-pointer min-h-[120px] flex flex-col`}
        style={shadowStyle}
        onDoubleClick={onDoubleClick}
        onMouseEnter={() => {
          setIsHovered(true);
          if (popoverItems && popoverItems.length > 0) setShowPopover(true);
        }}
        onMouseLeave={() => {
          setIsHovered(false);
          setShowPopover(false);
        }}
      >
        {/* Icon and Count Row */}
        <div className="flex items-center justify-between mb-3">
          <div className={`flex-shrink-0 p-2.5 ${getIconBackgroundClass(borderColorClass)} rounded-full ${iconColorClass}`}>{icon}</div>
          <p className="text-2xl font-bold text-primary leading-none">{value}</p>
        </div>

        {/* Title Text Below */}
        <div className="flex-1">
          <p className="text-sm text-secondary leading-tight break-words">{title}</p>
        </div>

        {/* ✅ Popover with improved visibility */}
        {showPopover && popoverItems && popoverItems.length > 0 && (
          <div
            className="absolute top-full left-0 mt-2 w-72 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-2xl p-4 z-50 max-h-64 overflow-y-auto"
            style={{
              boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
            }}
          >
            {popoverTitle && (
              <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 border-b border-gray-200 dark:border-gray-600 pb-2">{popoverTitle}</h4>
            )}
            <ul className="space-y-2">
              {popoverItems.map((item) => (
                <li
                  key={item.id}
                  className="text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-700 p-2 rounded-md cursor-pointer transition-all duration-200 break-words"
                  onClick={(e) => {
                    e.stopPropagation();
                    navigate(item.path);
                    setShowPopover(false);
                  }}
                >
                  {item.name}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    );
  };

  useEffect(() => {
    setWeatherLocation('411002, India');
  }, []);

  useEffect(() => {
    if (!displaySettings.showWeatherReport) {
      setIsLoadingWeather(false);
      setWeatherData(null);
      setWeatherError(null);
      return;
    }

    const fetchWeather = async () => {
      setIsLoadingWeather(true);
      setWeatherError(null);
      try {
        const response = await fetch(`https://wttr.in/${encodeURIComponent(weatherLocation)}?format=j1`);
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        const data = await response.json();

        const current = data.current_condition?.[0];
        if (!current) throw new Error('Unexpected weather data');

        const weatherDesc = current.weatherDesc?.[0]?.value.toLowerCase() || '';
        let iconNode: React.ReactNode = <CloudIcon className="h-6 w-6" />;
        if (weatherDesc.includes('sunny') || weatherDesc.includes('clear'))
          iconNode = <SunIcon className="h-6 w-6" />;
        else if (weatherDesc.includes('rain') || weatherDesc.includes('shower'))
          iconNode = <CloudRainIcon className="h-6 w-6" />;
        else if (weatherDesc.includes('cloudy') || weatherDesc.includes('overcast'))
          iconNode = <CloudIcon className="h-6 w-6" />;

        setWeatherData({
          locationName: data.nearest_area?.[0]?.areaName?.[0]?.value || weatherLocation,
          temperature: current.temp_C,
          feelsLike: current.FeelsLikeC,
          description: current.weatherDesc?.[0]?.value,
          humidity: current.humidity,
          windSpeed: current.windspeedKmph,
          icon: iconNode,
        });
      } catch (error) {
        console.error('Failed to fetch weather:', error);
        setWeatherError((error as Error).message || 'Unknown error');
        setWeatherData(null);
      } finally {
        setIsLoadingWeather(false);
      }
    };

    if (displaySettings.showWeatherReport && weatherLocation) {
      fetchWeather();
    }
  }, [weatherLocation, displaySettings.showWeatherReport]);

  const loadAndFilterData = useCallback(async (isBackgroundRefresh = false) => {
    if (!isBackgroundRefresh) setIsLoadingData(true);
    else setIsBackgroundRefreshing(true);

    try {
      const fetchedCampaigns = await campaignService.getAllCampaigns();
      const allTemplates = await templateService.getAllTemplates();
      const allUsers = await userService.getAllUsers();
      const allSignatures = await signatureService.getAllSignatures();
      const allSubscribers = await subscriberService.getAllSubscribers();
      const allAreasOfInterest = await areaOfInterestService.getAllAreasOfInterest();

      const filteredCampaigns = fetchedCampaigns.filter((c) =>
        canUserViewItem(c as AccessibleItem, currentUser)
      );
      setCampaigns(filteredCampaigns);
      setTemplates(allTemplates.filter((t) => canUserViewItem(t as AccessibleItem, currentUser)));
      setUsers(allUsers);
      setSignatures(
        allSignatures.filter((s) => canUserViewItem(s as AccessibleItem, currentUser))
      );

      const filteredSubscribers = allSubscribers.filter((s) =>
        canUserViewItem(s as AccessibleItem, currentUser)
      );
      setSubscribers(filteredSubscribers);
      setAreasOfInterest(
        allAreasOfInterest.filter((a) => canUserViewItem(a as AccessibleItem, currentUser))
      );

      return { filteredCampaigns, filteredSubscribers };
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      setCampaigns([]);
      setSubscribers([]);
      return { filteredCampaigns: [], filteredSubscribers: [] };
    } finally {
      if (!isBackgroundRefresh) setIsLoadingData(false);
      else setIsBackgroundRefreshing(false);
    }
  }, [currentUser]);

  const checkTodaysBirthdays = useCallback(
    (currentSubscribers: Subscriber[], currentCampaigns: Campaign[]) => {
      const todayStr = formatDateToYyyyMmDd(new Date());
      const activeBirthdayCampaigns = currentCampaigns.filter(
        (c) =>
          c.campaign_type === CampaignType.BIRTHDAY_WISH &&
          c.status === CampaignStatus.SCHEDULED &&
          canUserViewItem(c as AccessibleItem, currentUser)
      );

      if (!activeBirthdayCampaigns.length) return;

      const subscribersWithBirthdayToday = new Set<string>();
      currentSubscribers.forEach((subscriber) => {
        if (subscriber.birthDate && subscriber.status === SubscriberProfileStatus.ACTIVE) {
          activeBirthdayCampaigns.forEach((campaign) => {
            const effectiveSendDate = calculateEffectiveSendDate(subscriber.birthDate!, campaign);
            if (effectiveSendDate && formatDateToYyyyMmDd(effectiveSendDate) === todayStr) {
              subscribersWithBirthdayToday.add(subscriber.id);
            }
          });
        }
      });

      if (subscribersWithBirthdayToday.size > 0) {
        setTodaysBirthdayCount(subscribersWithBirthdayToday.size);
        setShowBirthdayPrompt(true);
      }
    },
    [currentUser]
  );

  useEffect(() => {
    const fetchDataAndCheckBirthdays = async (isBackground = false) => {
      const { filteredCampaigns, filteredSubscribers } = await loadAndFilterData(isBackground);
      checkTodaysBirthdays(filteredSubscribers, filteredCampaigns);
    };

    fetchDataAndCheckBirthdays(false);

    const refreshInterval = setInterval(() => {      fetchDataAndCheckBirthdays(true);
    }, 30000);

    return () => clearInterval(refreshInterval);
  }, [loadAndFilterData, checkTodaysBirthdays]);



  const handleBirthdayPromptConfirm = () => {
    setShowBirthdayPrompt(false);
    navigate('/subscribers');
  };

  const handleBirthdayPromptClose = () => {
    setShowBirthdayPrompt(false);
  };

  const triggerBirthdayAutomation = async () => {
    if (!currentUser) {
      setNotification({ type: 'error', message: 'Please login to run birthday automation' });
      return;
    }

    setIsBirthdayAutomationLoading(true);
    setNotification(null);

    try {
      const results = await runBirthdayAutomation(currentUser.user_id);
      const message = `🎉 ${results.created} campaigns created, ${results.skipped} skipped.`;
      setNotification({ type: 'success', message });
      loadAndFilterData(true);
    } catch (error: any) {
      setNotification({ type: 'error', message: `🚨 ${error.message}` });
    } finally {
      setIsBirthdayAutomationLoading(false);
    }
  };

  const viewableCampaigns = campaigns;
  const viewableTemplates = templates;
  const viewableSubscribers = subscribers;

  // Active campaigns are those that are scheduled or currently sending (not completed)
  const allActiveCampaigns = viewableCampaigns.filter(
    (c) => c.status === CampaignStatus.SCHEDULED || c.status === CampaignStatus.SENDING
  );
  const activeCampaignsCount = allActiveCampaigns.length;
  const activeCampaignsPopoverItems: PopoverItem[] = allActiveCampaigns
    .sort((a, b) => a.name.localeCompare(b.name))
    .slice(0, 5)
    .map((c) => ({ id: c.id, name: c.name, path: `/campaigns/edit/${c.id}` }));

  const allActiveTemplates = viewableTemplates.filter(
    (t) => t.status === TemplateStatus.ACTIVE && t.is_active
  );
  const totalTemplatesCount = allActiveTemplates.length;
  const activeTemplatesPopoverItems: PopoverItem[] = allActiveTemplates
    .sort((a, b) => a.display_name.localeCompare(b.display_name))
    .slice(0, 5)
    .map((t) => ({ id: t.id, name: t.display_name, path: `/templates/edit/${t.id}` }));

  const totalSubscribersCount = viewableSubscribers.length;
  const subscribersPopoverItems: PopoverItem[] = viewableSubscribers
    .sort((a, b) => (a.firstName || '').localeCompare(b.firstName || ''))
    .slice(0, 5)
    .map((s) => ({
      id: s.id,
      name: `${s.firstName || ''} ${s.lastName || ''} (${s.email})`.trim(),
      path: `/subscribers/edit/${s.id}`,
    }));

  // Calculate subscribers without areas of interest
  const subscribersWithoutAreas = viewableSubscribers.filter(s => s.areasOfInterestIds.length === 0);
  const subscribersWithoutAreasCount = subscribersWithoutAreas.length;
  const subscribersWithoutAreasPopoverItems: PopoverItem[] = subscribersWithoutAreas
    .sort((a, b) => (a.firstName || '').localeCompare(b.firstName || ''))
    .slice(0, 5)
    .map((s) => ({
      id: s.id,
      name: `${s.firstName || ''} ${s.lastName || ''} (${s.email})`.trim(),
      path: `/subscribers/edit/${s.id}`,
    }));

  // Calculate overdue campaigns
  const now = new Date();
  console.log('🕐 [Dashboard] Current time:', now.toISOString());

  const overdueCampaigns = viewableCampaigns.filter(campaign => {
    if (campaign.status === CampaignStatus.SCHEDULED && campaign.scheduled_date) {
      const scheduledDate = new Date(campaign.scheduled_date);
      const isOverdue = scheduledDate < now;
      console.log(`🕐 [Dashboard] Campaign "${campaign.name}": Scheduled: ${scheduledDate.toISOString()}, Current: ${now.toISOString()}, Overdue: ${isOverdue}`);
      return isOverdue;
    }
    return false;
  });
  const overdueCampaignsCount = overdueCampaigns.length;
  const overdueCampaignsPopoverItems: PopoverItem[] = overdueCampaigns
    .sort((a, b) => new Date(a.scheduled_date!).getTime() - new Date(b.scheduled_date!).getTime())
    .slice(0, 5)
    .map((c) => ({
      id: c.id,
      name: `${c.name} (${formatDateForDisplay(c.scheduled_date!, { type: 'datetime' })})`,
      path: `/campaigns/edit/${c.id}`,
    }));

  const allActiveUsers = users.filter((u) => u.is_active);
  const activeUsersCount = allActiveUsers.length;
  const activeUsersPopoverItems: PopoverItem[] = allActiveUsers
    .sort((a, b) => a.name.localeCompare(b.name))
    .slice(0, 5)
    .map((u) => ({ id: u.user_id, name: u.name, path: `/users/edit/${u.user_id}` }));

  const calculatePercentage = (value: number, total: number): string => {
    if (total === 0) return '0%';
    return `${((value / total) * 100).toFixed(1)}%`;
  };

  const recentlyCompletedCampaigns = viewableCampaigns
    .filter((c) => {
      // Include campaigns that are sent, regardless of sent_date
      if (c.status === CampaignStatus.SENT) return true;
      // Also include failed campaigns as "completed"
      if (c.status === CampaignStatus.FAILED) return true;
      return false;
    })
    .sort((a, b) => {
      // Sort by sent_date if available, otherwise by updated_at
      const aDate = a.sent_date ? new Date(a.sent_date) : new Date(a.updated_at);
      const bDate = b.sent_date ? new Date(b.sent_date) : new Date(b.updated_at);
      return bDate.getTime() - aDate.getTime();
    })
    .slice(0, 5);

  const nextScheduledCampaigns = viewableCampaigns
    .filter(
      (c) =>
        c.status === CampaignStatus.SCHEDULED &&
        c.scheduled_date &&
        c.campaign_type !== CampaignType.BIRTHDAY_WISH
    )
    .sort((a, b) => new Date(a.scheduled_date!).getTime() - new Date(b.scheduled_date!).getTime())
    .slice(0, 5);

  const recentlyActiveSubscribers = viewableSubscribers
    .sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())
    .slice(0, 5);

  const getUpcomingBirthdays = (subList: Subscriber[], daysAhead: number = 30): Subscriber[] => {
    const today = new Date();
    const limitDate = new Date(today);
    limitDate.setDate(today.getDate() + daysAhead);

    return subList
      .filter((sub) => sub.birthDate && sub.status === SubscriberProfileStatus.ACTIVE)
      .map((sub) => {
        const nextBday = getNextBirthday(sub.birthDate!);
        if (!nextBday) return null;
        const diffTime = nextBday.getTime() - today.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return { ...sub, _daysUntilBirthday: diffDays, _nextBirthdayDate: nextBday };
      })
      .filter(
        (sub): sub is Subscriber & { _daysUntilBirthday: number; _nextBirthdayDate: Date } =>
          sub !== null && 
          sub._nextBirthdayDate !== undefined && 
          sub._nextBirthdayDate >= today &&
          sub._nextBirthdayDate <= limitDate
      )
      .sort((a, b) => a._nextBirthdayDate.getTime() - b._nextBirthdayDate.getTime())
      .slice(0, 5);
  };

  const upcomingBirthdays = getUpcomingBirthdays(viewableSubscribers, displaySettings.campaignQueueDays || 30);

  if (isLoadingData) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-background text-primary">
        Loading dashboard data...
      </div>
    );
  }

  return (
    <div className="bg-surface min-h-screen text-primary transition-colors duration-300">
      <div className="max-w-7xl mx-auto p-4 sm:p-6 space-y-6">
        <div className="flex items-center justify-between">
          <Header title="Dashboard" subtitle="Overview of your CRM activities." />
          {isBackgroundRefreshing && (
            <div className="flex items-center text-sm text-secondary">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
              Refreshing data...
            </div>
          )}
        </div>

      {/* Alert Section for Overdue Campaigns */}
      {overdueCampaignsCount > 0 && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 rounded-lg shadow-md">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ClockIcon className="h-6 w-6 text-red-400" />
            </div>
            <div className="ml-3 flex-1">
              <h3 className="text-sm font-medium text-red-800">
                Urgent: Overdue Campaigns Require Attention
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>
                  <strong>{overdueCampaignsCount}</strong> campaign{overdueCampaignsCount !== 1 ? 's have' : ' has'} passed
                  {overdueCampaignsCount !== 1 ? ' their' : ' its'} scheduled time and require immediate action.
                </p>
              </div>
              <div className="mt-3 flex space-x-3">
                <button
                  type="button"
                  onClick={() => navigate('/campaigns')}
                  className="bg-red-500 hover:bg-red-600 text-white text-sm px-3 py-2 rounded-md font-medium transition-colors"
                >
                  Manage Overdue Campaigns
                </button>
                <button
                  type="button"
                  onClick={() => navigate('/campaigns/add')}
                  className="bg-red-100 hover:bg-red-200 text-red-800 text-sm px-3 py-2 rounded-md font-medium transition-colors"
                >
                  Create New Campaign
                </button>
              </div>
            </div>
          </div>
        </div>
      )}



      {/* Weather Report */}
      {displaySettings.showWeatherReport && (
        <div>
          <WeatherDisplay
            locationName={weatherData?.locationName || weatherLocation}
            temperature={weatherData?.temperature}
            feelsLike={weatherData?.feelsLike}
            description={weatherData?.description}
            humidity={weatherData?.humidity}
            windSpeed={weatherData?.windSpeed}
            icon={weatherData?.icon}
            error={weatherError}
            isLoading={isLoadingWeather}
          />
        </div>
      )}

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 sm:gap-6">
        <StatCard
          title="Active Campaigns"
          value={activeCampaignsCount}
          icon={<CampaignIcon className="h-6 w-6" />}
          borderColorClass="border-primary"
          iconColorClass="text-primary"
          onDoubleClick={() => navigate('/campaigns')}
          popoverTitle="Top 5 Active Campaigns"
          popoverItems={activeCampaignsPopoverItems}
        />
        <StatCard
          title="Overdue Campaigns"
          value={overdueCampaignsCount}
          icon={<ClockIcon className="h-6 w-6" />}
          borderColorClass="border-red-500"
          iconColorClass="text-red-500"
          onDoubleClick={() => navigate('/campaigns')}
          popoverTitle="Overdue Campaigns"
          popoverItems={overdueCampaignsPopoverItems}
        />
        <StatCard
          title="Active Templates"
          value={totalTemplatesCount}
          icon={<TemplateIcon className="h-6 w-6" />}
          borderColorClass="border-emerald-500"
          iconColorClass="text-emerald-500"
          onDoubleClick={() => navigate('/templates')}
          popoverTitle="Top 5 Active Templates"
          popoverItems={activeTemplatesPopoverItems}
        />
        <StatCard
          title="Total Subscribers"
          value={totalSubscribersCount}
          icon={<SubscribersIcon className="h-6 w-6" />}
          borderColorClass="border-cyan-500"
          iconColorClass="text-cyan-500"
          onDoubleClick={() => navigate('/subscribers')}
          popoverTitle="Top 5 Subscribers"
          popoverItems={subscribersPopoverItems}
        />
        <StatCard
          title="Active Users"
          value={activeUsersCount}
          icon={<UserIcon className="h-6 w-6" />}
          borderColorClass="border-purple-500"
          iconColorClass="text-purple-500"
          onDoubleClick={() => navigate('/users')}
          popoverTitle="Top 5 Active Users"
          popoverItems={activeUsersPopoverItems}
        />
        <StatCard
          title="No Areas Assigned"
          value={subscribersWithoutAreasCount}
          icon={<InformationCircleIcon className="h-6 w-6" />}
          borderColorClass="border-orange-500"
          iconColorClass="text-orange-500"
          onDoubleClick={() => navigate('/subscribers?filter=no-areas')}
          popoverTitle="Subscribers Without Areas"
          popoverItems={subscribersWithoutAreasPopoverItems}
        />
      </div>

      {/* Campaign Execution Status */}
      <CampaignExecutionStatus className="mb-6" />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          {/* Recently Completed Campaigns */}
          <div className="bg-background p-6 rounded-lg shadow-md border border-default">
            <h3 className="text-xl font-semibold text-primary mb-4">Recently Completed Campaigns</h3>
            {recentlyCompletedCampaigns.length > 0 ? (
              <ul className="space-y-2">
                {recentlyCompletedCampaigns.map((campaign) => (
                  <li
                    key={campaign.id}
                    className="p-3 bg-surface rounded-md hover:bg-opacity-90 transition-colors cursor-pointer border border-default"
                    onDoubleClick={() => navigate(`/campaigns/edit/${campaign.id}`)}
                  >
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="font-medium text-primary">{campaign.name}</p>
                        <p className="text-xs text-secondary">Subject: {campaign.subject}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-primary">
                          Sent: {formatDateForDisplay(campaign.sent_date, { type: 'date' })}
                        </p>
                        <p className="text-xs text-secondary">
                          {campaign.opened}/{campaign.total_recipients} opened (
                          {calculatePercentage(campaign.opened, campaign.total_recipients)})
                        </p>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-secondary">No campaigns completed recently.</p>
            )}
          </div>

          {/* Next Scheduled Campaigns */}
          <div className="bg-background p-6 rounded-lg shadow-md border border-default">
            <h3 className="text-xl font-semibold text-primary mb-4">Next Scheduled Campaigns</h3>
            {nextScheduledCampaigns.length > 0 ? (
              <ul className="space-y-2">
                {nextScheduledCampaigns.map((campaign) => (
                  <li
                    key={campaign.id}
                    className="p-3 bg-surface rounded-md hover:bg-opacity-90 transition-colors cursor-pointer border border-default"
                    onDoubleClick={() => navigate(`/campaigns/edit/${campaign.id}`)}
                  >
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="font-medium text-primary">{campaign.name}</p>
                        <p className="text-xs text-secondary">
                          Type: {formatEnumValueForDisplay(campaign.campaign_type)}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-primary">
                          Scheduled: {formatDateForDisplay(campaign.scheduled_date, { type: 'datetime' })}
                        </p>
                        <p className="text-xs text-secondary">
                          Recipients: {campaign.total_recipients || 'N/A'}
                        </p>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-secondary">No standard campaigns scheduled.</p>
            )}
          </div>
        </div>

        {/* Side Column: Birthdays & Recent Subscribers */}
        <div className="lg:col-span-1 space-y-6">
          {/* Upcoming Birthdays */}
          <div className="bg-background p-6 rounded-lg shadow-md border border-default">
            <h3 className="text-xl font-semibold text-primary mb-4 flex items-center">
              <CakeIcon className="h-6 w-6 text-pink-500 mr-2" />
              Upcoming Birthdays (Next {displaySettings.campaignQueueDays || 30} Days)
            </h3>
            {upcomingBirthdays.length > 0 ? (
              <ul className="space-y-2">
                {upcomingBirthdays.map((sub) => (
                  <li
                    key={sub.id}
                    className="p-3 bg-pink-50 dark:bg-pink-900/30 hover:bg-pink-100 dark:hover:bg-pink-800/40 rounded-md border border-pink-200 dark:border-pink-700 cursor-pointer transition-colors"
                    onDoubleClick={() => navigate(`/subscribers/edit/${sub.id}`)}
                  >
                    <p className="font-medium text-pink-700 dark:text-pink-300">
                      {sub.firstName} {sub.lastName}
                    </p>
                    <p className="text-sm text-pink-600 dark:text-pink-400">
                      {formatDateForDisplay(sub._nextBirthdayDate, { type: 'date' })}{' '}
                      <span className="text-xs">(in {sub._daysUntilBirthday} day{sub._daysUntilBirthday !== 1 ? 's' : ''})</span>
                    </p>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-secondary">No upcoming birthdays in the next {displaySettings.campaignQueueDays || 30} days.</p>
            )}
          </div>

          {/* Recently Updated Subscribers */}
          <div className="bg-background p-6 rounded-lg shadow-md border border-default">
            <h3 className="text-xl font-semibold text-primary mb-4">Recently Updated Subscribers</h3>
            {recentlyActiveSubscribers.length > 0 ? (
              <ul className="space-y-2">
                {recentlyActiveSubscribers.map((sub) => (
                  <li
                    key={sub.id}
                    className="p-3 bg-indigo-50 dark:bg-indigo-900/30 hover:bg-indigo-100 dark:hover:bg-indigo-800/40 rounded-md border border-indigo-200 dark:border-indigo-700 cursor-pointer transition-colors"
                    onDoubleClick={() => navigate(`/subscribers/edit/${sub.id}`)}
                  >
                    <p className="font-medium text-indigo-700 dark:text-indigo-300">
                      {sub.firstName} {sub.lastName} ({sub.email})
                    </p>
                    <p className="text-sm text-indigo-600 dark:text-indigo-400">
                      Status: {formatEnumValueForDisplay(sub.status)}
                    </p>
                    <p className="text-xs text-secondary">
                      Last Updated: {formatDateForDisplay(sub.updated_at, { type: 'datetime' })}
                    </p>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-secondary">No subscribers updated recently.</p>
            )}
          </div>
        </div>
      </div>

      {/* Birthday Prompt Modal */}
      <BirthdayPromptModal
        isOpen={showBirthdayPrompt}
        onClose={handleBirthdayPromptClose}
        onConfirm={handleBirthdayPromptConfirm}
        birthdayCount={todaysBirthdayCount}
      />

      {/* Toast Notification */}
      {notification && (
        <ToastNotification
          message={notification.message}
          type={notification.type}
          onClose={() => setNotification(null)}
          duration={5000}
        />
      )}
      </div>
    </div>
  );
};

export default DashboardPage;