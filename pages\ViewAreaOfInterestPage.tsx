import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { AreaOfInterest } from '../types';
import { areaOfInterestService } from '../services/AreaOfInterestService';
import { ArrowLeftIcon } from '../components/icons';

const ViewAreaOfInterestPage: React.FC = () => {
  const { areaId } = useParams<{ areaId: string }>();
  const navigate = useNavigate();
  const [area, setArea] = useState<AreaOfInterest | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadArea = async () => {
      if (!areaId) {
        setError('No area of interest ID provided');
        setLoading(false);
        return;
      }

      try {
        const areaData = await areaOfInterestService.getAreaOfInterestById(areaId);
        setArea(areaData);
      } catch (err) {
        console.error('Error loading area of interest:', err);
        setError('Failed to load area of interest details');
      } finally {
        setLoading(false);
      }
    };

    loadArea();
  }, [areaId]);

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not set';
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return dateString;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-textPrimary">Loading area of interest details...</div>
      </div>
    );
  }

  if (error || !area) {
    return (
      <div className="min-h-screen bg-background p-6">
        <div className="max-w-4xl mx-auto">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h2 className="text-lg font-semibold text-red-800 mb-2">Error</h2>
            <p className="text-red-600">{error || 'Area of interest not found'}</p>
            <button
              onClick={() => navigate('/areas-of-interest')}
              className="mt-4 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
            >
              Back to Areas of Interest
            </button>
          </div>
        </div>
      </div>
    );
  }

  const fieldClass = "block text-sm font-medium text-textPrimary mb-1";
  const valueClass = "w-full p-3 border border-border rounded-lg bg-gray-50 text-textPrimary";

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-4xl mx-auto p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate('/areas-of-interest')}
              className="flex items-center text-primary hover:text-opacity-80"
            >
              <ArrowLeftIcon className="h-5 w-5 mr-2" />
              Back to Areas of Interest
            </button>
            <h1 className="text-2xl font-bold text-textPrimary">View Area of Interest</h1>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => navigate(`/areas-of-interest/edit/${area.id}`)}
              className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-opacity-90"
            >
              Edit Area
            </button>
          </div>
        </div>

        {/* Area Details */}
        <div className="bg-cardBackground border border-border rounded-lg shadow-sm">
          <div className="p-6">
            <h2 className="text-lg font-semibold text-textPrimary mb-6">Area of Interest Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Basic Information */}
              <div>
                <label className={fieldClass}>Name</label>
                <div className={valueClass}>{area.name}</div>
              </div>

              <div>
                <label className={fieldClass}>Status</label>
                <div className={valueClass}>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    area.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {area.is_active ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </div>

              <div>
                <label className={fieldClass}>Created At</label>
                <div className={valueClass}>{formatDate(area.created_at)}</div>
              </div>

              <div>
                <label className={fieldClass}>Last Updated</label>
                <div className={valueClass}>{formatDate(area.updated_at)}</div>
              </div>

              <div>
                <label className={fieldClass}>Subscriber Count</label>
                <div className={valueClass}>{area.subscriber_count || 0}</div>
              </div>

              <div>
                <label className={fieldClass}>Sort Order</label>
                <div className={valueClass}>{area.sort_order || 'Not set'}</div>
              </div>
            </div>

            {/* Description */}
            {area.description && (
              <div className="mt-6">
                <label className={fieldClass}>Description</label>
                <div className="w-full p-3 border border-border rounded-lg bg-gray-50 text-textPrimary min-h-[100px] whitespace-pre-wrap">
                  {area.description}
                </div>
              </div>
            )}

            {/* Additional Information */}
            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="text-sm font-semibold text-blue-800 mb-2">Usage Information</h3>
              <p className="text-sm text-blue-700">
                This area of interest is used to categorize subscribers and target specific groups in campaigns and templates.
                Subscribers can be associated with multiple areas of interest to receive relevant communications.
              </p>
            </div>

            {/* Statistics */}
            <div className="mt-6">
              <h3 className="text-lg font-semibold text-textPrimary mb-4">Statistics</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="text-2xl font-bold text-green-800">{area.subscriber_count || 0}</div>
                  <div className="text-sm text-green-600">Total Subscribers</div>
                </div>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="text-2xl font-bold text-blue-800">
                    {area.is_active ? 'Active' : 'Inactive'}
                  </div>
                  <div className="text-sm text-blue-600">Current Status</div>
                </div>
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                  <div className="text-2xl font-bold text-purple-800">{area.sort_order || 'N/A'}</div>
                  <div className="text-sm text-purple-600">Display Order</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewAreaOfInterestPage;
