import React, { useState, useEffect } from 'react';
import Header from '../../components/Header';
import { whatsappAutomationIntegration } from './WhatsAppAutomationIntegrationService';

interface AutomationPreferences {
  messageDelay: number;
  searchDelay: number;
  typeDelay: number;
  screenshotLogging: boolean;
  safeMode: boolean;
  retryAttempts: number;
  maxBatchSize: number;
}

const WhatsAppAutomationConfigPage: React.FC = () => {
  const [preferences, setPreferences] = useState<AutomationPreferences>({
    messageDelay: 3000,
    searchDelay: 2000,
    typeDelay: 50,
    screenshotLogging: true,
    safeMode: true,
    retryAttempts: 3,
    maxBatchSize: 50
  });

  const [isSaving, setIsSaving] = useState(false);
  const [systemStatus, setSystemStatus] = useState<{
    nutjsAvailable: boolean;
    systemCompatible: boolean;
    whatsappDesktopDetected: boolean;
    recommendations: string[];
  } | null>(null);

  useEffect(() => {
    loadConfiguration();
    checkSystemStatus();
  }, []);

  const loadConfiguration = async () => {
    try {
      // Load saved preferences from automation service
      const savedPrefs = await whatsappAutomationIntegration.getPreferences();
      if (savedPrefs) {
        setPreferences(savedPrefs);
      }
    } catch (error) {
      console.error('Failed to load automation preferences:', error);
    }
  };

  const checkSystemStatus = async () => {
    try {
      const status = await whatsappAutomationIntegration.checkDependencies();
      setSystemStatus(status);
    } catch (error) {
      console.error('Failed to check system status:', error);
    }
  };

  const handlePreferenceChange = (key: keyof AutomationPreferences, value: number | boolean) => {
    setPreferences(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const saveConfiguration = async () => {
    setIsSaving(true);
    try {
      await whatsappAutomationIntegration.updatePreferences(preferences);
      // Show success message
    } catch (error) {
      console.error('Failed to save automation preferences:', error);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header title="WhatsApp Automation Configuration" />
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">WhatsApp Automation Configuration</h1>
          <p className="text-gray-600 dark:text-gray-300 mt-2">
            Configure advanced settings for nut.js WhatsApp Desktop automation
          </p>
        </div>

        {/* System Status */}
        {systemStatus && (
          <div className="bg-white shadow rounded-lg mb-6">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">System Status</h2>
            </div>
            <div className="px-6 py-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className={`p-3 rounded-lg ${systemStatus.nutjsAvailable ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
                  <div className="flex items-center">
                    <span className={`text-sm font-medium ${systemStatus.nutjsAvailable ? 'text-green-800' : 'text-red-800'}`}>
                      {systemStatus.nutjsAvailable ? '✅' : '❌'} nut.js Available
                    </span>
                  </div>
                </div>
                <div className={`p-3 rounded-lg ${systemStatus.systemCompatible ? 'bg-green-50 border border-green-200' : 'bg-yellow-50 border border-yellow-200'}`}>
                  <div className="flex items-center">
                    <span className={`text-sm font-medium ${systemStatus.systemCompatible ? 'text-green-800' : 'text-yellow-800'}`}>
                      {systemStatus.systemCompatible ? '✅' : '⚠️'} System Compatible
                    </span>
                  </div>
                </div>
                <div className={`p-3 rounded-lg ${systemStatus.whatsappDesktopDetected ? 'bg-green-50 border border-green-200' : 'bg-yellow-50 border border-yellow-200'}`}>
                  <div className="flex items-center">
                    <span className={`text-sm font-medium ${systemStatus.whatsappDesktopDetected ? 'text-green-800' : 'text-yellow-800'}`}>
                      {systemStatus.whatsappDesktopDetected ? '✅' : '⚠️'} WhatsApp Desktop
                    </span>
                  </div>
                </div>
              </div>

              {systemStatus.recommendations.length > 0 && (
                <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                  <h3 className="text-sm font-medium text-blue-800 mb-2">Recommendations:</h3>
                  <ul className="text-sm text-blue-700 space-y-1">
                    {systemStatus.recommendations.map((rec, index) => (
                      <li key={index}>• {rec}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Automation Preferences */}
        <div className="bg-white shadow rounded-lg mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Automation Preferences</h2>
          </div>
          <div className="px-6 py-4">
            <div className="space-y-6">

              {/* Timing Settings */}
              <div>
                <h3 className="text-base font-medium text-gray-900 mb-4">Timing Settings</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">

                  {/* Message Delay */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Message Delay (ms)
                    </label>
                    <input
                      type="number"
                      value={preferences.messageDelay}
                      onChange={(e) => handlePreferenceChange('messageDelay', parseInt(e.target.value))}
                      min="1000"
                      max="10000"
                      step="500"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      aria-label="Message delay in milliseconds"
                    />
                    <p className="text-xs text-gray-500 mt-1">Delay between messages</p>
                  </div>

                  {/* Search Delay */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Search Delay (ms)
                    </label>
                    <input
                      type="number"
                      value={preferences.searchDelay}
                      onChange={(e) => handlePreferenceChange('searchDelay', parseInt(e.target.value))}
                      min="500"
                      max="5000"
                      step="250"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      aria-label="Search delay in milliseconds"
                    />
                    <p className="text-xs text-gray-500 mt-1">Delay for contact search</p>
                  </div>

                  {/* Type Delay */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Type Delay (ms)
                    </label>
                    <input
                      type="number"
                      value={preferences.typeDelay}
                      onChange={(e) => handlePreferenceChange('typeDelay', parseInt(e.target.value))}
                      min="10"
                      max="200"
                      step="10"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      aria-label="Type delay in milliseconds"
                    />
                    <p className="text-xs text-gray-500 mt-1">Delay between keystrokes</p>
                  </div>
                </div>
              </div>

              {/* Advanced Settings */}
              <div>
                <h3 className="text-base font-medium text-gray-900 mb-4">Advanced Settings</h3>
                <div className="space-y-4">

                  {/* Screenshot Logging */}
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-base font-medium text-gray-900">Screenshot Logging</h3>
                      <p className="text-sm text-gray-600">
                        Capture screenshots during automation for debugging
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={preferences.screenshotLogging}
                        onChange={(e) => handlePreferenceChange('screenshotLogging', e.target.checked)}
                        className="sr-only peer"
                        aria-labelledby="screenshotLoggingLabel"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                      <span id="screenshotLoggingLabel" className="sr-only">Enable screenshot logging</span>
                    </label>
                  </div>

                  {/* Safe Mode */}
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-base font-medium text-gray-900">Safe Mode</h3>
                      <p className="text-sm text-gray-600">
                        Use slower, more reliable automation with extra delays
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={preferences.safeMode}
                        onChange={(e) => handlePreferenceChange('safeMode', e.target.checked)}
                        className="sr-only peer"
                        aria-labelledby="safeModeLabel"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                      <span id="safeModeLabel" className="sr-only">Enable safe mode</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Usage Guidelines */}
        <div className="bg-white shadow rounded-lg mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Usage Guidelines</h2>
          </div>
          <div className="px-6 py-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              
              {/* Best Practices */}
              <div>
                <h3 className="text-base font-semibold text-gray-900 mb-3">Best Practices</h3>
                <ul className="text-sm text-gray-600 space-y-2">
                  <li className="flex items-start">
                    <span className="text-green-500 mr-2">✓</span>
                    <span>Test with small batches first (5-10 messages)</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-2">✓</span>
                    <span>Ensure WhatsApp Desktop is logged in and visible</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-2">✓</span>
                    <span>Use Safe Mode for reliable automated sending</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-2">✓</span>
                    <span>Keep screen unobstructed during automation</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-2">✓</span>
                    <span>Monitor progress and be ready to intervene</span>
                  </li>
                </ul>
              </div>

              {/* Limitations */}
              <div>
                <h3 className="text-base font-semibold text-gray-900 mb-3">Limitations</h3>
                <ul className="text-sm text-gray-600 space-y-2">
                  <li className="flex items-start">
                    <span className="text-orange-500 mr-2">⚠</span>
                    <span>Requires WhatsApp Desktop to be running</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-orange-500 mr-2">⚠</span>
                    <span>Screen resolution affects automation accuracy</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-orange-500 mr-2">⚠</span>
                    <span>May be affected by WhatsApp UI updates</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-orange-500 mr-2">⚠</span>
                    <span>Slower than API method but more personal</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-orange-500 mr-2">⚠</span>
                    <span>Subject to WhatsApp's usage policies</span>
                  </li>
                </ul>
              </div>
            </div>

            {/* Performance Tips */}
            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
              <h3 className="text-base font-semibold text-blue-900 mb-2">Performance Tips</h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Close unnecessary applications to free up system resources</li>
                <li>• Use a dedicated computer/session for large campaigns</li>
                <li>• Enable screenshot logging for troubleshooting</li>
                <li>• Start with Safe Mode enabled for consistent results</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Save Button */}
        <div className="flex justify-end space-x-4">
          <button
            onClick={() => loadConfiguration()}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
          >
            Reset
          </button>
          <button
            onClick={saveConfiguration}
            disabled={isSaving}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSaving ? 'Saving...' : 'Save Configuration'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default WhatsAppAutomationConfigPage;
