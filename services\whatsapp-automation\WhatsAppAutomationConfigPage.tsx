                      <input
                        type="checkbox"
                        checked={preferences.screenshotLogging}
                        onChange={(e) => handlePreferenceChange('screenshotLogging', e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  {/* Safe Mode */}
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-base font-medium text-gray-900">Safe Mode</h3>
                      <p className="text-sm text-gray-600">
                        Use slower, more reliable automation with extra delays
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={preferences.safeMode}
                        onChange={(e) => handlePreferenceChange('safeMode', e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Usage Guidelines */}
        <div className="bg-white shadow rounded-lg mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Usage Guidelines</h2>
          </div>
          <div className="px-6 py-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              
              {/* Best Practices */}
              <div>
                <h3 className="text-base font-semibold text-gray-900 mb-3">Best Practices</h3>
                <ul className="text-sm text-gray-600 space-y-2">
                  <li className="flex items-start">
                    <span className="text-green-500 mr-2">✓</span>
                    <span>Test with small batches first (5-10 messages)</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-2">✓</span>
                    <span>Ensure WhatsApp Desktop is logged in and visible</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-2">✓</span>
                    <span>Use Safe Mode for reliable automated sending</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-2">✓</span>
                    <span>Keep screen unobstructed during automation</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-2">✓</span>
                    <span>Monitor progress and be ready to intervene</span>
                  </li>
                </ul>
              </div>

              {/* Limitations */}
              <div>
                <h3 className="text-base font-semibold text-gray-900 mb-3">Limitations</h3>
                <ul className="text-sm text-gray-600 space-y-2">
                  <li className="flex items-start">
                    <span className="text-orange-500 mr-2">⚠</span>
                    <span>Requires WhatsApp Desktop to be running</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-orange-500 mr-2">⚠</span>
                    <span>Screen resolution affects automation accuracy</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-orange-500 mr-2">⚠</span>
                    <span>May be affected by WhatsApp UI updates</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-orange-500 mr-2">⚠</span>
                    <span>Slower than API method but more personal</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-orange-500 mr-2">⚠</span>
                    <span>Subject to WhatsApp's usage policies</span>
                  </li>
                </ul>
              </div>
            </div>

            {/* Performance Tips */}
            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
              <h3 className="text-base font-semibold text-blue-900 mb-2">Performance Tips</h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Close unnecessary applications to free up system resources</li>
                <li>• Use a dedicated computer/session for large campaigns</li>
                <li>• Enable screenshot logging for troubleshooting</li>
                <li>• Start with Safe Mode enabled for consistent results</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Save Button */}
        <div className="flex justify-end space-x-4">
          <button
            onClick={() => loadConfiguration()}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
          >
            Reset
          </button>
          <button
            onClick={saveConfiguration}
            disabled={isSaving}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSaving ? 'Saving...' : 'Save Configuration'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default WhatsAppAutomationConfigPage;