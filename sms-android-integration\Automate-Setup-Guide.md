# Automate App Setup Guide for CRM SMS Integration

## Overview
This guide shows you how to set up the Automate app on Android to automatically send SMS messages from your CRM system. Automate will poll your CRM server for pending messages and send them via the Android device's SMS capability.

## Step 1: Install Automate App

### Download and Install
1. **Open Google Play Store** on your Android device
2. **Search for "Automate"** by LlamaLab
3. **Install the app** (it's free with premium features available)
4. **Open Automate** and grant necessary permissions

### Required Permissions
When prompted, grant these permissions:
- ✅ **SMS** - To send text messages
- ✅ **Phone** - To access phone features
- ✅ **Storage** - To save flow configurations
- ✅ **Network** - To communicate with your CRM server
- ✅ **Device Admin** (optional) - For advanced features

## Step 2: Create SMS Sending Flow

### Import Pre-built Flow
I'll provide you with a ready-made flow configuration. Follow these steps:

1. **Open Automate app**
2. **Tap the "+" button** to create new flow
3. **Choose "Import"** if you have the flow file
4. **Or follow the manual creation steps below**

### Manual Flow Creation

#### A. Create New Flow
1. **Tap "+" button** in Automate
2. **Select "Create new flow"**
3. **Name it**: "CRM SMS Gateway"
4. **Tap "Create"**

#### B. Add Flow Blocks

**Block 1: Flow Beginning**
1. **Drag "Flow beginning" block** to canvas
2. **No configuration needed**

**Block 2: HTTP Request (Check for Messages)**
1. **Drag "HTTP request" block** to canvas
2. **Connect it** to Flow beginning
3. **Configure**:
   - **URL**: `http://[YOUR_PC_IP]:3001/api/sms/pending`
   - **Method**: GET
   - **Headers**: Content-Type: application/json
   - **Variable name**: `pendingMessages`

**Block 3: Expression (Parse JSON)**
1. **Drag "Expression" block** to canvas
2. **Connect it** to HTTP request
3. **Configure**:
   - **Expression**: `json(pendingMessages)`
   - **Variable name**: `messageList`

**Block 4: For Each Loop**
1. **Drag "For each" block** to canvas
2. **Connect it** to Expression
3. **Configure**:
   - **List**: `messageList`
   - **Variable name**: `currentMessage`

**Block 5: Send SMS**
1. **Drag "Send SMS" block** to canvas
2. **Connect it** inside the For each loop
3. **Configure**:
   - **Phone number**: `currentMessage.recipient`
   - **Message**: `currentMessage.message`
   - **SIM**: Default SIM

**Block 6: HTTP Request (Report Status)**
1. **Drag another "HTTP request" block** to canvas
2. **Connect it** after Send SMS
3. **Configure**:
   - **URL**: `http://[YOUR_PC_IP]:3001/api/sms/status`
   - **Method**: POST
   - **Headers**: Content-Type: application/json
   - **Body**: 
     ```json
     {
       "messageId": "{currentMessage.id}",
       "status": "sent",
       "timestamp": "{datetime()}"
     }
     ```

**Block 7: Delay**
1. **Drag "Delay" block** to canvas
2. **Connect it** after the For each loop
3. **Configure**:
   - **Duration**: 5 seconds

**Block 8: Loop Back**
1. **Connect the Delay block** back to the HTTP request block
2. **This creates an infinite loop** that checks every 5 seconds

#### C. Handle Errors
1. **Add "Try/Catch" blocks** around HTTP requests
2. **Add "Toast" blocks** to show error messages
3. **Add "Log" blocks** for debugging

## Step 3: Configure Your Network Settings

### Find Your PC's IP Address
**On Windows PC**:
1. **Open Command Prompt** (cmd)
2. **Type**: `ipconfig`
3. **Look for "IPv4 Address"** under your network adapter
4. **Example**: `*************`

### Update Flow Configuration
1. **Edit the HTTP request blocks** in your flow
2. **Replace `[YOUR_PC_IP]`** with your actual IP address
3. **Example URLs**:
   - Check messages: `http://*************:3001/api/sms/pending`
   - Report status: `http://*************:3001/api/sms/status`

## Step 4: Test the Flow

### Initial Testing
1. **Tap "Play" button** in Automate to start the flow
2. **Check the log** for any errors
3. **Create a test SMS campaign** in your CRM
4. **Verify the message appears** in pending queue
5. **Confirm SMS is sent** from Android device

### Debugging
If issues occur:
1. **Check network connectivity** - can Android reach your PC?
2. **Verify CRM server** is running on port 3001
3. **Check permissions** - ensure SMS permission is granted
4. **Review logs** in Automate app
5. **Test URLs manually** in Android browser

## Step 5: Advanced Configuration

### Background Operation
1. **Enable "Run in background"** in flow settings
2. **Disable battery optimization** for Automate app:
   - Settings → Battery → Battery Optimization
   - Find Automate → Don't optimize
3. **Keep flow running** even when screen is off

### Error Handling
Add these blocks for better reliability:

**Network Error Handling**:
```
Try → HTTP Request → Catch → Log Error → Delay → Continue
```

**SMS Failure Handling**:
```
Try → Send SMS → Catch → Report Failed Status → Continue
```

### Notification Setup
1. **Add "Notification" blocks** to show SMS sending status
2. **Configure different notifications** for:
   - Messages received
   - SMS sent successfully
   - Errors occurred

## Step 6: Flow Optimization

### Efficient Polling
1. **Adjust delay timing** based on your needs:
   - High volume: 5-10 seconds
   - Low volume: 30-60 seconds
2. **Add condition** to skip if no messages
3. **Implement exponential backoff** for errors

### Battery Optimization
1. **Use "Device idle" detection**
2. **Reduce polling frequency** when device is idle
3. **Stop flow** during specific hours if needed

### Multiple SIM Support
1. **Add "SIM selection" logic** if device has multiple SIMs
2. **Configure different SIMs** for different campaigns
3. **Load balance** across available SIMs

## Step 7: Monitoring and Maintenance

### Flow Monitoring
1. **Check flow status** regularly in Automate
2. **Review logs** for errors or issues
3. **Monitor SMS delivery** in CRM system
4. **Verify network connectivity** periodically

### Troubleshooting Common Issues

**Flow Stops Running**:
- Check battery optimization settings
- Ensure Automate has all required permissions
- Restart the flow manually

**SMS Not Sending**:
- Verify SIM card is active
- Check SMS permissions
- Ensure phone has SMS capability
- Check message format and recipient number

**Network Errors**:
- Verify PC IP address hasn't changed
- Check WiFi connectivity
- Ensure CRM server is running
- Test URLs in browser

**High Battery Usage**:
- Increase delay between polls
- Optimize flow logic
- Use device idle detection

## Step 8: Production Deployment

### Final Setup
1. **Test thoroughly** with various message types
2. **Configure automatic startup** on device boot
3. **Set up monitoring** and alerting
4. **Document the configuration** for future reference

### Backup Configuration
1. **Export the flow** from Automate
2. **Save configuration** to cloud storage
3. **Document network settings** and IP addresses
4. **Keep backup** of working configuration

## Alternative: Tasker Setup

If you prefer Tasker over Automate:

### Tasker Configuration
1. **Install Tasker** from Play Store
2. **Create Profile**: Time → Every 5 minutes
3. **Add Task**: HTTP Get → Parse JSON → Send SMS
4. **Configure same URLs** and logic as Automate

### Tasker Advantages
- More mature app
- Better community support
- More plugins available
- Advanced scripting capabilities

## Security Considerations

### Network Security
- **Use local network only** (don't expose to internet)
- **Consider VPN** if accessing remotely
- **Monitor network traffic** for unusual activity

### Device Security
- **Keep Android updated**
- **Use device lock screen**
- **Don't install unknown apps**
- **Monitor SMS usage** for unexpected charges

## Support and Troubleshooting

### Getting Help
1. **Check Automate documentation** and community forums
2. **Test individual components** (HTTP requests, SMS sending)
3. **Use Android debugging tools** if needed
4. **Contact support** if persistent issues occur

### Performance Monitoring
- **Track SMS delivery rates**
- **Monitor battery usage**
- **Check network data usage**
- **Review error logs regularly**

This setup will give you a reliable SMS gateway using your Android device that integrates seamlessly with your CRM system!
