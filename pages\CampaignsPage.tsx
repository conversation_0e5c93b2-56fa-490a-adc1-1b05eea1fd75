import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import Header from '../components/Header';
import Table, { Column } from '../components/Table';
import ColumnSelector from '../components/ColumnSelector';
import { Campaign as BaseCampaign, CampaignStatus, CampaignType, AuditActionType, UserRole, UserTablePreferences, AreaOfInterest, CampaignTemplate, Subscriber, SubscriberProfileStatus } from '../types';

interface Campaign extends BaseCampaign {
  target_segments?: string[];
  template_display_name?: string;
  template_area_name?: string;
}

import { PlusIcon, EditIcon, DeleteIcon, PaperAirplaneIcon } from '../components/icons';

// Helper function to get CSS class for campaign status badge
const getStatusColorClass = (status: CampaignStatus) => {
  switch (status) {
    case CampaignStatus.DRAFT:
      return 'bg-gray-200 text-gray-800 dark:bg-gray-600 dark:text-gray-100';
    case CampaignStatus.SCHEDULED:
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100';
    case CampaignStatus.SENDING:
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100';
    case CampaignStatus.SENT:
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100';
    case CampaignStatus.FAILED:
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100';
    case CampaignStatus.ARCHIVED:
      return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-100';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-100';
  }
};

import { formatEnumValueForDisplay } from '../utils/displayUtils';
import { addAuditLog } from '../utils/auditUtilsDB';
import { useAuth } from '../contexts/AuthContextDB';
import { canUserViewItem, canUserEditDeleteItem, canUserExecuteCampaign, AccessibleItem } from '../utils/accessControlUtils';
import { parseIndianDateStringToDate, formatDateForDisplay, isValidIndianDateInput } from '../utils/dateUtils';
import ConfirmationModal from '../components/ConfirmationModal';
import { campaignService } from '../services/campaignService-API';
import { templateService } from '../services/TemplateService-API';
import { areaOfInterestService } from '../services/AreaOfInterestService-API';
import { userService } from '../services/userService-API';
import { subscriberService } from '../services/SubscriberService-API';
import { browserDatabaseService } from '../services/BrowserDatabaseService';
// Removed CampaignSendingLogs import - now using dedicated page

type CampaignFilterStatus = 'CURRENT_ACTIVITY' | 'SCHEDULED_SENDING' | 'COMPLETED' | 'DRAFT';

const CAMPAIGNS_TABLE_KEY = 'campaignsListTable';

/**
 * Component to display recipient breakdown by channel - MOVED OUTSIDE TO FIX TIMING ISSUE
 */
const RecipientBreakdown: React.FC<{
  campaign: Campaign;
  campaignCounts: Record<string, any>
}> = ({ campaign, campaignCounts }) => {
  const counts = campaignCounts[campaign.id] || {
    total: 0,
    email: { total: 0, sent: 0 },
    whatsapp: { total: 0, sent: 0 },
    sms: { total: 0, sent: 0 }
  };

  return (
    <div className="text-right text-sm">
      <div className="font-medium text-gray-900 mb-1">
        Total: {counts.total}
      </div>
      <div className="space-y-1 text-xs text-gray-600">
        {/* Email Channel */}
        {(counts.email.total > 0 || Boolean(campaign.email_enabled)) && (
          <div className="flex justify-between items-center">
            <span className="flex items-center">
              <span className="w-2 h-2 bg-blue-500 rounded-full mr-1"></span>
              📧:
            </span>
            <span className="font-medium">
              {!Boolean(campaign.email_enabled) ? '0/0' : `${counts.email.sent}/${counts.email.total}`}
            </span>
          </div>
        )}

        {/* WhatsApp Channel */}
        {(counts.whatsapp.total > 0 || Boolean(campaign.whatsapp_enabled)) && (
          <div className="flex justify-between items-center">
            <span className="flex items-center">
              <span className="w-2 h-2 bg-green-500 rounded-full mr-1"></span>
              💬:
            </span>
            <span className="font-medium">
              {!Boolean(campaign.whatsapp_enabled) ? '0/0' : `${counts.whatsapp.sent}/${counts.whatsapp.total}`}
            </span>
          </div>
        )}

        {/* SMS Channel */}
        {(counts.sms.total > 0 || Boolean(campaign.sms_enabled)) && (
          <div className="flex justify-between items-center">
            <span className="flex items-center">
              <span className="w-2 h-2 bg-purple-500 rounded-full mr-1"></span>
              📱:
            </span>
            <span className="font-medium">
              {!Boolean(campaign.sms_enabled) ? '0/0' : `${counts.sms.sent}/${counts.sms.total}`}
            </span>
          </div>
        )}

        {counts.total === 0 && (
          <div className="text-orange-600 font-medium">No recipients</div>
        )}
      </div>
    </div>
  );
};

/**
 * Professional Campaigns Management Page
 * Database-backed campaign management with comprehensive CRUD operations
 *
 * Migration Status: ✅ MIGRATED to Database
 */
const CampaignsPage: React.FC = () => {
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [subscribers, setSubscribers] = useState<Subscriber[]>([]);
  const [sendingLogs, setSendingLogs] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [feedbackMessage, setFeedbackMessage] = useState<string | null>(null);
  const [activeFilter, setActiveFilter] = useState<CampaignFilterStatus>('CURRENT_ACTIVITY');
  const [startDateFilterInput, setStartDateFilterInput] = useState<string>('');
  const [endDateFilterInput, setEndDateFilterInput] = useState<string>('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [campaignToDelete, setCampaignToDelete] = useState<Campaign | null>(null);

  // Campaign Sending Logs - now handled via page navigation
  // Removed modal state variable

  // Overdue campaign management
  const [showOverdueModal, setShowOverdueModal] = useState(false);
  const [overdueCampaign, setOverdueCampaign] = useState<Campaign | null>(null);
  const [overdueAction, setOverdueAction] = useState<'send' | 'reschedule' | null>(null);

  /**
   * Force re-render when sendingLogs change or campaigns change
   */
  const [forceRender, setForceRender] = useState(0);

  /**
   * Calculate recipient counts for all campaigns - MOVED BEFORE TABLE COLUMNS
   * Uses campaign.total_recipients from database and calculates channel-specific counts
   */
  const campaignCounts = useMemo(() => {
    console.log(`🔍 [campaignCounts] Calculating counts for ${campaigns.length} campaigns`);
    console.log(`📊 [campaignCounts] SendingLogs available: ${sendingLogs ? sendingLogs.length : 'null'}`);

    const counts: Record<string, any> = {};

    campaigns.forEach(campaign => {
      // DEBUG: Log detailed campaign information
      console.log(`🔍 [campaignCounts] Processing campaign: ${campaign.name} (${campaign.id})`);
      console.log(`   - total_recipients: ${campaign.total_recipients} (type: ${typeof campaign.total_recipients})`);
      console.log(`   - sender_name: "${campaign.sender_name || 'NULL'}"`);
      console.log(`   - sender_email: "${campaign.sender_email || 'NULL'}"`);
      console.log(`   - subscriber_selection_mode: ${campaign.subscriber_selection_mode}`);
      console.log(`   - selected_subscriber_ids: ${campaign.selected_subscriber_ids ? JSON.stringify(campaign.selected_subscriber_ids) : 'NULL'}`);
      console.log(`   - email_enabled: ${campaign.email_enabled}`);
      console.log(`   - email_content length: ${campaign.email_content ? campaign.email_content.length : 0}`);
      console.log(`   - whatsapp_content length: ${campaign.whatsapp_content ? campaign.whatsapp_content.length : 0}`);

      // Get actual sent counts from campaign sending logs
      let emailSent = 0, whatsappSent = 0, smsSent = 0;

      if (sendingLogs && sendingLogs.length > 0) {
        const campaignLogs = sendingLogs.filter(log => log.campaign_id === campaign.id);
        emailSent = campaignLogs.filter(log => log.channel === 'email' && log.status === 'sent').length;
        whatsappSent = campaignLogs.filter(log => log.channel === 'whatsapp' && log.status === 'sent').length;
        smsSent = campaignLogs.filter(log => log.channel === 'sms' && log.status === 'sent').length;
        console.log(`   - Campaign logs found: ${campaignLogs.length}, Email sent: ${emailSent}, WhatsApp sent: ${whatsappSent}`);
      }

      // Use the total_recipients from campaign data (calculated during campaign creation)
      const totalRecipients = campaign.total_recipients || 0;

      // DEBUG: Check if total_recipients is being lost or converted incorrectly
      if (campaign.name.includes('GSTR')) {
        console.log(`🔍 [DEBUG] GSTR Campaign ${campaign.name}:`);
        console.log(`   - Raw total_recipients: ${campaign.total_recipients} (${typeof campaign.total_recipients})`);
        console.log(`   - Calculated totalRecipients: ${totalRecipients} (${typeof totalRecipients})`);
        console.log(`   - Raw email_enabled: ${campaign.email_enabled} (${typeof campaign.email_enabled})`);
        console.log(`   - Boolean email_enabled: ${Boolean(campaign.email_enabled)}`);
        console.log(`   - Raw whatsapp_enabled: ${campaign.whatsapp_enabled} (${typeof campaign.whatsapp_enabled})`);
        console.log(`   - Boolean whatsapp_enabled: ${Boolean(campaign.whatsapp_enabled)}`);
      }

      // Calculate channel-specific totals based on enabled channels and content availability
      const hasEmailContent = campaign.email_content && campaign.email_content.trim() !== '';
      const hasWhatsAppContent = campaign.whatsapp_content && campaign.whatsapp_content.trim() !== '';
      const hasSmsContent = campaign.sms_content && campaign.sms_content.trim() !== '';

      console.log(`   - Content availability: Email: ${hasEmailContent}, WhatsApp: ${hasWhatsAppContent}, SMS: ${hasSmsContent}`);

      // Convert database values (1/0) to boolean and check for enabled channels
      const isEmailEnabled = Boolean(campaign.email_enabled);
      const isWhatsAppEnabled = Boolean(campaign.whatsapp_enabled);
      const isSmsEnabled = Boolean(campaign.sms_enabled);

      console.log(`   - Channel enabled flags: Email: ${isEmailEnabled}, WhatsApp: ${isWhatsAppEnabled}, SMS: ${isSmsEnabled}`);

      // Use the new channel-specific recipient counts from the campaign data if available
      const emailTotal = campaign.email_recipients_count !== undefined ? campaign.email_recipients_count :
                         (isEmailEnabled && hasEmailContent) ? totalRecipients : 0;
      const whatsappTotal = campaign.whatsapp_recipients_count !== undefined ? campaign.whatsapp_recipients_count :
                           (isWhatsAppEnabled && hasWhatsAppContent) ? totalRecipients : 0;
      const smsTotal = campaign.sms_recipients_count !== undefined ? campaign.sms_recipients_count :
                      (isSmsEnabled && hasSmsContent) ? totalRecipients : 0;

      console.log(`✅ [campaignCounts] ${campaign.name}: Total: ${totalRecipients}, Email: ${emailSent}/${emailTotal}, WhatsApp: ${whatsappSent}/${whatsappTotal}, SMS: ${smsSent}/${smsTotal}`);

      counts[campaign.id] = {
        total: totalRecipients,
        email: { total: emailTotal, sent: emailSent },
        whatsapp: { total: whatsappTotal, sent: whatsappSent },
        sms: { total: smsTotal, sent: smsSent }
      };
    });

    return counts;
  }, [campaigns, sendingLogs, forceRender]);

  // Table columns configuration - MOVED AFTER campaignCounts TO ACCESS IT
  const allTableColumns = useMemo(() => [
    {
      id: 'status',
      header: 'Status',
      accessor: 'status' as keyof Campaign,
      render: (item: Campaign) => {
        const colorClass = getStatusColorClass(item.status);
        return (
          <span className={`px-2 py-1 text-xs font-semibold leading-tight rounded-full whitespace-nowrap ${colorClass}`}>
            {formatEnumValueForDisplay(item.status)}
          </span>
        );
      },
      sortable: true,
      sortValue: (item: Campaign) => item.status,
      defaultVisible: true,
    },
    {
      id: 'template_display_name',
      header: 'Template',
      accessor: (item: Campaign) => item.template_display_name || (item.template_id ? `Template ${item.template_id}` : 'N/A'),
      render: (item: Campaign) => (
        <div className="max-w-48 truncate break-all" title={item.template_display_name || (item.template_id ? `Template ${item.template_id}` : 'N/A')}>
          {item.template_display_name || (item.template_id ? `Template ${item.template_id}` : 'N/A')}
        </div>
      ),
      sortable: true,
      sortValue: (item: Campaign) => item.template_display_name || item.template_id || '',
      defaultVisible: true,
    },
    {
      id: 'template_area',
      header: 'Template Area',
      accessor: (item: Campaign) => item.template_area_name || 'None',
      render: (item: Campaign) => (
        <div className="max-w-32 truncate" title={item.template_area_name || 'None'}>
          {item.template_area_name || 'None'}
        </div>
      ),
      sortable: true,
      sortValue: (item: Campaign) => item.template_area_name || '',
      defaultVisible: true,
    },
    {
      id: 'target_areas',
      header: 'Target Areas',
      accessor: (item: Campaign) => item.target_segments?.join(', ') || 'None',
      render: (item: Campaign) => (
        <div className="max-w-40 truncate" title={item.target_segments?.join(', ') || 'None'}>
          {item.target_segments?.join(', ') || 'None'}
        </div>
      ),
      sortable: true,
      sortValue: (item: Campaign) => item.target_segments ? item.target_segments.join('') : '',
      defaultVisible: true,
    },
    {
      id: 'scheduled_sent_date',
      header: 'Scheduled/ Sent',
      accessor: (item: Campaign) => {
        if (item.status === CampaignStatus.SENT) {
          if (item.sent_date) {
            return `Sent: ${formatDateForDisplay(item.sent_date, { type: 'datetime' })}`;
          } else {
            // Fallback for sent campaigns without sent_date
            return `Sent: ${formatDateForDisplay(item.updated_at || item.created_at, { type: 'datetime' })}`;
          }
        }
        // Check for scheduled campaigns with string comparison
        if (item.status === 'scheduled' && item.scheduled_date && item.campaign_type !== CampaignType.BIRTHDAY_WISH) {
          return `Scheduled: ${formatDateForDisplay(item.scheduled_date, { type: 'datetime' })}`;
        }
        if (item.status === CampaignStatus.SENDING) return 'Sending Now';
        if (item.status === CampaignStatus.DRAFT) return 'Draft';
        if (item.status === CampaignStatus.FAILED) return 'Failed';
        return 'N/A';
      },
      render: (item: Campaign) => {
        if (item.status === CampaignStatus.SENT) {
          if (item.sent_date) {
            return `Sent: ${formatDateForDisplay(item.sent_date, { type: 'datetime' })}`;
          } else {
            // Fallback for sent campaigns without sent_date
            return `Sent: ${formatDateForDisplay(item.updated_at || item.created_at, { type: 'datetime' })}`;
          }
        }

        // Check for scheduled campaigns
        if (item.status === 'scheduled' &&
            item.scheduled_date &&
            item.campaign_type !== CampaignType.BIRTHDAY_WISH) {
          return (
            <div className="text-sm">
              <div className="font-medium text-blue-600">
                Scheduled: {formatDateForDisplay(item.scheduled_date, { type: 'datetime' })}
              </div>
            </div>
          );
        }

        if (item.status === CampaignStatus.SENDING) return 'Sending Now';
        if (item.status === CampaignStatus.DRAFT) return 'Draft';
        if (item.status === CampaignStatus.FAILED) return 'Failed';
        return 'N/A';
      },
      sortable: true,
      sortValue: (item: Campaign) => (item.sent_date || item.scheduled_date) ? new Date(item.sent_date || item.scheduled_date!).getTime() : 0,
      defaultVisible: true,
    },
    {
      id: 'recipients_progress',
      header: 'Recipients by Channel',
      accessor: (item: Campaign) => {
        return `Total: ${item.total_recipients || 0}`;
      },
      render: (item: Campaign) => {
        return <RecipientBreakdown campaign={item} campaignCounts={campaignCounts} />;
      },
      className: 'text-right min-w-32',
      sortable: true,
      sortValue: (item: Campaign) => {
        return item.total_recipients || 0;
      },
      defaultVisible: true,
    },
    {
      id: 'sender_info',
      header: 'Sender Name',
      accessor: (item: Campaign) => item.sender_name || 'From Email Config',
      render: (item: Campaign) => (
        <div className="max-w-40 truncate" title={item.sender_name ? `Campaign: ${item.sender_name}` : 'Using default from email configuration'}>
          {item.sender_name ? (
            <span className="text-green-700 dark:text-green-300 font-medium">
              {item.sender_name}
            </span>
          ) : (
            <span className="text-gray-500 dark:text-gray-400 italic">
              Email Config
            </span>
          )}
        </div>
      ),
      sortable: true,
      sortValue: (item: Campaign) => item.sender_name || 'zzz', // Sort campaigns with sender_name first
      defaultVisible: true,
    },
    {
      id: 'remarks',
      header: 'Remarks',
      accessor: (item: Campaign) => item.remarks || 'None',
      render: (item: Campaign) => (
        <div className="max-w-48 truncate" title={item.remarks || 'No remarks'}>
          {item.remarks ? (
            <span className="text-gray-700 dark:text-gray-300">
              {item.remarks}
            </span>
          ) : (
            <span className="text-gray-400 dark:text-gray-500 italic">
              None
            </span>
          )}
        </div>
      ),
      sortable: true,
      sortValue: (item: Campaign) => item.remarks || '',
      defaultVisible: false,
    },
    {
      id: 'created_by',
      header: 'Created By',
      accessor: 'created_by' as keyof Campaign,
      sortable: true,
      sortValue: (item: Campaign) => item.created_by.toLowerCase(),
      defaultVisible: false,
    },
    {
      id: 'updated_at',
      header: 'Last Updated',
      accessor: 'updated_at' as keyof Campaign,
      render: (item: Campaign) => formatDateForDisplay(item.updated_at, { type: 'datetime' }),
      sortable: true,
      sortValue: (item: Campaign) => new Date(item.updated_at).getTime(),
      defaultVisible: true,
    },
    {
      id: 'actions',
      header: 'Actions',
      accessor: () => '', // Simple accessor for Actions column
      render: (campaign: Campaign) => (
        <div className="flex space-x-1">
          <button
            onClick={() => handleEditCampaign(campaign.id)}
            disabled={!canUserEditDeleteItem(campaign as AccessibleItem, currentUser)}
            className={`text-primary hover:text-opacity-80 p-1 ${!canUserEditDeleteItem(campaign as AccessibleItem, currentUser) ? 'opacity-50 cursor-not-allowed' : ''}`}
            title={canUserEditDeleteItem(campaign as AccessibleItem, currentUser) ? `Edit ${campaign.name}` : "Permission Denied"}
            aria-label={`Edit ${campaign.name}`}
          >
            <EditIcon className="h-5 w-5" />
          </button>
          <button
            onClick={() => handleDeleteCampaignConfirmation(campaign)}
            disabled={!canUserEditDeleteItem(campaign as AccessibleItem, currentUser)}
            className={`text-red-500 hover:text-red-700 p-1 ${!canUserEditDeleteItem(campaign as AccessibleItem, currentUser) ? 'opacity-50 cursor-not-allowed' : ''}`}
            title={canUserEditDeleteItem(campaign as AccessibleItem, currentUser) ? `Delete ${campaign.name}` : "Permission Denied"}
            aria-label={`Delete ${campaign.name}`}
          >
            <DeleteIcon className="h-5 w-5" />
          </button>
          {campaign.status !== CampaignStatus.DRAFT && campaign.status !== CampaignStatus.SENT && campaign.campaign_type !== CampaignType.BIRTHDAY_WISH && (
            <button
              onClick={() => handleSendCampaign(campaign.id)}
              disabled={!canUserExecuteCampaign(campaign, currentUser)}
              className={`text-green-500 hover:text-green-700 p-1 ${!canUserExecuteCampaign(campaign, currentUser) ? 'opacity-50 cursor-not-allowed' : ''}`}
              title={canUserExecuteCampaign(campaign, currentUser) ? `Send/Resume ${campaign.name}` : "Permission Denied"}
              aria-label={`Send/Resume ${campaign.name}`}
            >
              <PaperAirplaneIcon className="h-5 w-5" />
            </button>
          )}
        </div>
      ),
      defaultVisible: true,
      isNonRemovable: true,
    },
  ], [currentUser, campaignCounts]);

  const defaultVisibleColumnIds = useMemo(() => allTableColumns.filter(c => c.defaultVisible).map(c => c.id), [allTableColumns]);
  const [visibleColumnIds, setVisibleColumnIds] = useState<string[]>(defaultVisibleColumnIds);

  /**
   * Fetch campaign recipients (for area-based targeting)
   */
  const fetchCampaignRecipients = async (campaignId: string) => {
    try {
      const response = await fetch(`/api/campaigns/${campaignId}/recipients`);
      if (!response.ok) throw new Error('Failed to fetch recipients');
      return await response.json();
    } catch (error) {
      console.error('Error fetching campaign recipients:', error);
      return [];
    }
  };

  /**
   * Professional data fetching with comprehensive error handling
   */
  const fetchCampaigns = useCallback(async () => {
    try {
      setIsLoading(true);
      setFeedbackMessage(null);
      console.log('🔄 [CampaignsPage] Fetching campaigns and templates from database...');
      console.log('🔍 [CampaignsPage] Current user:', currentUser);
      const [allCampaigns, allTemplates, allAreas, allSubscribers, allSendingLogs] = await Promise.all([
        campaignService.getAllCampaigns(),
        templateService.getAllTemplates(),
        areaOfInterestService.getAllAreasOfInterest(),
        subscriberService.getAllSubscribers(),
        fetch('/api/campaign-sending-logs').then(res => {
          console.log(`📡 [CampaignsPage] Sending logs API response status: ${res.status}`);
          if (!res.ok) {
            throw new Error(`HTTP ${res.status}: ${res.statusText}`);
          }
          return res.json();
        })
      ]);
      console.log('🔍 [CampaignsPage] Raw campaigns from API:', allCampaigns.length);
      console.log('🔍 [CampaignsPage] Templates from API:', allTemplates.length);
      console.log('🔍 [CampaignsPage] Areas of Interest from API:', allAreas.length);
      // Create template display name map
      const templateDisplayNames = allTemplates.reduce((map: Record<string, string>, template) => {
        map[template.id] = template.display_name;
        return map;
      }, {});
      // Create area of interest name map
      const areaNames = allAreas.reduce((map: Record<string, string>, area: AreaOfInterest) => {
        map[area.id] = area.name;
        return map;
      }, {});
      const nonBirthdayCampaigns = allCampaigns.filter((c: Campaign) => c.campaign_type !== CampaignType.BIRTHDAY_WISH);

      const accessibleCampaigns = nonBirthdayCampaigns
        .filter((c: Campaign) => canUserViewItem(c as AccessibleItem, currentUser))
        .map(campaign => {
          const template = allTemplates.find((t: CampaignTemplate) => t.id === campaign.template_id);
          return {
            ...campaign,
            template_display_name: campaign.template_id ? templateDisplayNames[campaign.template_id] : undefined,
            template_area_name: template?.interest_area_id ? areaNames[template.interest_area_id] : undefined
          };
        });
      setCampaigns(accessibleCampaigns);
      setSubscribers(allSubscribers);
      setSendingLogs(allSendingLogs);
      // Log successful data access (only if user is authenticated)
      if (currentUser) {
        await addAuditLog(
          AuditActionType.VIEW,
          'Campaigns',
          'Campaigns list accessed',
          {
            userId: currentUser.user_id,
            metadata: { count: accessibleCampaigns.length }
          }
        );
      }
    } catch (error: any) {
      console.error('❌ [CampaignsPage] Error fetching campaigns:', error);
      setFeedbackMessage(`Error fetching campaigns: ${error.message}`);
      setCampaigns([]);
      // Log error for audit trail (only if user is authenticated)
      if (currentUser) {
        await addAuditLog(
          AuditActionType.ERROR,
          'Campaigns',
          'Failed to fetch campaigns list',
          {
            userId: currentUser.user_id,
            metadata: { error: error.message }
          }
        );
      }
    } finally {
      setIsLoading(false);
      console.log('🔄 [CampaignsPage] Loading state set to false');
    }
  }, [currentUser]);

  /**
   * Load user table preferences from database
   */
  useEffect(() => {
    const loadTablePreferences = async () => {
      if (!currentUser?.user_id) return;
      try {
        const sql = `
          SELECT table_preferences 
          FROM users 
          WHERE user_id = ?
        `;
        const result = await browserDatabaseService.query(sql, [currentUser.user_id]);
        if (result.length > 0 && result[0].table_preferences) {
          const preferences = JSON.parse(result[0].table_preferences);
          if (preferences[CAMPAIGNS_TABLE_KEY]) {
            setVisibleColumnIds(preferences[CAMPAIGNS_TABLE_KEY]);
            console.log('✅ Loaded saved column preferences:', preferences[CAMPAIGNS_TABLE_KEY]);
          }
        }
      } catch (error) {
        console.error('❌ Failed to load column preferences:', error);
      }
    };
    loadTablePreferences();
  }, [currentUser]);

  /**
   * Initialize component with database data
   */
  useEffect(() => {
    console.log('🔄 [CampaignsPage] useEffect triggered, currentUser:', currentUser);
    if (currentUser) {
      fetchCampaigns();
    } else {
      console.log('⚠️ [CampaignsPage] No current user, skipping fetchCampaigns');
      setIsLoading(false);
    }
  }, [fetchCampaigns, currentUser]);

  /**
   * Check for refresh parameter in URL and force refresh if present
   */
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const refreshParam = urlParams.get('refresh');
    const filterParam = urlParams.get('filter');

    if (refreshParam && currentUser) {
      fetchCampaigns();
    }

    if (filterParam) {
      setActiveFilter(filterParam as CampaignFilterStatus);
    }

    // Clean up the URL parameters
    if (refreshParam || filterParam) {
      window.history.replaceState({}, '', window.location.pathname);
    }
  }, [currentUser, fetchCampaigns]);

  /**
   * Check for refresh parameter in URL and force refresh if present
   */
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const refreshParam = urlParams.get('refresh');
    if (refreshParam && currentUser) {
      console.log('🔄 [CampaignsPage] Refresh parameter detected, forcing data refresh');
      fetchCampaigns();
      // Clean up the URL parameter
      window.history.replaceState({}, '', window.location.pathname);
    }
  }, [currentUser, fetchCampaigns]);

  /**
   * Save column visibility preferences to database
   */
  const handleColumnVisibilityChange = async (newVisibleIds: string[]) => {
    console.log('Saving new columns:', newVisibleIds);
    setVisibleColumnIds(newVisibleIds);
    if (currentUser?.user_id) {
      try {
        const sql = `
          UPDATE users 
          SET table_preferences = json_set(
            COALESCE(table_preferences, '{}'),
            '$."${CAMPAIGNS_TABLE_KEY}"',
            json(?)
          )
          WHERE user_id = ?
        `;
        await browserDatabaseService.query(sql, [JSON.stringify(newVisibleIds), currentUser.user_id]);
        console.log(`✅ Saved column preferences for table ${CAMPAIGNS_TABLE_KEY}`);
      } catch (error) {
        console.error(`❌ Failed to save column preferences for table ${CAMPAIGNS_TABLE_KEY}:`, error);
      }
    }
  };

  /**
   * Professional feedback system
   */
  const showFeedback = (message: string) => {
    setFeedbackMessage(message);
    setTimeout(() => {
      setFeedbackMessage(null);
    }, 3000);
  };

  /**
   * Add new campaign with permission checking
   */
  const handleAddCampaign = () => {
    if (currentUser?.role === UserRole.VIEWER) {
      showFeedback("Access Denied: Viewers cannot add new campaigns.");
      return;
    }
    navigate('/campaigns/add');
  };

  /**
   * Edit campaign with audit logging
   */
  const handleEditCampaign = (campaignId: string) => {
    navigate(`/campaigns/edit/${campaignId}`);
  };

  /**
   * Handle double-click on campaign row
   * For sent campaigns, open campaign-specific sending logs instead of editing
   */
  const handleCampaignDoubleClick = (campaign: Campaign) => {
    if (campaign.status === CampaignStatus.SENT) {
      // For sent campaigns, open campaign-specific sending logs page
      navigate(`/campaigns/${campaign.id}/sending-logs`);
    } else {
      // For non-sent campaigns, allow editing
      handleEditCampaign(campaign.id);
    }
  };

  /**
   * Send/Resume campaign with permission checking
   */
  const handleSendCampaign = (campaignId: string) => {
    const campaign = campaigns.find(c => c.id === campaignId);
    if (campaign && !canUserExecuteCampaign(campaign, currentUser)) {
      showFeedback("Access Denied: You do not have permission to send this campaign.");
      return;
    }
    showFeedback(`Simulating send/resume for campaign ID: ${campaignId}. Full functionality in Add/Edit page.`);
    navigate(`/campaigns/edit/${campaignId}`);
  };

  /**
   * Professional campaign deletion with database operations
   */
  const confirmDeleteCampaign = async () => {
    if (!campaignToDelete) return;
    try {
      console.log(`🗑️ [CampaignsPage] Deleting campaign: ${campaignToDelete.id}`);
      await campaignService.deleteCampaign(campaignToDelete.id);
      // Log successful deletion
      if (currentUser) {
        await addAuditLog(
          AuditActionType.DELETE,
          'Campaign',
          `Deleted campaign: ${campaignToDelete.name}`,
          {
            entityId: campaignToDelete.id,
            userId: currentUser.user_id,
            metadata: { name: campaignToDelete.name }
          }
        );
      }
      showFeedback(`Campaign "${campaignToDelete.name}" deleted successfully.`);
      console.log(`✅ [CampaignsPage] Campaign ${campaignToDelete.id} deleted successfully`);
      // Refresh campaigns list
      fetchCampaigns();
    } catch (error: any) {
      console.error('❌ [CampaignsPage] Error deleting campaign:', error);
      showFeedback(`Error deleting campaign: ${error.message}`);
      // Log deletion failure
      if (currentUser) {
        await addAuditLog(
          AuditActionType.ERROR,
          'Campaign',
          `Failed to delete campaign: ${campaignToDelete.name}`,
          {
            entityId: campaignToDelete.id,
            userId: currentUser.user_id,
            metadata: {
              name: campaignToDelete.name,
              error: error.message
            }
          }
        );
      }
    } finally {
      setShowDeleteModal(false);
      setCampaignToDelete(null);
    }
  };

  /**
   * Initiate campaign deletion with permission checking
   */
  const handleDeleteCampaignConfirmation = (campaign: Campaign) => {
    if (!canUserEditDeleteItem(campaign as AccessibleItem, currentUser)) {
      showFeedback("Access Denied: You do not have permission to delete this campaign.");
      return;
    }
    setCampaignToDelete(campaign);
    setShowDeleteModal(true);
  };

  /**
   * Handle overdue campaign actions
   */
  const handleSendOverdueNow = async (campaign: Campaign) => {
    try {
      // Update campaign status to sending and trigger immediate send
      await campaignService.updateCampaign(campaign.id, {
        ...campaign,
        status: CampaignStatus.SENDING,
        sent_date: new Date().toISOString()
      });

      // Log the action
      if (currentUser) {
        await addAuditLog(
          AuditActionType.SEND_MANUAL,
          'Campaign',
          `Sent overdue campaign immediately: ${campaign.name}`,
          {
            entityId: campaign.id,
            entityName: campaign.name,
            userId: currentUser.user_id,
            metadata: {
              originalScheduledDate: campaign.scheduled_date,
              actualSentDate: new Date().toISOString()
            }
          }
        );
      }

      showFeedback(`Campaign "${campaign.name}" sent immediately.`);
      fetchCampaigns(); // Refresh the list
    } catch (error: any) {
      console.error('Error sending overdue campaign:', error);
      showFeedback(`Error sending campaign: ${error.message}`);
    }
  };

  const handleRescheduleOverdue = (campaign: Campaign) => {
    // Navigate to edit page for rescheduling
    navigate(`/campaigns/edit/${campaign.id}`);
  };

  const handleDismissOverdue = (campaign: Campaign) => {
    // For now, just refresh - in a real app you might want to mark as dismissed
    showFeedback(`Overdue alert dismissed for "${campaign.name}". Campaign remains scheduled.`);
  };

  /**
   * Clear date filters
   */
  const clearDateFilters = () => {
    setStartDateFilterInput('');
    setEndDateFilterInput('');
  };

  /**
   * Detect overdue campaigns
   */
  const overdueCampaigns = useMemo(() => {
    const now = new Date();
    return campaigns.filter(campaign =>
      campaign.status === CampaignStatus.SCHEDULED &&
      campaign.scheduled_date &&
      new Date(campaign.scheduled_date) < now
    );
  }, [campaigns]);

  // Force re-render when sendingLogs change or campaigns change
  useEffect(() => {
    if (sendingLogs.length > 0) {
      console.log(`🔄 [CampaignsPage] SendingLogs loaded: ${sendingLogs.length} logs`);
      // Force re-render by updating a dummy state
      setForceRender(prev => prev + 1);
      console.log(`🔄 [CampaignsPage] Forcing re-render to update campaign counts`);
    }
  }, [sendingLogs]);

  // Force re-render when campaigns data changes to ensure fresh recipient counts
  useEffect(() => {
    if (campaigns.length > 0) {
      console.log(`🔄 [CampaignsPage] Campaigns loaded: ${campaigns.length} campaigns`);
      setForceRender(prev => prev + 1);
      console.log(`🔄 [CampaignsPage] Forcing re-render for fresh campaign data`);
    }
  }, [campaigns]);

  // Removed modal scroll prevention effect - no longer needed



  /**
   * Professional campaign filtering and search
   */
  const filteredCampaigns = useMemo(() => {
    let displayCampaigns = [...campaigns];

    // Filter by status
    switch (activeFilter) {
      case 'CURRENT_ACTIVITY':
        displayCampaigns = displayCampaigns.filter(c =>
          c.status === CampaignStatus.SCHEDULED ||
          c.status === CampaignStatus.SENDING ||
          c.status === CampaignStatus.SENT
        );
        break;
      case 'SCHEDULED_SENDING':
        displayCampaigns = displayCampaigns.filter(c => c.status === CampaignStatus.SCHEDULED || c.status === CampaignStatus.SENDING);
        break;
      case 'COMPLETED':
        displayCampaigns = displayCampaigns.filter(c => c.status === CampaignStatus.SENT || c.status === CampaignStatus.ARCHIVED || c.status === CampaignStatus.FAILED);
        break;
      case 'DRAFT':
        displayCampaigns = displayCampaigns.filter(c => c.status === CampaignStatus.DRAFT);
        break;
      default:
        break;
    }

    // Date filtering
    const startDate = startDateFilterInput && isValidIndianDateInput(startDateFilterInput) ? parseIndianDateStringToDate(startDateFilterInput) : null;
    const endDate = endDateFilterInput && isValidIndianDateInput(endDateFilterInput) ? parseIndianDateStringToDate(endDateFilterInput) : null;
    if (startDate) startDate.setHours(0, 0, 0, 0);
    if (endDate) endDate.setHours(23, 59, 59, 999);
    if (startDate || endDate) {
      displayCampaigns = displayCampaigns.filter(campaign => {
        const campaignDateStr = campaign.status === CampaignStatus.SENT ? campaign.sent_date : campaign.scheduled_date;
        if (!campaignDateStr) return false;
        const campaignDate = new Date(campaignDateStr);
        const afterStartDate = startDate ? campaignDate >= startDate : true;
        const beforeEndDate = endDate ? campaignDate <= endDate : true;
        return afterStartDate && beforeEndDate;
      });
    }

    // Text search
    if (searchTerm.trim()) {
      const lowercasedFilter = searchTerm.toLowerCase();
      displayCampaigns = displayCampaigns.filter(campaign =>
        campaign.name.toLowerCase().includes(lowercasedFilter) ||
        campaign.subject.toLowerCase().includes(lowercasedFilter) ||
        (campaign.campaign_type && campaign.campaign_type.toLowerCase().includes(lowercasedFilter)) ||
        (campaign.target_segments && campaign.target_segments.some(segment => segment.toLowerCase().includes(lowercasedFilter))) ||
        campaign.status.toLowerCase().includes(lowercasedFilter) ||
        (campaign.id.toLowerCase().includes(lowercasedFilter))
      );
    }

    return displayCampaigns.sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime());
  }, [campaigns, searchTerm, activeFilter, startDateFilterInput, endDateFilterInput]);

  /**
   * Filter button styling
   */
  const filterButtonClass = (filterType: CampaignFilterStatus) =>
    `py-2 px-4 text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-colors duration-150
     ${activeFilter === filterType
       ? 'bg-primary text-white focus:ring-primary'
       : 'bg-surface text-textPrimary hover:bg-gray-100 dark:hover:bg-gray-700 focus:ring-gray-400 border border-border'
     }`;

  /**
   * Handle authentication requirement
   */
  if (!currentUser) {
    return (
      <div className="text-textPrimary">
        <Header title="Campaign Management" subtitle="Oversee and manage all standard communication campaigns." />
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-gray-600 dark:text-gray-400">Please log in to access campaigns.</p>
          </div>
        </div>
      </div>
    );
  }

  /**
   * Professional loading state
   */
  if (isLoading) {
    return (
      <div className="text-textPrimary">
        <Header title="Campaign Management" subtitle="Oversee and manage all standard communication campaigns." />
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading campaigns from database...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="text-textPrimary">
      <Header title="Campaign Management" subtitle="Oversee and manage all standard communication campaigns." />

      {/* Overdue Campaigns Alert */}
      {overdueCampaigns.length > 0 && (
        <div className="mb-6 p-4 bg-red-50 border-l-4 border-red-400 rounded-md shadow-sm">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-bold">!</span>
              </div>
            </div>
            <div className="ml-3 flex-1">
              <h3 className="text-lg font-medium text-red-800">
                {overdueCampaigns.length} Overdue Campaign{overdueCampaigns.length !== 1 ? 's' : ''}
              </h3>
              <p className="text-sm text-red-700 mb-3">
                The following campaign{overdueCampaigns.length !== 1 ? 's have' : ' has'} passed {overdueCampaigns.length !== 1 ? 'their' : 'its'} scheduled time:
              </p>
              <div className="space-y-3">
                {overdueCampaigns.map(campaign => (
                  <div key={campaign.id} className="bg-white p-3 rounded-md border border-red-200">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{campaign.name}</h4>
                        <p className="text-sm text-gray-600">
                          Scheduled: {formatDateForDisplay(campaign.scheduled_date!, { type: 'datetime' })}
                        </p>
                        <p className="text-sm text-gray-600">
                          Recipients: {campaign.total_recipients || 0}
                        </p>
                      </div>
                      <div className="flex space-x-2 ml-4">
                        <button
                          type="button"
                          onClick={() => handleSendOverdueNow(campaign)}
                          className="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
                          title="Send campaign immediately"
                        >
                          Send Now
                        </button>
                        <button
                          type="button"
                          onClick={() => handleRescheduleOverdue(campaign)}
                          className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
                          title="Reschedule campaign"
                        >
                          Reschedule
                        </button>
                        <button
                          type="button"
                          onClick={() => handleEditCampaign(campaign.id)}
                          className="bg-yellow-500 hover:bg-yellow-600 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
                          title="Edit campaign"
                        >
                          Edit
                        </button>
                        <button
                          type="button"
                          onClick={() => handleDeleteCampaignConfirmation(campaign)}
                          className="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
                          title="Delete campaign"
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Professional Feedback System */}
      {feedbackMessage && (
        <div className="mb-4 p-3 bg-green-100 text-green-700 border border-green-300 rounded-md shadow-sm text-sm dark:bg-green-700 dark:text-green-100 dark:border-green-500" role="alert">
          {feedbackMessage}
        </div>
      )}

      {/* Professional Search and Controls */}
      <div className="mb-6 flex flex-wrap items-center gap-4">
        <input
          type="text"
          placeholder="Search campaigns (name, subject, type, status, ID)..."
          className="flex-1 min-w-[250px] px-4 py-2 border border-border bg-surface rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          aria-label="Search campaigns"
        />
        <div className="flex items-center gap-2 flex-wrap">
          <ColumnSelector
            allColumns={allTableColumns}
            visibleColumnIds={visibleColumnIds}
            onSave={handleColumnVisibilityChange}
            defaultVisibleColumnIds={defaultVisibleColumnIds}
            tableKey={CAMPAIGNS_TABLE_KEY}
            userId={currentUser?.user_id}
          />



          <button
            type="button"
            onClick={handleAddCampaign}
            disabled={currentUser?.role === UserRole.VIEWER}
            className={`bg-primary hover:bg-opacity-80 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transition duration-150 ease-in-out flex items-center justify-center ${currentUser?.role === UserRole.VIEWER ? 'opacity-50 cursor-not-allowed' : ''}`}
            aria-label="Add new campaign"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            Add New Campaign
          </button>
        </div>
      </div>

      {/* Professional Filter System */}
      <div className="mb-4 flex flex-wrap gap-2 items-center">
        <span className="text-sm font-medium text-textSecondary mr-2">Show:</span>
        <button type="button" onClick={() => setActiveFilter('CURRENT_ACTIVITY')} className={filterButtonClass('CURRENT_ACTIVITY')}>Current Activity</button>
        <button type="button" onClick={() => setActiveFilter('SCHEDULED_SENDING')} className={filterButtonClass('SCHEDULED_SENDING')}>Scheduled/In Progress</button>
        <button type="button" onClick={() => setActiveFilter('COMPLETED')} className={filterButtonClass('COMPLETED')}>Completed/Archived</button>
        <button type="button" onClick={() => setActiveFilter('DRAFT')} className={filterButtonClass('DRAFT')}>Drafts</button>

        {/* Sending Logs Button */}
        <div className="ml-4 border-l border-border pl-4">
          <button
            type="button"
            onClick={() => navigate('/campaigns/sending-logs')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors flex items-center"
          >
            📊 Sending Logs
          </button>
        </div>
      </div>

      {/* Professional Date Filters */}
      <div className="mb-4 p-4 bg-surface border border-border rounded-lg shadow-sm">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 items-end">
          <div>
            <label htmlFor="startDateFilterInput" className="block text-sm font-medium text-textPrimary">Scheduled/Sent From:</label>
            <input
              type="text"
              id="startDateFilterInput"
              value={startDateFilterInput}
              onChange={(e) => setStartDateFilterInput(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-border bg-surface rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm"
              placeholder="dd/mm/yy"
              aria-label="Filter campaigns scheduled or sent from date"
            />
          </div>
          <div>
            <label htmlFor="endDateFilterInput" className="block text-sm font-medium text-textPrimary">Scheduled/Sent To:</label>
            <input
              type="text"
              id="endDateFilterInput"
              value={endDateFilterInput}
              onChange={(e) => setEndDateFilterInput(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-border bg-surface rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm"
              placeholder="dd/mm/yy"
              aria-label="Filter campaigns scheduled or sent to date"
            />
          </div>
          <button
            onClick={clearDateFilters}
            className="py-2 px-4 text-sm font-medium rounded-md shadow-sm bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-textPrimary focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50 transition-colors duration-150 h-fit"
            aria-label="Clear date filters"
          >
            Clear Dates
          </button>
        </div>
      </div>

      {/* Professional Data Display */}
      <div className="overflow-x-auto">
        <Table<Campaign>
          allColumns={allTableColumns}
          visibleColumnIds={visibleColumnIds}
          data={filteredCampaigns}
          caption="Campaign List (Standard Campaigns)"
          rowKey="id"
          onRowDoubleClick={(campaign) => handleCampaignDoubleClick(campaign)}
          userId={currentUser?.user_id}
        />
      </div>

      {/* Professional Delete Confirmation */}
      {showDeleteModal && campaignToDelete && (
        <ConfirmationModal
          isOpen={showDeleteModal}
          title="Confirm Deletion"
          message={<>Are you sure you want to delete campaign: <strong>{campaignToDelete.name}</strong>?</>}
          onConfirm={confirmDeleteCampaign}
          onCancel={() => { setShowDeleteModal(false); setCampaignToDelete(null); }}
          confirmText="Delete"
        />
      )}

      {/* Campaign Sending Logs Modal - Replaced with dedicated page navigation */}



      {/* Professional Information Note */}
      <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/30 border-l-4 border-primary rounded-md">
        <h4 className="text-md font-semibold text-primary mb-1">Note on Campaign Sending</h4>
        <p className="text-sm text-textSecondary">
          Campaigns are sent based on their scheduled date or manual triggers from the edit page. Sending progress and recipient counts are updated conceptually.
          For Birthday Automations, please visit the dedicated "Birthday Automations" section.
        </p>
      </div>
    </div>
  );
};

export default CampaignsPage;