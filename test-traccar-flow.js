// Test complete Traccar configuration flow
const baseUrl = 'http://localhost:3001/api';

async function testTraccarFlow() {
  console.log('🧪 Testing Complete Traccar Flow...\n');

  try {
    // Test 1: Test endpoint without configuration
    console.log('1. Testing Traccar endpoint without configuration...');
    const testResponse1 = await fetch(`${baseUrl}/sms/traccar/test`);
    
    if (testResponse1.ok) {
      const result1 = await testResponse1.json();
      console.log('   ✅ Response received (200 status):', result1);
      console.log(`   📊 Success: ${result1.success}, Status: ${result1.status}`);
    } else {
      console.log('   ❌ Unexpected error:', testResponse1.status);
    }

    // Test 2: Configure Traccar
    console.log('\n2. Configuring Traccar...');
    const configResponse = await fetch(`${baseUrl}/sms/traccar/config`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        baseUrl: 'http://*************:8080',
        apiKey: 'test-api-key-12345',
        enabled: true
      })
    });

    if (configResponse.ok) {
      const configResult = await configResponse.json();
      console.log('   ✅ Configuration saved:', configResult);
    } else {
      console.log('   ❌ Configuration failed:', configResponse.status);
    }

    // Test 3: Test endpoint with configuration
    console.log('\n3. Testing Traccar endpoint with configuration...');
    const testResponse2 = await fetch(`${baseUrl}/sms/traccar/test`);
    
    if (testResponse2.ok) {
      const result2 = await testResponse2.json();
      console.log('   ✅ Response received:', result2);
      console.log(`   📊 Success: ${result2.success}, Status: ${result2.status || 'testing'}`);
      
      if (!result2.success) {
        console.log('   ℹ️ This is expected - no actual Android device connected');
      }
    } else {
      console.log('   ❌ Unexpected error:', testResponse2.status);
    }

    // Test 4: Check SMS endpoints still work
    console.log('\n4. Verifying SMS endpoints still work...');
    
    const statusResponse = await fetch(`${baseUrl}/sms/status`);
    if (statusResponse.ok) {
      const status = await statusResponse.json();
      console.log('   ✅ SMS Status:', status.status);
    }

    const pendingResponse = await fetch(`${baseUrl}/sms/pending`);
    if (pendingResponse.ok) {
      const pending = await pendingResponse.json();
      console.log('   ✅ Pending messages:', pending.length);
    }

    console.log('\n🎉 Traccar flow testing complete!');
    console.log('\n📋 Summary:');
    console.log('✅ Traccar test endpoint returns 200 (not 400)');
    console.log('✅ Configuration can be saved');
    console.log('✅ Test endpoint provides proper error messages');
    console.log('✅ SMS endpoints continue to work');
    console.log('\n🎯 Next step: Open SMS Web Gateway and test the interface');

  } catch (error) {
    console.error('❌ Error testing Traccar flow:', error);
  }
}

// Run the test
testTraccarFlow();
