import React, { useState, useEffect } from 'react';
import { CheckC<PERSON>cleIcon, XCircleIcon, ClockIcon, RefreshIcon } from './icons';
import { WhatsAppNutjsService } from '../services/whatsapp-automation/WhatsAppNutjsService';

interface SendingLogEntry {
  id: string;
  campaign_id: string;
  campaign_name: string;
  recipient_name: string;
  recipient_email?: string;
  recipient_phone?: string;
  recipient_whatsapp?: string;
  channel: 'email' | 'whatsapp' | 'sms';
  status: 'sent' | 'failed' | 'pending';
  sent_at: string;
  error_message?: string;
  message_id?: string;
}

interface CampaignApiResponse {
  id: string;
  name: string;
  status: string;
  total_recipients?: number;
  created_at: string;
  sent_date?: string;
}

interface CampaignSummary {
  id: string;
  name: string;
  status: string;
  total_recipients: number;
  created_at: string;
  sent_date?: string;
  logCounts: {
    total: number;
    sent: number;
    pending: number;
    failed: number;
    email: number;
    whatsapp: number;
    sms: number;
  };
}

interface CampaignSendingLogsProps {
  className?: string;
  campaignId?: string;
  hideHeader?: boolean; // Option to hide the header when used in modals
}

const CampaignSendingLogs: React.FC<CampaignSendingLogsProps> = ({
  className = '',
  campaignId,
  hideHeader = false
}) => {
  const [campaigns, setCampaigns] = useState<CampaignSummary[]>([]);
  const [logs, setLogs] = useState<SendingLogEntry[]>([]);
  const [selectedCampaignId, setSelectedCampaignId] = useState<string>(campaignId || '');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedChannel, setSelectedChannel] = useState<string>('all');
  const [campaignFilter, setCampaignFilter] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [view, setView] = useState<'campaigns' | 'logs'>(campaignId ? 'logs' : 'campaigns');

  const fetchCampaigns = async () => {
    try {
      setIsLoading(true);

      // Fetch campaigns
      const campaignsResponse = await fetch('/api/campaigns');
      if (!campaignsResponse.ok) throw new Error('Failed to fetch campaigns');
      const campaignsData = await campaignsResponse.json();

      // Fetch all logs to calculate summaries
      const logsResponse = await fetch('/api/campaign-sending-logs');
      if (!logsResponse.ok) throw new Error('Failed to fetch logs');
      const allLogs = await logsResponse.json();

      // Create campaign summaries with log counts - ONLY for campaigns that have been sent or have logs
      const campaignSummaries: CampaignSummary[] = campaignsData
        .map((campaign: CampaignApiResponse) => {
          const campaignLogs = allLogs.filter((log: SendingLogEntry) => log.campaign_id === campaign.id);

          return {
            id: campaign.id,
            name: campaign.name,
            status: campaign.status,
            total_recipients: campaign.total_recipients || 0,
            created_at: campaign.created_at,
            sent_date: campaign.sent_date,
            logCounts: {
              total: campaignLogs.length,
              sent: campaignLogs.filter((log: SendingLogEntry) => log.status === 'sent').length,
              pending: campaignLogs.filter((log: SendingLogEntry) => log.status === 'pending').length,
              failed: campaignLogs.filter((log: SendingLogEntry) => log.status === 'failed').length,
              email: campaignLogs.filter((log: SendingLogEntry) => log.channel === 'email').length,
              whatsapp: campaignLogs.filter((log: SendingLogEntry) => log.channel === 'whatsapp').length,
              sms: campaignLogs.filter((log: SendingLogEntry) => log.channel === 'sms').length,
            }
          };
        })
        .filter((campaign: CampaignSummary) => {
          // Only include campaigns that have been sent, are sending, or have sending logs
          const hasLogs = campaign.logCounts.total > 0;
          const isSentOrSending = campaign.status === 'sent' || campaign.status === 'sending' || campaign.status === 'completed';
          const hasBeenScheduled = campaign.sent_date !== null && campaign.sent_date !== undefined;

          return hasLogs || isSentOrSending || hasBeenScheduled;
        });

      setCampaigns(campaignSummaries);
    } catch (error) {
      console.error('Error fetching campaigns:', error);
      setError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchLogs = async () => {
    try {
      setError(null);
      const targetCampaignId = selectedCampaignId || campaignId;
      const url = targetCampaignId
        ? `/api/campaign-sending-logs/${targetCampaignId}`
        : '/api/campaign-sending-logs';

      const response = await fetch(url);
      if (!response.ok) throw new Error('Failed to fetch sending logs');

      const data = await response.json();
      setLogs(data);
    } catch (error) {
      console.error('Error fetching sending logs:', error);
      setError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (view === 'campaigns') {
      fetchCampaigns();
    } else if (selectedCampaignId) {
      fetchLogs();
    }
  }, [view, selectedCampaignId]);

  // Handle campaignId prop changes
  useEffect(() => {
    if (campaignId && campaignId !== selectedCampaignId) {
      setSelectedCampaignId(campaignId);
      setView('logs');
    }
  }, [campaignId]);

  const handleCampaignSelect = (campaign: CampaignSummary) => {
    setSelectedCampaignId(campaign.id);
    setView('logs');
    setIsLoading(true);
  };

  const handleBackToCampaigns = () => {
    setView('campaigns');
    setSelectedCampaignId('');
    setLogs([]);
  };

  // Filter campaigns
  const filteredCampaigns = campaigns.filter((campaign: CampaignSummary) => {
    // Filter by status
    if (campaignFilter !== 'all') {
      if (campaignFilter === 'with_logs' && campaign.logCounts.total === 0) return false;
      if (campaignFilter === 'sent' && campaign.status !== 'sent') return false;
      if (campaignFilter === 'pending' && campaign.logCounts.pending === 0) return false;
      if (campaignFilter === 'failed' && campaign.logCounts.failed === 0) return false;
    }

    // Filter by search term
    if (searchTerm && !campaign.name.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false;
    }

    return true;
  });

  // Filter logs
  const filteredLogs = selectedChannel === 'all'
    ? logs
    : logs.filter(log => log.channel === selectedChannel);

  const getChannelIcon = (channel: string) => {
    switch (channel) {
      case 'email': return '📧';
      case 'whatsapp': return '💬';
      case 'sms': return '📱';
      default: return '📤';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent': return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
      case 'failed': return <XCircleIcon className="h-4 w-4 text-red-500" />;
      case 'pending': return <ClockIcon className="h-4 w-4 text-yellow-500" />;
      default: return <ClockIcon className="h-4 w-4 text-gray-500" />;
    }
  };

  const stats = {
    total: filteredLogs.length,
    sent: filteredLogs.filter(log => log.status === 'sent').length,
    failed: filteredLogs.filter(log => log.status === 'failed').length,
    pending: filteredLogs.filter(log => log.status === 'pending').length
  };

  const pendingWhatsAppLogs = filteredLogs.filter(log =>
    log.status === 'pending' && log.channel === 'whatsapp' && log.message_id
  );

  const handleSendWhatsAppMessage = async (phone: string, message: string, logId: string) => {
    try {
      const whatsappService = new WhatsAppNutjsService();
      await whatsappService.initialize();
      
      const result = await whatsappService.sendSingleMessage({
        phone,
        message,
        name: logs.find(l => l.id === logId)?.recipient_name || ''
      });

      if (result.success) {
        setLogs(prevLogs =>
          prevLogs.map(log =>
            log.id === logId
              ? { ...log, status: 'sent' as const, message_id: result.messageId }
              : log
          )
        );
      } else {
        setLogs(prevLogs =>
          prevLogs.map(log =>
            log.id === logId
              ? { ...log, status: 'failed' as const, error_message: result.error }
              : log
          )
        );
      }
    } catch (error) {
      console.error('Failed to send WhatsApp message:', error);
      setLogs(prevLogs =>
        prevLogs.map(log =>
          log.id === logId
            ? { ...log, status: 'failed' as const, error_message: error instanceof Error ? error.message : 'Unknown error' }
            : log
        )
      );
    }
  };

  const handleSendAllWhatsAppMessages = async () => {
    for (const log of pendingWhatsAppLogs) {
      if (log.recipient_phone) {
        await handleSendWhatsAppMessage(
          log.recipient_phone,
          log.message_id || '', // Using message_id as the message content
          log.id
        );
        await new Promise(resolve => setTimeout(resolve, 3000)); // 3 second delay between messages
      }
    }
  };

  if (isLoading) {
    return (
      <div className={`bg-surface rounded-lg border border-border p-6 ${className}`}>
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mr-2"></div>
          <span className="text-primary">Loading sending logs...</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-surface rounded-lg border border-border ${className} flex flex-col max-h-[80vh]`}>
      {/* Header - conditionally rendered */}
      {!hideHeader && (
        <div className="flex items-center justify-between p-6 border-b border-border flex-shrink-0">
          <div className="flex items-center">
          {view === 'logs' && (
            <button
              type="button"
              onClick={handleBackToCampaigns}
              className="mr-3 p-1 text-textSecondary hover:text-primary transition-colors"
              title="Back to campaigns"
            >
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
          )}
          <div>
            <h3 className="text-lg font-semibold text-primary">
              {view === 'campaigns' ? 'Campaign Sending Logs' : `Logs: ${campaigns.find(c => c.id === selectedCampaignId)?.name || 'Campaign'}`}
            </h3>
            <p className="text-sm text-textSecondary">
              {view === 'campaigns' ? 'Select a campaign to view its sending logs' : 'Detailed log of messages sent to recipients'}
            </p>
          </div>
          <button
            type="button"
            onClick={view === 'campaigns' ? fetchCampaigns : fetchLogs}
            className="p-2 text-textSecondary hover:text-primary transition-colors"
            title="Refresh"
          >
            <RefreshIcon className="h-4 w-4" />
          </button>
        </div>
      </div>
      )}

      {/* Content Area */}
      <div className="flex-1 overflow-hidden flex flex-col">
        {view === 'campaigns' ? (
          /* CAMPAIGNS VIEW */
          <div className="flex flex-col h-full">
            {/* Campaign Filters */}
            <div className="p-4 border-b border-border flex-shrink-0">
              <div className="flex flex-wrap gap-4 items-center">
                {/* Search */}
                <div className="flex-1 min-w-64">
                  <input
                    type="text"
                    placeholder="Search campaigns..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full px-3 py-2 border border-border bg-surface rounded-md shadow-sm focus:ring-primary focus:border-primary text-sm"
                  />
                </div>

                {/* Status Filter */}
                <div className="flex space-x-2">
                  {[
                    { key: 'all', label: 'All' },
                    { key: 'with_logs', label: 'With Logs' },
                    { key: 'sent', label: 'Sent' },
                    { key: 'pending', label: 'Pending' },
                    { key: 'failed', label: 'Failed' }
                  ].map(filter => (
                    <button
                      type="button"
                      key={filter.key}
                      onClick={() => setCampaignFilter(filter.key)}
                      className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                        campaignFilter === filter.key
                          ? 'bg-primary text-white'
                          : 'bg-background text-textSecondary hover:bg-border'
                      }`}
                    >
                      {filter.label}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Campaigns List */}
            <div className="flex-1 overflow-auto p-4">
              {isLoading ? (
                <div className="text-center py-8 text-textSecondary">Loading campaigns...</div>
              ) : filteredCampaigns.length === 0 ? (
                <div className="text-center py-8 text-textSecondary">
                  <p>No campaigns found</p>
                  <p className="text-sm mt-1">Try adjusting your filters</p>
                </div>
              ) : (
                <div className="grid gap-4">
                  {filteredCampaigns.map(campaign => (
                    <div
                      key={campaign.id}
                      onClick={() => handleCampaignSelect(campaign)}
                      className="bg-background border border-border rounded-lg p-4 hover:bg-border cursor-pointer transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium text-primary">
                            {campaign.name}
                          </h4>
                          <p className="text-sm text-textSecondary mt-1">
                            <span className="font-mono bg-gray-100 px-2 py-0.5 rounded text-xs">ID: {campaign.id}</span>
                          </p>
                          <p className="text-sm text-textSecondary mt-1">
                            Status: {campaign.status} • Recipients: {campaign.total_recipients} •
                            Created: {new Date(campaign.created_at).toLocaleString(undefined, {
                              year: 'numeric',
                              month: '2-digit', 
                              day: '2-digit',
                              hour: '2-digit',
                              minute: '2-digit',
                              hour12: true
                            })}
                            {campaign.sent_date && (
                              <> • Sent: {new Date(campaign.sent_date).toLocaleString(undefined, {
                                year: 'numeric',
                                month: '2-digit',
                                day: '2-digit', 
                                hour: '2-digit',
                                minute: '2-digit',
                                hour12: true
                              })}</>
                            )}
                          </p>
                        </div>
                        <div className="flex space-x-4 text-sm">
                          <div className="text-center">
                            <div className="font-bold text-primary">{campaign.logCounts.total}</div>
                            <div className="text-xs text-textSecondary">Total</div>
                          </div>
                          <div className="text-center">
                            <div className="font-bold text-green-500">{campaign.logCounts.sent}</div>
                            <div className="text-xs text-textSecondary">Sent</div>
                          </div>
                          <div className="text-center">
                            <div className="font-bold text-yellow-500">{campaign.logCounts.pending}</div>
                            <div className="text-xs text-textSecondary">Pending</div>
                          </div>
                          <div className="text-center">
                            <div className="font-bold text-red-500">{campaign.logCounts.failed}</div>
                            <div className="text-xs text-textSecondary">Failed</div>
                          </div>
                        </div>
                      </div>

                      {/* Channel breakdown */}
                      <div className="flex space-x-4 mt-3 text-xs text-textSecondary">
                        <span>📧 {campaign.logCounts.email}</span>
                        <span>💬 {campaign.logCounts.whatsapp}</span>
                        <span>📱 {campaign.logCounts.sms}</span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        ) : (
          /* LOGS VIEW */
          <div className="flex flex-col h-full p-6">

        {/* Error Display */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md flex-shrink-0">
            <div className="flex items-center">
              <XCircleIcon className="h-4 w-4 text-red-500 mr-2" />
              <span className="text-sm text-red-700">{error}</span>
            </div>
          </div>
        )}

        {/* Stats Summary */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6 flex-shrink-0">
          <div className="bg-background rounded-lg p-3 border border-border">
            <div className="text-center">
              <p className="text-2xl font-bold text-primary">{stats.total}</p>
              <p className="text-xs text-textSecondary">Total</p>
            </div>
          </div>
          <div className="bg-background rounded-lg p-3 border border-border">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-500">{stats.sent}</p>
              <p className="text-xs text-textSecondary">Sent</p>
            </div>
          </div>
          <div className="bg-background rounded-lg p-3 border border-border">
            <div className="text-center">
              <p className="text-2xl font-bold text-red-500">{stats.failed}</p>
              <p className="text-xs text-textSecondary">Failed</p>
            </div>
          </div>
          <div className="bg-background rounded-lg p-3 border border-border">
            <div className="text-center">
              <p className="text-2xl font-bold text-yellow-500">{stats.pending}</p>
              <p className="text-xs text-textSecondary">Pending</p>
            </div>
          </div>
        </div>

        {/* Channel Filter */}
        <div className="flex space-x-2 mb-4 flex-shrink-0">
          {['all', 'email', 'whatsapp', 'sms'].map(channel => (
            <button
              type="button"
              key={channel}
              onClick={() => setSelectedChannel(channel)}
              className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                selectedChannel === channel
                  ? 'bg-primary text-white'
                  : 'bg-background text-textSecondary hover:bg-border'
              }`}
            >
              {channel === 'all' ? 'All' : `${getChannelIcon(channel)} ${channel.charAt(0).toUpperCase() + channel.slice(1)}`}
            </button>
          ))}
        </div>

        {/* WhatsApp Actions */}
        {pendingWhatsAppLogs.length > 0 && (
          <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-md flex-shrink-0">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-green-800">
                  📱 {pendingWhatsAppLogs.length} WhatsApp message{pendingWhatsAppLogs.length !== 1 ? 's' : ''} ready to send
                </h4>
                <p className="text-xs text-green-600 mt-1">
                  Click to open WhatsApp Desktop and send messages
                </p>
              </div>
              <div className="flex space-x-2">
                <button
                  type="button"
                  onClick={handleSendAllWhatsAppMessages}
                  className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
                >
                  Auto Send All ({pendingWhatsAppLogs.length})
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Logs Table */}
        {filteredLogs.length === 0 ? (
          <div className="text-center py-8 text-textSecondary flex-1 flex items-center justify-center">
            <div>
              <p>No sending logs found</p>
              <p className="text-sm mt-1">Messages will appear here when campaigns are sent</p>
            </div>
          </div>
        ) : (
          <div className="flex-1 overflow-auto">
            <table className="w-full text-sm">
              <thead className="sticky top-0 bg-surface border-b border-border">
                <tr>
                  <th className="text-left py-2 px-3 font-medium text-textSecondary">Status</th>
                  <th className="text-left py-2 px-3 font-medium text-textSecondary">Channel</th>
                  <th className="text-left py-2 px-3 font-medium text-textSecondary">Recipient</th>
                  <th className="text-left py-2 px-3 font-medium text-textSecondary">Contact</th>
                  <th className="text-left py-2 px-3 font-medium text-textSecondary">Campaign</th>
                  <th className="text-left py-2 px-3 font-medium text-textSecondary">Campaign ID</th>
                  <th className="text-left py-2 px-3 font-medium text-textSecondary">Sent At</th>
                  <th className="text-left py-2 px-3 font-medium text-textSecondary">Error/Message ID</th>
                  <th className="text-left py-2 px-3 font-medium text-textSecondary">Action</th>
                </tr>
              </thead>
              <tbody>
                {filteredLogs.map((log) => (
                  <tr key={log.id} className="border-b border-border hover:bg-background">
                    <td className="py-2 px-3">
                      <div className="flex items-center">
                        {getStatusIcon(log.status)}
                        <span className="ml-2 text-xs">{log.status}</span>
                      </div>
                    </td>
                    <td className="py-2 px-3">
                      <span className="text-lg">{getChannelIcon(log.channel)}</span>
                    </td>
                    <td className="py-2 px-3 font-medium">{log.recipient_name}</td>
                    <td className="py-2 px-3 text-textSecondary">
                      {log.channel === 'email' && log.recipient_email}
                      {log.channel === 'whatsapp' && (log.recipient_whatsapp || log.recipient_phone)}
                      {log.channel === 'sms' && log.recipient_phone}
                    </td>
                    <td className="py-2 px-3 text-textSecondary">{log.campaign_name}</td>
                    <td className="py-2 px-3">
                      <span className="font-mono text-xs bg-gray-100 px-2 py-1 rounded">
                        {log.campaign_id}
                      </span>
                    </td>
                    <td className="py-2 px-3 text-textSecondary">
                      {new Date(log.sent_at).toLocaleString(undefined, {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit', 
                        minute: '2-digit',
                        second: '2-digit',
                        hour12: true
                      })}
                    </td>
                    <td className="py-2 px-3 text-textSecondary">
                      {log.error_message ? (
                        <span className="text-red-500 text-xs" title={log.error_message}>
                          {log.error_message.length > 30 ? `${log.error_message.substring(0, 30)}...` : log.error_message}
                        </span>
                      ) : log.message_id ? (
                        <span className="font-mono text-xs">{log.message_id.length > 30 ? `${log.message_id.substring(0, 30)}...` : log.message_id}</span>
                      ) : (
                        <span className="text-gray-400 text-xs">No ID</span>
                      )}
                    </td>
                    <td className="py-2 px-3">
                      {log.status === 'pending' && log.channel === 'whatsapp' && log.message_id ? (
                        <button
                          type="button"
                          onClick={() => log.recipient_phone && handleSendWhatsAppMessage(
                            log.recipient_phone,
                            log.message_id || '',
                            log.id
                          )}
                          className="bg-green-600 hover:bg-green-700 text-white px-2 py-1 rounded text-xs font-medium transition-colors"
                          title="Send via WhatsApp Automation"
                        >
                          🤖 Auto Send
                        </button>
                      ) : (
                        <span className="text-gray-400 text-xs">-</span>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          )}
        </div>
      )}
      </div>
    </div>
  );
};

export default CampaignSendingLogs;
