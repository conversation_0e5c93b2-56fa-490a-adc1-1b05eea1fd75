import { CampaignTemplate, TemplateStatus } from '../types';
import { browserDatabaseService } from './BrowserDatabaseService';

export const templateService = {
  async getAllTemplates(): Promise<CampaignTemplate[]> {
    try {
      console.log('📋 Getting templates from browser database...');
      const sql = `
        SELECT 
          id, template_name, display_name, description, campaign_type, status,
          subject_template, email_content, email_content_type,
          whatsapp_content_template, whatsapp_content_type, sms_content_template,
          email_signature_id, whatsapp_signature_id, sms_signature_id,
          requires_approval, sender_name, sender_email, sender_phone,
          merge_fields, target_segments, interest_area_id, is_active,
          is_public, version, uses_placeholders, available_placeholders,
          created_by, created_at, updated_at, attachments,
          is_recurring, recurrence_type, recurrence_days_of_month,
          recurrence_months_of_year, recurrence_specific_dates,
          is_admin_only, owner_user_id
        FROM campaign_templates
        ORDER BY created_at DESC
      `;
      
      const result = await browserDatabaseService.query(sql);
      return result.map((row: any) => ({
        ...row,
        merge_fields: row.merge_fields ? JSON.parse(row.merge_fields) : {},
        target_segments: row.target_segments ? JSON.parse(row.target_segments) : [],
        available_placeholders: row.available_placeholders ? JSON.parse(row.available_placeholders) : [],
        attachments: row.attachments ? JSON.parse(row.attachments) : [],
        recurrence_days_of_month: row.recurrence_days_of_month ? JSON.parse(row.recurrence_days_of_month) : [],
        recurrence_months_of_year: row.recurrence_months_of_year ? JSON.parse(row.recurrence_months_of_year) : [],
        recurrence_specific_dates: row.recurrence_specific_dates ? JSON.parse(row.recurrence_specific_dates) : [],
        status: row.status as TemplateStatus,
        is_active: Boolean(row.is_active),
        is_public: Boolean(row.is_public),
        uses_placeholders: Boolean(row.uses_placeholders),
        requires_approval: Boolean(row.requires_approval),
        is_recurring: Boolean(row.is_recurring),
        is_admin_only: Boolean(row.is_admin_only)
      }));
    } catch (error: unknown) {
      console.error('Error fetching templates:', error);
      throw new Error(`Failed to fetch templates: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  async getTemplateById(templateId: string): Promise<CampaignTemplate | null> {
    try {
      console.log('🔍 Getting template by ID:', templateId);
      const sql = `
        SELECT 
          id, template_name, display_name, description, campaign_type, status,
          subject_template, email_content, email_content_type,
          whatsapp_content_template, whatsapp_content_type, sms_content_template,
          email_signature_id, whatsapp_signature_id, sms_signature_id,
          requires_approval, sender_name, sender_email, sender_phone,
          merge_fields, target_segments, interest_area_id, is_active,
          is_public, version, uses_placeholders, available_placeholders,
          created_by, created_at, updated_at, attachments,
          is_recurring, recurrence_type, recurrence_days_of_month,
          recurrence_months_of_year, recurrence_specific_dates,
          is_admin_only, owner_user_id
        FROM campaign_templates
        WHERE id = ?
      `;
      
      const result = await browserDatabaseService.query(sql, [templateId]);
      if (result.length === 0) return null;
      
      const row = result[0];
      return {
        ...row,
        merge_fields: row.merge_fields ? JSON.parse(row.merge_fields) : {},
        target_segments: row.target_segments ? JSON.parse(row.target_segments) : [],
        available_placeholders: row.available_placeholders ? JSON.parse(row.available_placeholders) : [],
        attachments: row.attachments ? JSON.parse(row.attachments) : [],
        recurrence_days_of_month: row.recurrence_days_of_month ? JSON.parse(row.recurrence_days_of_month) : [],
        recurrence_months_of_year: row.recurrence_months_of_year ? JSON.parse(row.recurrence_months_of_year) : [],
        recurrence_specific_dates: row.recurrence_specific_dates ? JSON.parse(row.recurrence_specific_dates) : [],
        status: row.status as TemplateStatus,
        is_active: Boolean(row.is_active),
        is_public: Boolean(row.is_public),
        uses_placeholders: Boolean(row.uses_placeholders),
        requires_approval: Boolean(row.requires_approval),
        is_recurring: Boolean(row.is_recurring),
        is_admin_only: Boolean(row.is_admin_only)
      };
    } catch (error: unknown) {
      console.error('Error fetching template:', error);
      throw new Error(`Failed to fetch template: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  async createTemplate(templateData: Omit<CampaignTemplate, 'id' | 'created_at' | 'updated_at'>): Promise<CampaignTemplate> {
    try {
      console.log('➕ Creating new template:', templateData.display_name);
      const templateId = `tmpl_${Date.now()}`;
      const now = new Date().toISOString();
      
      const sql = `
        INSERT INTO campaign_templates (
          id, template_name, display_name, description, campaign_type, status,
          subject_template, email_content, email_content_type,
          whatsapp_content_template, whatsapp_content_type, sms_content_template,
          email_signature_id, whatsapp_signature_id, sms_signature_id,
          requires_approval, sender_name, sender_email, sender_phone,
          merge_fields, target_segments, interest_area_id, is_active,
          is_public, version, uses_placeholders, available_placeholders,
          created_by, created_at, updated_at, attachments,
          is_recurring, recurrence_type, recurrence_days_of_month,
          recurrence_months_of_year, recurrence_specific_dates,
          is_admin_only, owner_user_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      
      await browserDatabaseService.query(sql, [
        templateId,
        templateData.template_name,
        templateData.display_name,
        templateData.description || '',
        templateData.campaign_type,
        templateData.status,
        templateData.subject_template,
        templateData.email_content || '',
        templateData.email_content_type || 'html',
        templateData.whatsapp_content_template || '',
        templateData.whatsapp_content_type || 'plain',
        templateData.sms_content_template || '',
        templateData.email_signature_id || '',
        templateData.whatsapp_signature_id || '',
        templateData.sms_signature_id || '',
        templateData.requires_approval ? 1 : 0,
        templateData.sender_name,
        templateData.sender_email,
        templateData.sender_phone || '',
        JSON.stringify(templateData.merge_fields || {}),
        JSON.stringify(templateData.target_segments || []),
        templateData.interest_area_id || '',
        templateData.is_active ? 1 : 0,
        templateData.is_public ? 1 : 0,
        templateData.version || 1,
        templateData.uses_placeholders ? 1 : 0,
        JSON.stringify(templateData.available_placeholders || []),
        templateData.created_by,
        now,
        now,
        JSON.stringify(templateData.attachments || []),
        templateData.is_recurring ? 1 : 0,
        templateData.recurrence_type || null,
        JSON.stringify(templateData.recurrence_days_of_month || []),
        JSON.stringify(templateData.recurrence_months_of_year || []),
        JSON.stringify(templateData.recurrence_specific_dates || []),
        templateData.is_admin_only ? 1 : 0,
        templateData.owner_user_id || null
      ]);
      
      const createdTemplate = await this.getTemplateById(templateId);
      if (!createdTemplate) throw new Error('Failed to retrieve created template');
      
      return createdTemplate;
    } catch (error: unknown) {
      console.error('Error creating template:', error);
      throw new Error(`Failed to create template: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
  async updateTemplate(templateId: string, templateData: Partial<CampaignTemplate>): Promise<CampaignTemplate> {
    try {
      console.log('📝 Updating template:', templateId);
      const now = new Date().toISOString();
      
      const sql = `
        UPDATE campaign_templates SET
          template_name = COALESCE(?, template_name),
          display_name = COALESCE(?, display_name),
          description = COALESCE(?, description),
          campaign_type = COALESCE(?, campaign_type),
          status = COALESCE(?, status),
          subject_template = COALESCE(?, subject_template),
          email_content = COALESCE(?, email_content),
          email_content_type = COALESCE(?, email_content_type),
          whatsapp_content_template = COALESCE(?, whatsapp_content_template),
          whatsapp_content_type = COALESCE(?, whatsapp_content_type),
          sms_content_template = COALESCE(?, sms_content_template),
          email_signature_id = COALESCE(?, email_signature_id),
          whatsapp_signature_id = COALESCE(?, whatsapp_signature_id),
          sms_signature_id = COALESCE(?, sms_signature_id),
          requires_approval = COALESCE(?, requires_approval),
          sender_name = COALESCE(?, sender_name),
          sender_email = COALESCE(?, sender_email),
          sender_phone = COALESCE(?, sender_phone),
          merge_fields = COALESCE(?, merge_fields),
          target_segments = COALESCE(?, target_segments),
          interest_area_id = COALESCE(?, interest_area_id),
          is_active = COALESCE(?, is_active),
          is_public = COALESCE(?, is_public),
          version = COALESCE(?, version),
          uses_placeholders = COALESCE(?, uses_placeholders),
          available_placeholders = COALESCE(?, available_placeholders),
          updated_at = ?,
          attachments = COALESCE(?, attachments),
          is_recurring = COALESCE(?, is_recurring),
          recurrence_type = COALESCE(?, recurrence_type),
          recurrence_days_of_month = COALESCE(?, recurrence_days_of_month),
          recurrence_months_of_year = COALESCE(?, recurrence_months_of_year),
          recurrence_specific_dates = COALESCE(?, recurrence_specific_dates),
          is_admin_only = COALESCE(?, is_admin_only),
          owner_user_id = COALESCE(?, owner_user_id)
        WHERE id = ?
      `;
      
      await browserDatabaseService.query(sql, [
        templateData.template_name,
        templateData.display_name,
        templateData.description,
        templateData.campaign_type,
        templateData.status,
        templateData.subject_template,
        templateData.email_content,
        templateData.email_content_type,
        templateData.whatsapp_content_template,
        templateData.whatsapp_content_type,
        templateData.sms_content_template,
        templateData.email_signature_id,
        templateData.whatsapp_signature_id,
        templateData.sms_signature_id,
        templateData.requires_approval !== undefined ? (templateData.requires_approval ? 1 : 0) : null,
        templateData.sender_name,
        templateData.sender_email,
        templateData.sender_phone,
        templateData.merge_fields ? JSON.stringify(templateData.merge_fields) : null,
        templateData.target_segments ? JSON.stringify(templateData.target_segments) : null,
        templateData.interest_area_id,
        templateData.is_active !== undefined ? (templateData.is_active ? 1 : 0) : null,
        templateData.is_public !== undefined ? (templateData.is_public ? 1 : 0) : null,
        templateData.version,
        templateData.uses_placeholders !== undefined ? (templateData.uses_placeholders ? 1 : 0) : null,
        templateData.available_placeholders ? JSON.stringify(templateData.available_placeholders) : null,
        now,
        templateData.attachments ? JSON.stringify(templateData.attachments) : null,
        templateData.is_recurring !== undefined ? (templateData.is_recurring ? 1 : 0) : null,
        templateData.recurrence_type,
        templateData.recurrence_days_of_month ? JSON.stringify(templateData.recurrence_days_of_month) : null,
        templateData.recurrence_months_of_year ? JSON.stringify(templateData.recurrence_months_of_year) : null,
        templateData.recurrence_specific_dates ? JSON.stringify(templateData.recurrence_specific_dates) : null,
        templateData.is_admin_only !== undefined ? (templateData.is_admin_only ? 1 : 0) : null,
        templateData.owner_user_id,
        templateId
      ]);
      
      const updatedTemplate = await this.getTemplateById(templateId);
      if (!updatedTemplate) throw new Error('Failed to retrieve updated template');
      
      return updatedTemplate;
    } catch (error: unknown) {
      console.error('Error updating template:', error);
      throw new Error(`Failed to update template: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  async deleteTemplate(templateId: string): Promise<void> {
    try {
      console.log('🗑️ Deleting template:', templateId);
      const sql = `DELETE FROM campaign_templates WHERE id = ?`;
      await browserDatabaseService.query(sql, [templateId]);
    } catch (error: unknown) {
      console.error('Error deleting template:', error);
      throw new Error(`Failed to delete template: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
};
