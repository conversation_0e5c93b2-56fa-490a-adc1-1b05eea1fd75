{"logs": [{"outputFile": "com.crmsms.app-mergeDebugResources-27:/values-mk/values-mk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7da3e27adb8c02368eb46247c342376b\\transformed\\core-1.9.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "96", "startColumns": "4", "startOffsets": "8108", "endColumns": "100", "endOffsets": "8204"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\23842c59899d74c6854e6a450173091d\\transformed\\material-1.8.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,352,432,514,611,700,796,920,1007,1073,1164,1234,1298,1401,1464,1529,1589,1657,1720,1775,1903,1960,2022,2077,2152,2292,2379,2462,2595,2677,2762,2849,2903,2958,3024,3097,3173,3262,3335,3411,3486,3556,3644,3719,3811,3903,3977,4051,4143,4196,4263,4346,4433,4495,4559,4622,4736,4843,4945,5056,5114,5173", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,80,79,81,96,88,95,123,86,65,90,69,63,102,62,64,59,67,62,54,127,56,61,54,74,139,86,82,132,81,84,86,53,54,65,72,75,88,72,75,74,69,87,74,91,91,73,73,91,52,66,82,86,61,63,62,113,106,101,110,57,58,84", "endOffsets": "266,347,427,509,606,695,791,915,1002,1068,1159,1229,1293,1396,1459,1524,1584,1652,1715,1770,1898,1955,2017,2072,2147,2287,2374,2457,2590,2672,2757,2844,2898,2953,3019,3092,3168,3257,3330,3406,3481,3551,3639,3714,3806,3898,3972,4046,4138,4191,4258,4341,4428,4490,4554,4617,4731,4838,4940,5051,5109,5168,5253"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3036,3117,3197,3279,3376,3465,3561,3685,3772,3838,3929,3999,4063,4166,4229,4294,4354,4422,4485,4540,4668,4725,4787,4842,4917,5057,5144,5227,5360,5442,5527,5614,5668,5723,5789,5862,5938,6027,6100,6176,6251,6321,6409,6484,6576,6668,6742,6816,6908,6961,7028,7111,7198,7260,7324,7387,7501,7608,7710,7821,7879,7938", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "endColumns": "12,80,79,81,96,88,95,123,86,65,90,69,63,102,62,64,59,67,62,54,127,56,61,54,74,139,86,82,132,81,84,86,53,54,65,72,75,88,72,75,74,69,87,74,91,91,73,73,91,52,66,82,86,61,63,62,113,106,101,110,57,58,84", "endOffsets": "316,3112,3192,3274,3371,3460,3556,3680,3767,3833,3924,3994,4058,4161,4224,4289,4349,4417,4480,4535,4663,4720,4782,4837,4912,5052,5139,5222,5355,5437,5522,5609,5663,5718,5784,5857,5933,6022,6095,6171,6246,6316,6404,6479,6571,6663,6737,6811,6903,6956,7023,7106,7193,7255,7319,7382,7496,7603,7705,7816,7874,7933,8018"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\94b9c9510706c6dc301febc3dfcf0a64\\transformed\\appcompat-1.6.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,425,511,619,738,822,903,994,1087,1183,1277,1377,1470,1565,1661,1752,1843,1930,2036,2142,2243,2350,2462,2566,2722,2820", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,84", "endOffsets": "208,312,420,506,614,733,817,898,989,1082,1178,1272,1372,1465,1560,1656,1747,1838,1925,2031,2137,2238,2345,2457,2561,2717,2815,2900"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "321,429,533,641,727,835,954,1038,1119,1210,1303,1399,1493,1593,1686,1781,1877,1968,2059,2146,2252,2358,2459,2566,2678,2782,2938,8023", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,84", "endOffsets": "424,528,636,722,830,949,1033,1114,1205,1298,1394,1488,1588,1681,1776,1872,1963,2054,2141,2247,2353,2454,2561,2673,2777,2933,3031,8103"}}]}]}