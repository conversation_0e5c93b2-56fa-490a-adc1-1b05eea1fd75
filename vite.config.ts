import path from 'path';
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  root: '.',
  base: '/',
  optimizeDeps: {
    exclude: ['better-sqlite3'], // Exclude native modules from browser bundling
    include: ['react', 'react-dom'] // Ensure React is properly bundled
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, '.'),
    }
  },
  server: {
    port: parseInt(process.env.VITE_PORT || '5176'),
    host: '0.0.0.0',
    cors: true,
    proxy: {
      '/api': {
        target: 'http://localhost:3001',
        changeOrigin: true,
        secure: false
      }
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      input: {
        main: path.resolve(__dirname, 'index.html')
      },
      external: [], // No external dependencies needed
      output: {
        // Ensure proper module format for browser
        format: 'es',
        manualChunks: undefined
      }
    }
  }
});
