# CRM SMS Gateway Android App - Complete Setup Guide

## Overview
This Android app integrates with your CRM system to send SMS messages automatically. It polls your CRM server for pending SMS messages and sends them through the Android device's SMS capability.

## Quick Start (Pre-built APK)

If you have a pre-built APK file:

1. **Enable Unknown Sources** on your Android device:
   - Go to Settings > Security > Unknown Sources (enable)
   - Or Settings > Apps > Special Access > Install Unknown Apps

2. **Install the APK**:
   - Transfer the APK file to your Android device
   - Open the APK file and install

3. **Configure the App**:
   - Open the CRM SMS Gateway app
   - Enter your CRM server URL: `http://[YOUR_PC_IP]:3001`
   - Enable Auto Mode
   - Test Connection

## Building from Source

### Prerequisites

1. **Install Node.js** (version 16 or higher):
   - Download from: https://nodejs.org/
   - Verify installation: `node --version`

2. **Install Android Studio**:
   - Download from: https://developer.android.com/studio
   - Install Android SDK (API level 23 or higher)
   - Set up environment variables:
     ```
     ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
     JAVA_HOME=C:\Program Files\Android\Android Studio\jre
     ```

3. **Install React Native CLI**:
   ```bash
   npm install -g react-native-cli
   ```

### Build Process

1. **Navigate to the app directory**:
   ```bash
   cd sms-android-app
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Build APK**:
   
   **Option A: Using the build script (Windows)**:
   ```bash
   build-apk.bat
   ```
   
   **Option B: Manual build**:
   ```bash
   # For debug APK (easier for testing)
   cd android
   gradlew assembleDebug
   
   # For release APK (optimized)
   gradlew assembleRelease
   ```

4. **Find your APK**:
   - Debug: `android/app/build/outputs/apk/debug/app-debug.apk`
   - Release: `android/app/build/outputs/apk/release/app-release.apk`

## CRM Backend Setup

Your CRM system needs to be updated to support the SMS gateway. The backend routes have been added automatically.

### API Endpoints Added

The following endpoints are now available in your CRM:

1. **Health Check**: `GET /api/sms/status`
2. **Pending Messages**: `GET /api/sms/pending`
3. **Status Updates**: `POST /api/sms/status`
4. **Manual Send**: `POST /api/sms/send`
5. **Statistics**: `GET /api/sms/stats`

### Testing Backend

Test if the SMS gateway is working:

1. **Open browser** and go to: `http://localhost:3001/api/sms/status`
2. **You should see**:
   ```json
   {
     "status": "online",
     "timestamp": "2024-01-01T12:00:00.000Z",
     "server": "CRM SMS Gateway",
     "version": "1.0.0",
     "database": "connected"
   }
   ```

## Android App Configuration

### 1. Find Your PC's IP Address

**Windows**:
```cmd
ipconfig
```
Look for "IPv4 Address" under your network adapter (usually starts with 192.168.x.x)

**Example**: If your IP is `*************`, your server URL will be:
```
http://*************:3001
```

### 2. Configure the App

1. **Open the CRM SMS Gateway app**
2. **Enter Server URL**: `http://[YOUR_PC_IP]:3001`
3. **API Key**: Leave empty (unless you've set up authentication)
4. **Enable Auto Mode**: Turn on the switch
5. **Save Config**: Tap "Save Config"
6. **Test Connection**: Tap "Test Connection"

### 3. Grant Permissions

The app will request the following permissions:
- **SMS**: Required to send messages
- **Phone**: Required to access phone features
- **Storage**: Required to save configuration

Grant all permissions for the app to work properly.

## Usage

### Automatic Mode (Recommended)

1. **Enable Auto Mode** in the app
2. **Keep the app open** (or running in background)
3. **Create SMS campaigns** in your CRM system
4. **Messages will be sent automatically** through the Android device

### Manual Mode

1. **Open the app**
2. **Go to "Send Manual SMS" section**
3. **Enter recipient phone number** (with country code, e.g., +1234567890)
4. **Type your message**
5. **Tap "Send SMS"**

## Troubleshooting

### Connection Issues

**Problem**: "Failed to connect to CRM server"

**Solutions**:
1. **Check network**: Ensure both PC and Android are on same WiFi
2. **Check IP address**: Verify PC's IP address hasn't changed
3. **Check CRM server**: Ensure CRM is running on port 3001
4. **Test in browser**: Open `http://[PC_IP]:3001` in phone's browser
5. **Check firewall**: Ensure Windows firewall allows port 3001

### SMS Not Sending

**Problem**: SMS messages not being sent

**Solutions**:
1. **Check permissions**: Ensure SMS permission is granted
2. **Check phone capability**: Ensure device can send SMS
3. **Check phone number format**: Use international format (+1234567890)
4. **Check SIM card**: Ensure SIM card is active and has SMS capability
5. **Check message content**: Ensure message is not empty

### App Stops Working

**Problem**: App stops working in background

**Solutions**:
1. **Disable battery optimization**:
   - Settings > Battery > Battery Optimization
   - Find "CRM SMS Gateway" and set to "Don't optimize"
2. **Allow background activity**:
   - Settings > Apps > CRM SMS Gateway > Battery
   - Enable "Allow background activity"
3. **Keep app in recent apps**: Don't swipe away the app

### Network Discovery

**Problem**: Can't find PC's IP address

**Solutions**:
1. **Use network scanner apps** on Android
2. **Check router admin panel** for connected devices
3. **Use command prompt** on PC: `ipconfig`
4. **Try common IP ranges**:
   - 192.168.1.x (most common)
   - 192.168.0.x
   - 10.0.0.x

## Security Notes

- **Local Network Only**: App only works within your local network
- **No Internet Required**: All communication is local
- **No Data Collection**: App doesn't send data outside your network
- **Secure Storage**: Configuration is stored securely on device

## Performance Tips

1. **Keep app in foreground** when sending many messages
2. **Ensure stable WiFi connection**
3. **Don't use device for other SMS activities** during bulk sending
4. **Monitor message history** for failed messages
5. **Restart app** if it becomes unresponsive

## Support

### Common Issues

1. **"Permission denied"**: Grant SMS permissions in Android settings
2. **"Network error"**: Check WiFi connection and server URL
3. **"Server not found"**: Verify CRM is running and IP address is correct
4. **"Messages not sending"**: Check SIM card and phone capability

### Getting Help

1. **Check this guide** for common solutions
2. **Test with manual SMS** to verify basic functionality
3. **Check CRM logs** for error messages
4. **Verify network connectivity** between devices

## Technical Specifications

- **Minimum Android**: 6.0 (API 23)
- **Target Android**: 13 (API 33)
- **Network**: WiFi or mobile data (same network as CRM)
- **Storage**: ~50MB
- **RAM**: ~100MB during operation
- **Battery**: Optimized for background operation

## Version Information

- **App Version**: 1.0.0
- **React Native**: 0.72.6
- **Target SDK**: 33
- **Minimum SDK**: 23
