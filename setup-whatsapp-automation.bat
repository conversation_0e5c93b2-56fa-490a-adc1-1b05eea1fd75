@echo off
REM WhatsApp Automation Setup Script for Windows
REM Installs and configures robotjs automation for CRM4CA

echo 🚀 Setting up WhatsApp Automation for CRM4CA...
echo ==================================================

REM Check if we're in the right directory
if not exist "package.json" (
    echo ❌ Error: Not in CRM project root directory
    echo Please run this script from E:\Projects\CRM-AIstudio
    pause
    exit /b 1
)

REM Install robotjs in main project
echo 📦 Installing robotjs in main project...
npm install robotjs

if %errorlevel% neq 0 (
    echo ⚠️  Standard installation failed, trying with --force...
    npm install --force robotjs
)

REM Install robotjs in backend
echo 📦 Installing robotjs in backend...
cd backend
npm install robotjs

if %errorlevel% neq 0 (
    echo ⚠️  Backend installation failed, trying with --force...
    npm install --force robotjs
)

cd ..

REM Check platform requirements
echo 🔍 Checking Windows requirements...
echo 🪟 Windows detected
echo ✅ No additional dependencies required

REM Check WhatsApp Desktop installation
echo 📱 Checking WhatsApp Desktop...

if exist "%LOCALAPPDATA%\WhatsApp\WhatsApp.exe" (
    echo ✅ WhatsApp Desktop found
) else if exist "%PROGRAMFILES%\WindowsApps\*WhatsApp*" (
    echo ✅ WhatsApp Desktop found (Microsoft Store version)
) else (
    echo ⚠️  WhatsApp Desktop not found. Please install from:
    echo    - Microsoft Store: https://www.microsoft.com/store/apps/whatsapp
    echo    - Official website: https://www.whatsapp.com/download
)

REM Create necessary directories
echo 📁 Creating automation directories...
if not exist "assets\whatsapp-templates" mkdir assets\whatsapp-templates
if not exist "assets\screenshots" mkdir assets\screenshots
if not exist "backend\data" mkdir backend\data
if not exist "backend\logs" mkdir backend\logs

echo ✅ Setup completed!
echo.
echo 🎯 Next Steps:
echo 1. Start the application: npm run dev
echo 2. Navigate to Settings → WhatsApp Automation
echo 3. Run system check to verify installation
echo 4. Test with a few messages before bulk campaigns
echo.
echo 📚 Documentation: DevDocs\WHATSAPP_AUTOMATION_IMPLEMENTATION_COMPLETE.md
echo.
echo 🎉 WhatsApp automation is ready to use!
echo.
pause
