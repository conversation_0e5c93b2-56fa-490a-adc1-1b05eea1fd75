import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Template } from '../types';
import { templateService } from '../services/TemplateService';
import { ArrowLeftIcon } from '../components/icons';

const ViewTemplatePage: React.FC = () => {
  const { templateId } = useParams<{ templateId: string }>();
  const navigate = useNavigate();
  const [template, setTemplate] = useState<Template | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadTemplate = async () => {
      if (!templateId) {
        setError('No template ID provided');
        setLoading(false);
        return;
      }

      try {
        const templateData = await templateService.getTemplateById(templateId);
        setTemplate(templateData);
      } catch (err) {
        console.error('Error loading template:', err);
        setError('Failed to load template details');
      } finally {
        setLoading(false);
      }
    };

    loadTemplate();
  }, [templateId]);

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not set';
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return dateString;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-textPrimary">Loading template details...</div>
      </div>
    );
  }

  if (error || !template) {
    return (
      <div className="min-h-screen bg-background p-6">
        <div className="max-w-4xl mx-auto">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h2 className="text-lg font-semibold text-red-800 mb-2">Error</h2>
            <p className="text-red-600">{error || 'Template not found'}</p>
            <button
              onClick={() => navigate('/templates')}
              className="mt-4 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
            >
              Back to Templates
            </button>
          </div>
        </div>
      </div>
    );
  }

  const fieldClass = "block text-sm font-medium text-textPrimary mb-1";
  const valueClass = "w-full p-3 border border-border rounded-lg bg-gray-50 text-textPrimary";

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-4xl mx-auto p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate('/templates')}
              className="flex items-center text-primary hover:text-opacity-80"
            >
              <ArrowLeftIcon className="h-5 w-5 mr-2" />
              Back to Templates
            </button>
            <h1 className="text-2xl font-bold text-textPrimary">View Template</h1>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => navigate(`/templates/edit/${template.id}`)}
              className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-opacity-90"
            >
              Edit Template
            </button>
          </div>
        </div>

        {/* Template Details */}
        <div className="bg-cardBackground border border-border rounded-lg shadow-sm">
          <div className="p-6">
            <h2 className="text-lg font-semibold text-textPrimary mb-6">Template Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Basic Information */}
              <div>
                <label className={fieldClass}>Template Name</label>
                <div className={valueClass}>{template.display_name}</div>
              </div>

              <div>
                <label className={fieldClass}>Internal Name</label>
                <div className={valueClass}>{template.template_name}</div>
              </div>

              <div>
                <label className={fieldClass}>Created By</label>
                <div className={valueClass}>{template.created_by || 'Not specified'}</div>
              </div>

              <div>
                <label className={fieldClass}>Created At</label>
                <div className={valueClass}>{formatDate(template.created_at)}</div>
              </div>

              <div>
                <label className={fieldClass}>Last Updated</label>
                <div className={valueClass}>{formatDate(template.updated_at)}</div>
              </div>

              <div>
                <label className={fieldClass}>Campaign Type</label>
                <div className={valueClass}>{template.campaign_type}</div>
              </div>

              <div>
                <label className={fieldClass}>Status</label>
                <div className={valueClass}>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    template.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  }`}>
                    {template.status}
                  </span>
                </div>
              </div>

              <div>
                <label className={fieldClass}>Active</label>
                <div className={valueClass}>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    template.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  }`}>
                    {template.is_active ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>

              <div>
                <label className={fieldClass}>Public</label>
                <div className={valueClass}>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    template.is_public ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  }`}>
                    {template.is_public ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>
            </div>

            {/* Content Sections */}
            {template.subject_template && (
              <div className="mt-6">
                <label className={fieldClass}>Email Subject Template</label>
                <div className={valueClass}>{template.subject_template}</div>
              </div>
            )}

            {template.email_content && (
              <div className="mt-6">
                <label className={fieldClass}>Email Content</label>
                <div className="w-full p-3 border border-border rounded-lg bg-gray-50 text-textPrimary min-h-[200px]">
                  <div dangerouslySetInnerHTML={{ __html: template.email_content }} />
                </div>
              </div>
            )}

            {template.whatsapp_content_template && (
              <div className="mt-6">
                <label className={fieldClass}>WhatsApp Content Template</label>
                <div className="w-full p-3 border border-border rounded-lg bg-gray-50 text-textPrimary min-h-[100px] whitespace-pre-wrap">
                  {template.whatsapp_content_template}
                </div>
              </div>
            )}

            {template.sms_content_template && (
              <div className="mt-6">
                <label className={fieldClass}>SMS Content Template</label>
                <div className="w-full p-3 border border-border rounded-lg bg-gray-50 text-textPrimary min-h-[100px] whitespace-pre-wrap">
                  {template.sms_content_template}
                </div>
              </div>
            )}

            {/* Description */}
            {template.description && (
              <div className="mt-6">
                <label className={fieldClass}>Description</label>
                <div className="w-full p-3 border border-border rounded-lg bg-gray-50 text-textPrimary min-h-[100px] whitespace-pre-wrap">
                  {template.description}
                </div>
              </div>
            )}

            {/* Areas of Interest */}
            {template.interest_area_id && (
              <div className="mt-6">
                <label className={fieldClass}>Interest Area ID</label>
                <div className={valueClass}>
                  <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                    {template.interest_area_id}
                  </span>
                </div>
              </div>
            )}

            {/* Target Segments */}
            {template.target_segments && template.target_segments.length > 0 && (
              <div className="mt-6">
                <label className={fieldClass}>Target Segments</label>
                <div className={valueClass}>
                  <div className="flex flex-wrap gap-2">
                    {template.target_segments.map((segment, index) => (
                      <span
                        key={index}
                        className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800"
                      >
                        {segment}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Template Settings */}
            <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className={fieldClass}>Version</label>
                <div className={valueClass}>{template.version || 1}</div>
              </div>
              <div>
                <label className={fieldClass}>Uses Placeholders</label>
                <div className={valueClass}>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    template.uses_placeholders ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  }`}>
                    {template.uses_placeholders ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>
              <div>
                <label className={fieldClass}>Requires Approval</label>
                <div className={valueClass}>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    template.requires_approval ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'
                  }`}>
                    {template.requires_approval ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewTemplatePage;
