import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Template } from '../types';
import { templateService } from '../services/TemplateService';
import { ArrowLeftIcon } from '../components/icons';

const ViewTemplatePage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [template, setTemplate] = useState<Template | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadTemplate = async () => {
      if (!id) {
        setError('No template ID provided');
        setLoading(false);
        return;
      }

      try {
        const templateData = await templateService.getTemplateById(id);
        setTemplate(templateData);
      } catch (err) {
        console.error('Error loading template:', err);
        setError('Failed to load template details');
      } finally {
        setLoading(false);
      }
    };

    loadTemplate();
  }, [id]);

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not set';
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return dateString;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-textPrimary">Loading template details...</div>
      </div>
    );
  }

  if (error || !template) {
    return (
      <div className="min-h-screen bg-background p-6">
        <div className="max-w-4xl mx-auto">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h2 className="text-lg font-semibold text-red-800 mb-2">Error</h2>
            <p className="text-red-600">{error || 'Template not found'}</p>
            <button
              onClick={() => navigate('/templates')}
              className="mt-4 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
            >
              Back to Templates
            </button>
          </div>
        </div>
      </div>
    );
  }

  const fieldClass = "block text-sm font-medium text-textPrimary mb-1";
  const valueClass = "w-full p-3 border border-border rounded-lg bg-gray-50 text-textPrimary";

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-4xl mx-auto p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate('/templates')}
              className="flex items-center text-primary hover:text-opacity-80"
            >
              <ArrowLeftIcon className="h-5 w-5 mr-2" />
              Back to Templates
            </button>
            <h1 className="text-2xl font-bold text-textPrimary">View Template</h1>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => navigate(`/templates/edit/${template.id}`)}
              className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-opacity-90"
            >
              Edit Template
            </button>
          </div>
        </div>

        {/* Template Details */}
        <div className="bg-cardBackground border border-border rounded-lg shadow-sm">
          <div className="p-6">
            <h2 className="text-lg font-semibold text-textPrimary mb-6">Template Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Basic Information */}
              <div>
                <label className={fieldClass}>Template Name</label>
                <div className={valueClass}>{template.display_name}</div>
              </div>

              <div>
                <label className={fieldClass}>Internal Name</label>
                <div className={valueClass}>{template.name}</div>
              </div>

              <div>
                <label className={fieldClass}>Created By</label>
                <div className={valueClass}>{template.created_by || 'Not specified'}</div>
              </div>

              <div>
                <label className={fieldClass}>Created At</label>
                <div className={valueClass}>{formatDate(template.created_at)}</div>
              </div>

              <div>
                <label className={fieldClass}>Last Updated</label>
                <div className={valueClass}>{formatDate(template.updated_at)}</div>
              </div>

              <div>
                <label className={fieldClass}>Total Subscribers</label>
                <div className={valueClass}>{template.total_subscribers || 0}</div>
              </div>

              {/* Channel Settings */}
              <div>
                <label className={fieldClass}>Email Enabled</label>
                <div className={valueClass}>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    template.email_enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  }`}>
                    {template.email_enabled ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>

              <div>
                <label className={fieldClass}>WhatsApp Enabled</label>
                <div className={valueClass}>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    template.whatsapp_enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  }`}>
                    {template.whatsapp_enabled ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>

              <div>
                <label className={fieldClass}>SMS Enabled</label>
                <div className={valueClass}>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    template.sms_enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  }`}>
                    {template.sms_enabled ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>
            </div>

            {/* Content Sections */}
            {template.email_subject && (
              <div className="mt-6">
                <label className={fieldClass}>Email Subject</label>
                <div className={valueClass}>{template.email_subject}</div>
              </div>
            )}

            {template.email_content && (
              <div className="mt-6">
                <label className={fieldClass}>Email Content</label>
                <div className="w-full p-3 border border-border rounded-lg bg-gray-50 text-textPrimary min-h-[200px]">
                  <div dangerouslySetInnerHTML={{ __html: template.email_content }} />
                </div>
              </div>
            )}

            {template.whatsapp_content && (
              <div className="mt-6">
                <label className={fieldClass}>WhatsApp Content</label>
                <div className="w-full p-3 border border-border rounded-lg bg-gray-50 text-textPrimary min-h-[100px] whitespace-pre-wrap">
                  {template.whatsapp_content}
                </div>
              </div>
            )}

            {template.sms_content && (
              <div className="mt-6">
                <label className={fieldClass}>SMS Content</label>
                <div className="w-full p-3 border border-border rounded-lg bg-gray-50 text-textPrimary min-h-[100px] whitespace-pre-wrap">
                  {template.sms_content}
                </div>
              </div>
            )}

            {/* Description */}
            {template.description && (
              <div className="mt-6">
                <label className={fieldClass}>Description</label>
                <div className="w-full p-3 border border-border rounded-lg bg-gray-50 text-textPrimary min-h-[100px] whitespace-pre-wrap">
                  {template.description}
                </div>
              </div>
            )}

            {/* Areas of Interest */}
            {template.areasOfInterestIds && template.areasOfInterestIds.length > 0 && (
              <div className="mt-6">
                <label className={fieldClass}>Target Areas of Interest</label>
                <div className={valueClass}>
                  <div className="flex flex-wrap gap-2">
                    {template.areasOfInterestIds.map((areaId, index) => (
                      <span
                        key={index}
                        className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800"
                      >
                        Area ID: {areaId}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Recipient Counts */}
            <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className={fieldClass}>Email Recipients</label>
                <div className={valueClass}>{template.email_recipients || 0}</div>
              </div>
              <div>
                <label className={fieldClass}>WhatsApp Recipients</label>
                <div className={valueClass}>{template.whatsapp_recipients || 0}</div>
              </div>
              <div>
                <label className={fieldClass}>SMS Recipients</label>
                <div className={valueClass}>{template.sms_recipients || 0}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewTemplatePage;
