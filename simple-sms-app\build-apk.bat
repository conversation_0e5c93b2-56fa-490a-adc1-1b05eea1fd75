@echo off
echo ========================================
echo CRM SMS Gateway - Simple Android App Builder
echo ========================================
echo.

echo Attempting to build APK...
echo.

REM Try to build using gradlew
echo Using Gradle Wrapper to build...
call gradlew.bat assembleDebug

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo SUCCESS: APK built successfully!
    echo ========================================
    echo.
    echo APK Location: app\build\outputs\apk\debug\app-debug.apk
    echo.
    echo Installation Instructions:
    echo 1. Transfer the APK to your Android device
    echo 2. Enable "Install from Unknown Sources" in Android settings
    echo 3. Install the APK
    echo 4. Open the app and enter your CRM server IP
    echo 5. Test connection and enable Auto Mode
    echo.
    echo The app will automatically:
    echo - Check for pending SMS every 5 seconds
    echo - Send SMS via your Android device
    echo - Report status back to your CRM
    echo.
) else (
    echo.
    echo ========================================
    echo BUILD FAILED - Using Android Studio Instead
    echo ========================================
    echo.
    echo The build failed, but don't worry! Here's the easy solution:
    echo.
    echo RECOMMENDED: Use Android Studio (Much Easier)
    echo 1. Download Android Studio from: https://developer.android.com/studio
    echo 2. Install Android Studio
    echo 3. Open Android Studio
    echo 4. Click "Open an existing project"
    echo 5. Select this folder: %CD%
    echo 6. Wait for project to load and sync
    echo 7. Connect your Android device via USB
    echo 8. Click the green "Run" button (triangle icon)
    echo 9. App will install directly to your device!
    echo.
    echo This is actually easier than building an APK file!
    echo.
)

pause
