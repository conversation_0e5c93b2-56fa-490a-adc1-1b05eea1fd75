@echo off
echo ========================================
echo CRM SMS Gateway - Simple Android App Builder
echo ========================================
echo.

REM Check if Android SDK is available
if not exist "%ANDROID_HOME%" (
    echo ERROR: Android SDK not found
    echo.
    echo Please install Android Studio and set ANDROID_HOME environment variable
    echo Example: set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
    echo.
    echo Alternative: Use Android Studio to build the project
    echo 1. Open Android Studio
    echo 2. Open this project folder
    echo 3. Build -> Generate Signed Bundle/APK
    echo 4. Choose APK and follow the wizard
    echo.
    pause
    exit /b 1
)

echo Building APK...
echo.

REM Build debug APK
call gradlew assembleDebug

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo SUCCESS: APK built successfully!
    echo ========================================
    echo.
    echo APK Location: app\build\outputs\apk\debug\app-debug.apk
    echo.
    echo Installation Instructions:
    echo 1. Transfer the APK to your Android device
    echo 2. Enable "Install from Unknown Sources" in Android settings
    echo 3. Install the APK
    echo 4. Open the app and enter your CRM server IP
    echo 5. Test connection and enable Auto Mode
    echo.
    echo The app will automatically:
    echo - Check for pending SMS every 5 seconds
    echo - Send SMS via your Android device
    echo - Report status back to your CRM
    echo.
) else (
    echo.
    echo ========================================
    echo BUILD FAILED
    echo ========================================
    echo.
    echo Please try building with Android Studio instead:
    echo 1. Install Android Studio
    echo 2. Open this project folder
    echo 3. Build -> Generate Signed Bundle/APK
    echo 4. Choose APK and follow the wizard
    echo.
)

pause
