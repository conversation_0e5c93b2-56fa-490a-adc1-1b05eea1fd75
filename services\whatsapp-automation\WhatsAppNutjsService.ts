/**
 * WhatsApp Desktop Automation Service using nut.js
 * Provides automated WhatsApp Desktop interaction for bulk messaging
 * 
 * @fileoverview Enhanced WhatsApp automation with nut.js for professional CA CRM
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import * as robot from 'robotjs';
import { promises as fs } from 'fs';
import path from 'path';

// Configure robotjs
robot.setKeyboardDelay(10);
robot.setMouseDelay(10);

export interface WhatsAppAutomationConfig {
  messageDelay: number; // Delay between messages (ms)
  searchDelay: number; // Delay for contact search (ms)
  typeDelay: number; // Delay between keystrokes (ms)
  screenshotEnabled: boolean; // For debugging
  retryAttempts: number; // Retry attempts for failed operations
  safeMode: boolean; // Extra validation and delays
}

export interface ContactMessage {
  phone: string;
  message: string;
  name?: string;
}

export interface AutomationResult {
  success: boolean;
  messageId?: string;
  error?: string;
  timestamp: Date;
  screenshotPath?: string;
}

export interface BulkAutomationResult {
  total: number;
  successful: number;
  failed: number;
  results: Array<{
    contact: ContactMessage;
    result: AutomationResult;
  }>;
  duration: number;
  avgTimePerMessage: number;
}

/**
 * WhatsApp Desktop Automation Service
 * Provides automated bulk messaging through WhatsApp Desktop application
 */
export class WhatsAppNutjsService {
  private config: WhatsAppAutomationConfig;
  private isRunning: boolean = false;
  private currentSession: string | null = null;
  private progressCallback?: (current: number, total: number, contact?: ContactMessage) => void;

  constructor(config: Partial<WhatsAppAutomationConfig> = {}) {
    this.config = {
      messageDelay: 3000, // 3 seconds between messages
      searchDelay: 2000, // 2 seconds for search
      typeDelay: 50, // 50ms between keystrokes
      screenshotEnabled: true,
      retryAttempts: 3,
      safeMode: true,
      ...config
    };
  }

  /**
   * Initialize the automation service
   * Prepares screen templates and validates WhatsApp Desktop availability
   */
  async initialize(): Promise<boolean> {
    try {
      console.log('🚀 Initializing WhatsApp nut.js automation service...');
      
      // Create assets directory if not exists
      await this.ensureAssetsDirectory();
      
      // Validate screen accessibility
      await this.validateScreenAccess();
      
      // Check WhatsApp Desktop availability
      const isWhatsAppAvailable = await this.checkWhatsAppDesktop();
      
      if (!isWhatsAppAvailable) {
        throw new Error('WhatsApp Desktop not found. Please ensure it is installed and running.');
      }

      console.log('✅ WhatsApp nut.js automation service initialized successfully');
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize WhatsApp automation:', error);
      return false;
    }
  }

  /**
   * Send a single message to a contact
   */
  async sendSingleMessage(contact: ContactMessage): Promise<AutomationResult> {
    const startTime = Date.now();
    let screenshotPath: string | undefined;

    try {
      if (this.config.screenshotEnabled) {
        screenshotPath = await this.takeScreenshot('before-send');
      }

      // Focus WhatsApp Desktop
      await this.focusWhatsAppDesktop();
      
      // Search for contact
      await this.searchContact(contact.phone, contact.name);
      
      // Type and send message
      await this.typeMessage(contact.message);
      await this.sendMessage();

      // Wait for message to be sent
      await this.waitForMessageSent();

      if (this.config.screenshotEnabled) {
        await this.takeScreenshot('after-send');
      }

      return {
        success: true,
        messageId: this.generateMessageId(),
        timestamp: new Date(),
        screenshotPath
      };

    } catch (error) {
      console.error('❌ Failed to send message:', error);
      
      if (this.config.screenshotEnabled) {
        screenshotPath = await this.takeScreenshot('error');
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date(),
        screenshotPath
      };
    }
  }

  /**
   * Send bulk messages with progress tracking
   */
  async sendBulkMessages(
    contacts: ContactMessage[],
    progressCallback?: (current: number, total: number, contact?: ContactMessage) => void
  ): Promise<BulkAutomationResult> {
    const startTime = Date.now();
    this.progressCallback = progressCallback;
    this.isRunning = true;
    this.currentSession = this.generateSessionId();

    const results: BulkAutomationResult['results'] = [];
    let successful = 0;
    let failed = 0;

    console.log(`🚀 Starting bulk WhatsApp automation for ${contacts.length} messages`);

    try {
      // Initialize before bulk sending
      const initialized = await this.initialize();
      if (!initialized) {
        throw new Error('Failed to initialize automation service');
      }

      for (let i = 0; i < contacts.length && this.isRunning; i++) {
        const contact = contacts[i];
        
        // Progress callback
        if (this.progressCallback) {
          this.progressCallback(i + 1, contacts.length, contact);
        }

        console.log(`📱 Sending message ${i + 1}/${contacts.length} to ${contact.phone}`);

        // Send message with retry logic
        let result: AutomationResult;
        let attempts = 0;
        
        do {
          attempts++;
          result = await this.sendSingleMessage(contact);
          
          if (!result.success && attempts < this.config.retryAttempts) {
            console.log(`⚠️ Retry attempt ${attempts} for ${contact.phone}`);
            await this.delay(this.config.messageDelay * 2); // Double delay on retry
          }
        } while (!result.success && attempts < this.config.retryAttempts);

        results.push({ contact, result });
        
        if (result.success) {
          successful++;
          console.log(`✅ Message sent successfully to ${contact.phone}`);
        } else {
          failed++;
          console.log(`❌ Failed to send message to ${contact.phone}: ${result.error}`);
        }

        // Delay between messages (except for last message)
        if (i < contacts.length - 1 && this.isRunning) {
          await this.delay(this.config.messageDelay);
        }
      }

    } catch (error) {
      console.error('❌ Bulk automation failed:', error);
      // Mark remaining messages as failed
      for (let i = results.length; i < contacts.length; i++) {
        results.push({
          contact: contacts[i],
          result: {
            success: false,
            error: 'Bulk automation stopped due to error',
            timestamp: new Date()
          }
        });
        failed++;
      }
    } finally {
      this.isRunning = false;
      this.currentSession = null;
    }

    const duration = Date.now() - startTime;
    const avgTimePerMessage = duration / contacts.length;

    console.log(`📊 Bulk automation completed: ${successful} successful, ${failed} failed`);

    return {
      total: contacts.length,
      successful,
      failed,
      results,
      duration,
      avgTimePerMessage
    };
  }

  /**
   * Stop the current automation session
   */
  async stopAutomation(): Promise<void> {
    this.isRunning = false;
    console.log('🛑 WhatsApp automation stopped by user');
  }

  /**
   * Check if automation is currently running
   */
  isAutomationRunning(): boolean {
    return this.isRunning;
  }

  /**
   * Get current automation session details
   */
  getCurrentSession(): string | null {
    return this.currentSession;
  }

  // Private methods for automation implementation

  /**
   * Ensure assets directory exists for templates and screenshots
   */
  private async ensureAssetsDirectory(): Promise<void> {
    const assetsDir = path.join(process.cwd(), 'assets', 'whatsapp-templates');
    const screenshotsDir = path.join(process.cwd(), 'assets', 'screenshots');
    
    try {
      await fs.mkdir(assetsDir, { recursive: true });
      await fs.mkdir(screenshotsDir, { recursive: true });
    } catch (error) {
      console.error('Failed to create assets directories:', error);
    }
  }

  /**
   * Validate screen access and permissions
   */
  private async validateScreenAccess(): Promise<void> {
    try {
      const screenSize = robot.getScreenSize();
      console.log(`📱 Screen resolution: ${screenSize.width}x${screenSize.height}`);
      
      if (screenSize.width < 1024 || screenSize.height < 768) {
        console.warn('⚠️ Low screen resolution detected. Automation may be affected.');
      }
    } catch (error) {
      throw new Error('Failed to access screen. Please check permissions.');
    }
  }

  /**
   * Check if WhatsApp Desktop is available and running
   */
  private async checkWhatsAppDesktop(): Promise<boolean> {
    try {
      // Try to find WhatsApp window by looking for specific UI elements
      // This is a basic implementation - you might need to adjust based on your OS
      
      if (process.platform === 'win32') {
        // Windows implementation
        return await this.checkWhatsAppWindows();
      } else if (process.platform === 'darwin') {
        // macOS implementation
        return await this.checkWhatsAppMacOS();
      } else {
        // Linux implementation
        return await this.checkWhatsAppLinux();
      }
    } catch (error) {
      console.error('Error checking WhatsApp Desktop:', error);
      return false;
    }
  }

  /**
   * Windows-specific WhatsApp Desktop detection
   */
  private async checkWhatsAppWindows(): Promise<boolean> {
    try {
      // Look for WhatsApp window title patterns
      // This is a simplified implementation - actual window detection would require more sophisticated methods
      console.log('🔍 Checking for WhatsApp Desktop on Windows...');
      return true; // Placeholder - implement actual window detection
    } catch (error) {
      return false;
    }
  }

  /**
   * macOS-specific WhatsApp Desktop detection
   */
  private async checkWhatsAppMacOS(): Promise<boolean> {
    try {
      console.log('🔍 Checking for WhatsApp Desktop on macOS...');
      return true; // Placeholder - implement actual application detection
    } catch (error) {
      return false;
    }
  }

  /**
   * Linux-specific WhatsApp Desktop detection
   */
  private async checkWhatsAppLinux(): Promise<boolean> {
    try {
      console.log('🔍 Checking for WhatsApp Desktop on Linux...');
      return true; // Placeholder - implement actual application detection
    } catch (error) {
      return false;
    }
  }

  /**
   * Focus WhatsApp Desktop window
   */
  private async focusWhatsAppDesktop(): Promise<void> {
    try {
      // Platform-specific implementation to focus WhatsApp Desktop
      if (process.platform === 'win32') {
        // Windows: Alt+Tab to WhatsApp
        robot.keyTap('tab', ['alt']);
      } else if (process.platform === 'darwin') {
        // macOS: Cmd+Tab to WhatsApp
        robot.keyTap('tab', ['command']);
      } else {
        // Linux: Alt+Tab
        robot.keyTap('tab', ['alt']);
      }
      
      await this.delay(1000); // Wait for window to focus
    } catch (error) {
      throw new Error('Failed to focus WhatsApp Desktop window');
    }
  }

  /**
   * Search for a contact in WhatsApp
   */
  private async searchContact(phone: string, name?: string): Promise<void> {
    try {
      // Click on search field (usually Ctrl+F or clicking search icon)
      robot.keyTap('f', ['control']);
      await this.delay(500);

      // Clear search field
      robot.keyTap('a', ['control']);
      await this.delay(100);

      // Type contact search (prefer name if available, otherwise phone)
      const searchTerm = name || phone;
      robot.typeString(searchTerm);
      await this.delay(this.config.searchDelay);

      // Press Enter to select first result
      robot.keyTap('enter');
      await this.delay(1000);

    } catch (error) {
      throw new Error(`Failed to search for contact: ${phone}`);
    }
  }

  /**
   * Type a message in the chat input field
   */
  private async typeMessage(message: string): Promise<void> {
    try {
      // Click in message input area (usually at bottom of screen)
      // This is a basic implementation - you might need to find the exact coordinates
      
      // Clear any existing text
      robot.keyTap('a', ['control']);
      await this.delay(100);

      // Type the message with configured delay
      if (this.config.safeMode) {
        for (const char of message) {
          robot.typeString(char);
          await this.delay(this.config.typeDelay);
        }
      } else {
        robot.typeString(message);
      }
      
      await this.delay(500);
    } catch (error) {
      throw new Error('Failed to type message');
    }
  }

  /**
   * Send the typed message
   */
  private async sendMessage(): Promise<void> {
    try {
      // Press Enter to send message
      robot.keyTap('enter');
      await this.delay(1000);
    } catch (error) {
      throw new Error('Failed to send message');
    }
  }

  /**
   * Wait for message to be sent (check for delivery indicators)
   */
  private async waitForMessageSent(): Promise<void> {
    try {
      // Wait for message delivery indicators
      // This could involve checking for checkmarks or other visual indicators
      await this.delay(2000);
      
      // TODO: Implement actual delivery confirmation detection
      // This would involve image recognition of WhatsApp's delivery indicators
      
    } catch (error) {
      console.warn('Could not confirm message delivery status');
    }
  }

  /**
   * Take a screenshot for debugging/logging purposes
   */
  private async takeScreenshot(context: string): Promise<string> {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `whatsapp-${context}-${timestamp}.png`;
      const filepath = path.join(process.cwd(), 'assets', 'screenshots', filename);
      
      const img = robot.screen.capture();
      await fs.writeFile(filepath, img.image);
      return filepath;
    } catch (error) {
      console.error('Failed to take screenshot:', error);
      return '';
    }
  }

  /**
   * Generate a unique message ID for tracking
   */
  private generateMessageId(): string {
    return `wa_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate a unique session ID for bulk operations
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Utility delay function
   */
  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Update automation configuration
   */
  updateConfig(newConfig: Partial<WhatsAppAutomationConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Get current automation configuration
   */
  getConfig(): WhatsAppAutomationConfig {
    return { ...this.config };
  }

  /**
   * Validate phone number format
   */
  private validatePhoneNumber(phone: string): boolean {
    // Basic phone number validation
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    return phoneRegex.test(phone.replace(/\s+/g, ''));
  }

  /**
   * Format phone number for search
   */
  private formatPhoneForSearch(phone: string): string {
    // Remove all non-digit characters except +
    return phone.replace(/[^\d+]/g, '');
  }

  /**
   * Get automation statistics
   */
  async getAutomationStats(): Promise<{
    totalSessions: number;
    totalMessagesSent: number;
    successRate: number;
    avgTimePerMessage: number;
  }> {
    // This would typically read from a log file or database
    // Placeholder implementation
    return {
      totalSessions: 0,
      totalMessagesSent: 0,
      successRate: 0,
      avgTimePerMessage: 0
    };
  }
}

export default WhatsAppNutjsService;
