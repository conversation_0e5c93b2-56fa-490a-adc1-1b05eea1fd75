/**
 * WhatsApp Web Automation Service
 * Alternative automation solution using WhatsApp Web instead of Desktop
 * Uses Puppeteer for browser automation - no native dependencies required
 * 
 * @fileoverview Pure JavaScript WhatsApp automation without robotjs
 * <AUTHOR> Development Team
 * @version 2.0.0 - Web-based automation
 */

import puppeteer from 'puppeteer';
import path from 'path';
import { promises as fs } from 'fs';

export class WhatsAppWebAutomationService {
  constructor() {
    this.browser = null;
    this.page = null;
    this.isInitialized = false;
    this.sessionData = null;
  }

  /**
   * Initialize WhatsApp Web automation
   */
  async initialize() {
    try {
      console.log('🚀 Initializing WhatsApp Web automation...');
      
      // Launch browser with WhatsApp Web
      this.browser = await puppeteer.launch({
        headless: false, // Keep visible for QR code scanning
        defaultViewport: null,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      });

      this.page = await this.browser.newPage();
      
      // Set user agent to avoid detection
      await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
      
      // Navigate to WhatsApp Web
      await this.page.goto('https://web.whatsapp.com', { waitUntil: 'networkidle2' });
      
      console.log('✅ WhatsApp Web loaded - waiting for login...');
      
      // Wait for either QR code or chat interface
      await this.waitForLogin();
      
      this.isInitialized = true;
      console.log('✅ WhatsApp Web automation initialized successfully');
      
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize WhatsApp Web automation:', error);
      await this.cleanup();
      throw error;
    }
  }

  /**
   * Wait for user to scan QR code and login
   */
  async waitForLogin() {
    try {
      console.log('⏳ Waiting for WhatsApp Web login...');
      
      // Wait for either QR code or main interface
      await this.page.waitForSelector('canvas[aria-label="Scan me!"], [data-testid="chat-list"]', { 
        timeout: 60000 
      });
      
      // Check if QR code is present
      const qrCode = await this.page.$('canvas[aria-label="Scan me!"]');
      if (qrCode) {
        console.log('📱 QR Code detected - please scan with your phone');
        
        // Wait for login to complete
        await this.page.waitForSelector('[data-testid="chat-list"]', { 
          timeout: 120000 // 2 minutes to scan QR code
        });
      }
      
      console.log('✅ WhatsApp Web login successful');
      
      // Wait a bit more for full initialization
      await this.page.waitForTimeout(3000);
      
    } catch (error) {
      throw new Error(`Login timeout: ${error.message}`);
    }
  }

  /**
   * Send a message to a contact
   */
  async sendMessage(phoneNumber, message) {
    try {
      if (!this.isInitialized) {
        throw new Error('WhatsApp Web automation not initialized');
      }

      console.log(`📱 Sending message to ${phoneNumber}`);
      
      // Format phone number for WhatsApp Web URL
      const formattedPhone = phoneNumber.replace(/[^\d]/g, '');
      const whatsappUrl = `https://web.whatsapp.com/send?phone=${formattedPhone}&text=${encodeURIComponent(message)}`;
      
      // Navigate to the chat
      await this.page.goto(whatsappUrl, { waitUntil: 'networkidle2' });
      
      // Wait for chat to load
      await this.page.waitForSelector('[data-testid="conversation-compose-box-input"]', { 
        timeout: 10000 
      });
      
      // Wait a bit for full load
      await this.page.waitForTimeout(2000);
      
      // Click the send button
      const sendButton = await this.page.$('[data-testid="send"]');
      if (sendButton) {
        await sendButton.click();
        console.log(`✅ Message sent to ${phoneNumber}`);
        
        // Wait for message to be sent
        await this.page.waitForTimeout(2000);
        
        return {
          success: true,
          timestamp: new Date(),
          phone: phoneNumber,
          message: message
        };
      } else {
        throw new Error('Send button not found');
      }
      
    } catch (error) {
      console.error(`❌ Failed to send message to ${phoneNumber}:`, error);
      return {
        success: false,
        error: error.message,
        timestamp: new Date(),
        phone: phoneNumber,
        message: message
      };
    }
  }

  /**
   * Send bulk messages with progress tracking
   */
  async sendBulkMessages(contacts, progressCallback) {
    const results = [];
    let successful = 0;
    let failed = 0;

    console.log(`🚀 Starting bulk messaging for ${contacts.length} contacts`);

    for (let i = 0; i < contacts.length; i++) {
      const contact = contacts[i];
      
      // Update progress
      if (progressCallback) {
        progressCallback(i + 1, contacts.length, contact);
      }

      console.log(`📱 Processing ${i + 1}/${contacts.length}: ${contact.phone}`);

      // Send message
      const result = await this.sendMessage(contact.phone, contact.message);
      results.push(result);

      if (result.success) {
        successful++;
      } else {
        failed++;
      }

      // Delay between messages to avoid rate limiting
      if (i < contacts.length - 1) {
        console.log('⏳ Waiting 5 seconds before next message...');
        await this.page.waitForTimeout(5000);
      }
    }

    console.log(`🏁 Bulk messaging completed: ${successful} successful, ${failed} failed`);

    return {
      total: contacts.length,
      successful,
      failed,
      results,
      sessionId: `web_${Date.now()}`
    };
  }

  /**
   * Check if WhatsApp Web is ready
   */
  async isReady() {
    try {
      if (!this.page) return false;
      
      // Check if chat list is visible
      const chatList = await this.page.$('[data-testid="chat-list"]');
      return !!chatList;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get current status
   */
  async getStatus() {
    return {
      initialized: this.isInitialized,
      ready: await this.isReady(),
      browserOpen: !!this.browser
    };
  }

  /**
   * Cleanup resources
   */
  async cleanup() {
    try {
      if (this.page) {
        await this.page.close();
        this.page = null;
      }
      
      if (this.browser) {
        await this.browser.close();
        this.browser = null;
      }
      
      this.isInitialized = false;
      console.log('✅ WhatsApp Web automation cleaned up');
    } catch (error) {
      console.error('❌ Error during cleanup:', error);
    }
  }

  /**
   * Take screenshot for debugging
   */
  async takeScreenshot(filename = 'whatsapp-debug.png') {
    try {
      if (this.page) {
        const screenshotPath = path.join(process.cwd(), 'logs', filename);
        await this.page.screenshot({ path: screenshotPath, fullPage: true });
        console.log(`📸 Screenshot saved: ${screenshotPath}`);
        return screenshotPath;
      }
    } catch (error) {
      console.error('❌ Failed to take screenshot:', error);
    }
    return null;
  }
}

// Export singleton instance
export const whatsappWebAutomation = new WhatsAppWebAutomationService();
export default whatsappWebAutomation;
