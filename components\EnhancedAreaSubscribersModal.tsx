import React, { useState, useEffect, useMemo } from 'react';
import { AreaOfInterest, SubscriberProfile, SubscriberProfileStatus } from '../types';
import { subscriberService } from '../services/SubscriberService-API';
import { XMarkIcon } from './icons';

interface EnhancedAreaSubscribersModalProps {
  isOpen: boolean;
  area: AreaOfInterest;
  onClose: () => void;
  onSubscriberAdded?: (subscriber: SubscriberProfile) => void;
  onSubscriberRemoved?: (subscriber: SubscriberProfile) => void;
}

const EnhancedAreaSubscribersModal: React.FC<EnhancedAreaSubscribersModalProps> = ({
  isOpen,
  area,
  onClose,
  onSubscriberAdded,
  onSubscriberRemoved
}) => {
  const [allSubscribers, setAllSubscribers] = useState<SubscriberProfile[]>([]);
  const [currentSubscribers, setCurrentSubscribers] = useState<SubscriberProfile[]>([]);
  const [availableSubscribers, setAvailableSubscribers] = useState<SubscriberProfile[]>([]);
  const [loading, setLoading] = useState(true);
  
  // Single common filtering state
  const [searchFilter, setSearchFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  useEffect(() => {
    if (!isOpen || !area) return;

    const loadSubscribers = async () => {
      setLoading(true);
      try {
        const subscribers = await subscriberService.getAllSubscribers();
        const activeSubscribers = subscribers.filter(s => s.status === SubscriberProfileStatus.ACTIVE);
        setAllSubscribers(activeSubscribers);

        // Split subscribers into current and available
        const current = activeSubscribers.filter(subscriber => {
          const areasArray = subscriber.areasOfInterestIds || [];
          return Array.isArray(areasArray) && areasArray.includes(area.id);
        });

        const available = activeSubscribers.filter(subscriber => {
          const areasArray = subscriber.areasOfInterestIds || [];
          return !Array.isArray(areasArray) || !areasArray.includes(area.id);
        });

        setCurrentSubscribers(current);
        setAvailableSubscribers(available);
      } catch (error) {
        console.error('Error loading subscribers for area:', error);
      } finally {
        setLoading(false);
      }
    };

    loadSubscribers();
  }, [isOpen, area]);

  // Filter function using common filters
  const filterSubscribers = (subscribers: SubscriberProfile[]) => {
    return subscribers.filter(subscriber => {
      // Search filter
      const matchesSearch = !searchFilter.trim() ||
        subscriber.email.toLowerCase().includes(searchFilter.toLowerCase()) ||
        `${subscriber.firstName} ${subscriber.lastName}`.toLowerCase().includes(searchFilter.toLowerCase()) ||
        subscriber.organization?.toLowerCase().includes(searchFilter.toLowerCase());

      // Status filter
      const matchesStatus = statusFilter === 'all' || subscriber.status === statusFilter;

      return matchesSearch && matchesStatus;
    });
  };

  const filteredCurrentSubscribers = useMemo(() =>
    filterSubscribers(currentSubscribers),
    [currentSubscribers, searchFilter, statusFilter]
  );

  const filteredAvailableSubscribers = useMemo(() =>
    filterSubscribers(availableSubscribers),
    [availableSubscribers, searchFilter, statusFilter]
  );

  const handleAddSubscriber = async (subscriber: SubscriberProfile) => {
    try {
      // Add the area to the subscriber's areas of interest
      const currentAreas = subscriber.areasOfInterestIds || [];
      const updatedAreas = [...currentAreas, area.id];

      const updatedSubscriber = {
        ...subscriber,
        areasOfInterestIds: updatedAreas
      };

      await subscriberService.updateSubscriber(subscriber.id, updatedSubscriber);

      // Update local state
      setCurrentSubscribers(prev => [...prev, subscriber]);
      setAvailableSubscribers(prev => prev.filter(s => s.id !== subscriber.id));

      // Notify parent component
      onSubscriberAdded?.(subscriber);
    } catch (error) {
      console.error('Error adding subscriber to area:', error);
    }
  };

  const handleRemoveSubscriber = async (subscriber: SubscriberProfile) => {
    try {
      // Remove the area from the subscriber's areas of interest
      const currentAreas = subscriber.areasOfInterestIds || [];
      const updatedAreas = currentAreas.filter(areaId => areaId !== area.id);

      const updatedSubscriber = {
        ...subscriber,
        areasOfInterestIds: updatedAreas
      };

      await subscriberService.updateSubscriber(subscriber.id, updatedSubscriber);

      // Update local state
      setCurrentSubscribers(prev => prev.filter(s => s.id !== subscriber.id));
      setAvailableSubscribers(prev => [...prev, subscriber]);

      // Notify parent component
      onSubscriberRemoved?.(subscriber);
    } catch (error) {
      console.error('Error removing subscriber from area:', error);
    }
  };

  const handleClose = () => {
    // Reset filters when closing
    setSearchFilter('');
    setStatusFilter('all');
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-6xl w-full mx-4 max-h-[85vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-xl font-semibold text-gray-900">
            Manage Subscribers for "{area.name}"
          </h3>
          <button
            type="button"
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 text-2xl"
            aria-label="Close modal"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* Common Filters */}
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Filter Subscribers</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <input
              type="text"
              placeholder="Search by name, email, or organization..."
              value={searchFilter}
              onChange={(e) => setSearchFilter(e.target.value)}
              className="px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              aria-label="Filter subscribers by status"
            >
              <option value="all">All Statuses</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="unsubscribed">Unsubscribed</option>
            </select>
          </div>
          <p className="text-xs text-gray-600 mt-2">
            This filter applies to both Current and Available subscriber lists below.
          </p>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-12">
            <div className="text-gray-600">Loading subscribers...</div>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 flex-1 overflow-hidden">
            {/* Current Subscribers */}
            <div className="flex flex-col">
              <h4 className="font-medium text-gray-900 mb-3">
                Current Subscribers ({filteredCurrentSubscribers.length} of {currentSubscribers.length})
              </h4>
              
              <div className="border rounded-lg flex-1 overflow-y-auto">
                {filteredCurrentSubscribers.length > 0 ? (
                  filteredCurrentSubscribers.map(subscriber => (
                    <div key={subscriber.id} className="flex items-center justify-between p-3 border-b last:border-b-0">
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-sm truncate">{subscriber.email}</div>
                        <div className="text-xs text-gray-500 truncate">
                          {subscriber.firstName} {subscriber.lastName}
                          {subscriber.organization && ` • ${subscriber.organization}`}
                        </div>
                        <div className="text-xs mt-1">
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            subscriber.status === 'active' ? 'bg-green-100 text-green-800' :
                            subscriber.status === 'unsubscribed' ? 'bg-red-100 text-red-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {subscriber.status}
                          </span>
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={() => handleRemoveSubscriber(subscriber)}
                        className="text-red-600 hover:text-red-800 text-sm px-3 py-1 rounded hover:bg-red-50 ml-2"
                      >
                        Remove
                      </button>
                    </div>
                  ))
                ) : (
                  <div className="p-4 text-center text-gray-500 text-sm">
                    {currentSubscribers.length === 0 
                      ? "No subscribers assigned to this area"
                      : "No subscribers match the current filters"
                    }
                  </div>
                )}
              </div>
            </div>

            {/* Available Subscribers */}
            <div className="flex flex-col">
              <h4 className="font-medium text-gray-900 mb-3">
                Available Subscribers ({filteredAvailableSubscribers.length} of {availableSubscribers.length})
              </h4>
              
              <div className="border rounded-lg flex-1 overflow-y-auto">
                {filteredAvailableSubscribers.length > 0 ? (
                  filteredAvailableSubscribers.map(subscriber => (
                    <div key={subscriber.id} className="flex items-center justify-between p-3 border-b last:border-b-0">
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-sm truncate">{subscriber.email}</div>
                        <div className="text-xs text-gray-500 truncate">
                          {subscriber.firstName} {subscriber.lastName}
                          {subscriber.organization && ` • ${subscriber.organization}`}
                        </div>
                        <div className="text-xs mt-1">
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            subscriber.status === 'active' ? 'bg-green-100 text-green-800' :
                            subscriber.status === 'unsubscribed' ? 'bg-red-100 text-red-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {subscriber.status}
                          </span>
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={() => handleAddSubscriber(subscriber)}
                        className="text-blue-600 hover:text-blue-800 text-sm px-3 py-1 rounded hover:bg-blue-50 ml-2"
                      >
                        Add
                      </button>
                    </div>
                  ))
                ) : (
                  <div className="p-4 text-center text-gray-500 text-sm">
                    {availableSubscribers.length === 0 
                      ? "All subscribers are already assigned"
                      : "No subscribers match the current filters"
                    }
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="flex justify-end space-x-3 mt-6 pt-4 border-t">
          <button
            type="button"
            onClick={handleClose}
            className="px-4 py-2 text-gray-600 bg-gray-100 rounded hover:bg-gray-200"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default EnhancedAreaSubscribersModal;
