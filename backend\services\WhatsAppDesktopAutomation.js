// WhatsApp Desktop Automation Service
// backend/services/WhatsAppDesktopAutomation.js

import { spawn, exec } from 'child_process';
import path from 'path';
import fs from 'fs';
import { promisify } from 'util';
import puppeteer from 'puppeteer';

const execAsync = promisify(exec);

class WhatsAppDesktopAutomation {
  constructor() {
    this.whatsappProcess = null;
    this.whatsappRunningState = false;
    this.automationInProgress = false;
  }

  /**
   * Check if WhatsApp Desktop is installed
   */
  async isWhatsAppInstalled() {
    try {
      // Check common WhatsApp Desktop installation paths
      const possiblePaths = [
        'C:\\Users\\<USER>\\AppData\\Local\\WhatsApp\\WhatsApp.exe',
        'C:\\Program Files\\WhatsApp\\WhatsApp.exe',
        'C:\\Program Files (x86)\\WhatsApp\\WhatsApp.exe',
        '/Applications/WhatsApp.app', // macOS
        '/usr/bin/whatsapp-desktop', // Linux
        '/snap/bin/whatsapp-for-linux' // Linux Snap
      ];

      for (const whatsappPath of possiblePaths) {
        const expandedPath = whatsappPath.replace('%USERNAME%', process.env.USERNAME || process.env.USER);
        if (fs.existsSync(expandedPath)) {
          return { installed: true, path: expandedPath };
        }
      }

      // Try to find WhatsApp in PATH
      try {
        await execAsync('where whatsapp 2>nul || which whatsapp');
        return { installed: true, path: 'whatsapp' };
      } catch (error) {
        // WhatsApp not found in PATH
      }

      return { installed: false, path: null };
    } catch (error) {
      console.error('Error checking WhatsApp installation:', error);
      return { installed: false, path: null };
    }
  }

  /**
   * Launch WhatsApp Desktop application
   */
  async launchWhatsApp() {
    try {
      const installation = await this.isWhatsAppInstalled();
      
      if (!installation.installed) {
        throw new Error('WhatsApp Desktop is not installed. Please install WhatsApp Desktop from https://www.whatsapp.com/download');
      }

      console.log('🚀 Launching WhatsApp Desktop...');

      // Launch WhatsApp Desktop
      if (process.platform === 'win32') {
        // Windows
        this.whatsappProcess = spawn('cmd', ['/c', 'start', '', installation.path], {
          detached: true,
          stdio: 'ignore'
        });
      } else if (process.platform === 'darwin') {
        // macOS
        this.whatsappProcess = spawn('open', ['-a', 'WhatsApp'], {
          detached: true,
          stdio: 'ignore'
        });
      } else {
        // Linux
        this.whatsappProcess = spawn(installation.path, [], {
          detached: true,
          stdio: 'ignore'
        });
      }

      this.whatsappRunningState = true;

      // Give WhatsApp time to start
      await this.delay(3000);

      console.log('✅ WhatsApp Desktop launched successfully');
      return { success: true, message: 'WhatsApp Desktop launched successfully' };

    } catch (error) {
      console.error('❌ Failed to launch WhatsApp Desktop:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Check if WhatsApp Desktop is currently running
   */
  async isWhatsAppRunning() {
    try {
      let command;
      if (process.platform === 'win32') {
        command = 'tasklist /FI "IMAGENAME eq WhatsApp.exe" 2>NUL | find /I /N "WhatsApp.exe"';
      } else if (process.platform === 'darwin') {
        command = 'pgrep -f "WhatsApp"';
      } else {
        command = 'pgrep -f "whatsapp"';
      }

      const { stdout } = await execAsync(command);
      return stdout.trim().length > 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * Send a single message via WhatsApp Desktop with enhanced automation
   */
  async sendSingleMessage(phoneNumber, message) {
    try {
      console.log(`📱 Sending message to ${phoneNumber} with enhanced automation`);

      // Ensure WhatsApp is running
      const isRunning = await this.isWhatsAppRunning();
      if (!isRunning) {
        const launchResult = await this.launchWhatsApp();
        if (!launchResult.success) {
          throw new Error(launchResult.error);
        }
      }

      // Format phone number (remove any non-digits and ensure country code)
      const cleanPhone = phoneNumber.replace(/\D/g, '');
      const formattedPhone = cleanPhone.length === 10 ? `91${cleanPhone}` : cleanPhone;

      // Create WhatsApp URL for desktop app
      const whatsappUrl = `whatsapp://send?phone=${formattedPhone}&text=${encodeURIComponent(message)}`;

      // Open the URL (this will open in WhatsApp Desktop if it's running)
      console.log(`🔗 Opening WhatsApp URL: ${whatsappUrl}`);
      if (process.platform === 'win32') {
        await execAsync(`start "" "${whatsappUrl}"`);
      } else if (process.platform === 'darwin') {
        await execAsync(`open "${whatsappUrl}"`);
      } else {
        await execAsync(`xdg-open "${whatsappUrl}"`);
      }

      // Wait for WhatsApp to open and load the chat
      console.log('⏳ Waiting for WhatsApp to load chat...');
      await this.delay(5000); // Increased delay for better reliability

      // Try multiple methods to send the message
      const sendSuccess = await this.attemptMessageSending();

      if (sendSuccess) {
        console.log(`✅ Message sent to ${phoneNumber} with enhanced automation`);
        return {
          success: true,
          messageId: `desktop_auto_${Date.now()}_${formattedPhone}`,
          phone: formattedPhone
        };
      } else {
        throw new Error('Failed to send message after multiple attempts');
      }

    } catch (error) {
      console.error(`❌ Failed to send message to ${phoneNumber}:`, error);
      return {
        success: false,
        error: error.message,
        phone: phoneNumber
      };
    }
  }

  /**
   * Attempt to send message using multiple methods
   */
  async attemptMessageSending() {
    const methods = [
      () => this.sendWithEnterKey(),
      () => this.sendWithCtrlEnter(),
      () => this.sendWithTabEnter()
    ];

    for (let i = 0; i < methods.length; i++) {
      try {
        console.log(`📤 Attempting send method ${i + 1}/${methods.length}...`);
        await methods[i]();
        await this.delay(1500); // Wait to see if message was sent

        // For now, assume success (in a real implementation, you might check if the message input is cleared)
        console.log(`✅ Send method ${i + 1} completed`);
        return true;
      } catch (error) {
        console.log(`❌ Send method ${i + 1} failed:`, error.message);
        if (i < methods.length - 1) {
          console.log('🔄 Trying next method...');
          await this.delay(1000);
        }
      }
    }

    return false;
  }

  /**
   * Send using Enter key
   */
  async sendWithEnterKey() {
    console.log('📤 Sending with Enter key...');
    await this.sendEnterKey();
  }

  /**
   * Send using Ctrl+Enter (alternative shortcut)
   */
  async sendWithCtrlEnter() {
    console.log('📤 Sending with Ctrl+Enter...');
    if (process.platform === 'win32') {
      const psScript = `
        Add-Type -AssemblyName System.Windows.Forms
        [System.Windows.Forms.SendKeys]::SendWait("^{ENTER}")
      `;
      await execAsync(`powershell -Command "${psScript}"`);
    } else if (process.platform === 'darwin') {
      await execAsync(`osascript -e 'tell application "System Events" to keystroke return using command down'`);
    } else {
      await execAsync('xdotool key ctrl+Return');
    }
  }

  /**
   * Send using Tab to focus send button then Enter
   */
  async sendWithTabEnter() {
    console.log('📤 Sending with Tab+Enter...');
    if (process.platform === 'win32') {
      const psScript = `
        Add-Type -AssemblyName System.Windows.Forms
        [System.Windows.Forms.SendKeys]::SendWait("{TAB}")
        Start-Sleep -Milliseconds 500
        [System.Windows.Forms.SendKeys]::SendWait("{ENTER}")
      `;
      await execAsync(`powershell -Command "${psScript}"`);
    } else if (process.platform === 'darwin') {
      await execAsync(`osascript -e 'tell application "System Events" to key code 48'`); // Tab
      await this.delay(500);
      await execAsync(`osascript -e 'tell application "System Events" to key code 36'`); // Enter
    } else {
      await execAsync('xdotool key Tab');
      await this.delay(500);
      await execAsync('xdotool key Return');
    }
  }

  /**
   * Send Enter key to WhatsApp Desktop with focus management
   */
  async sendEnterKey() {
    try {
      if (process.platform === 'win32') {
        // Enhanced Windows automation with focus management
        const psScript = `
          Add-Type -AssemblyName System.Windows.Forms
          Add-Type -AssemblyName System.Runtime.InteropServices

          # Find WhatsApp window
          Add-Type @"
            using System;
            using System.Runtime.InteropServices;
            public class Win32 {
              [DllImport("user32.dll")]
              public static extern IntPtr FindWindow(string lpClassName, string lpWindowName);
              [DllImport("user32.dll")]
              public static extern bool SetForegroundWindow(IntPtr hWnd);
              [DllImport("user32.dll")]
              public static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);
            }
"@

          # Try to find WhatsApp window
          $whatsappWindow = [Win32]::FindWindow($null, "WhatsApp")
          if ($whatsappWindow -eq [IntPtr]::Zero) {
            # Try alternative window title
            $whatsappWindow = [Win32]::FindWindow($null, "WhatsApp Desktop")
          }

          if ($whatsappWindow -ne [IntPtr]::Zero) {
            # Bring WhatsApp to foreground
            [Win32]::ShowWindow($whatsappWindow, 9)  # SW_RESTORE
            [Win32]::SetForegroundWindow($whatsappWindow)
            Start-Sleep -Milliseconds 500
          }

          # Send Enter key
          [System.Windows.Forms.SendKeys]::SendWait("{ENTER}")
        `;
        await execAsync(`powershell -Command "${psScript.replace(/"/g, '\\"')}"`);
        console.log('⌨️ Enhanced Enter key sent via PowerShell with focus management');
      } else if (process.platform === 'darwin') {
        // Enhanced macOS automation with focus management
        const appleScript = `
          tell application "System Events"
            set whatsappApp to first application process whose name contains "WhatsApp"
            set frontmost of whatsappApp to true
            delay 0.5
            key code 36
          end tell
        `;
        await execAsync(`osascript -e '${appleScript}'`);
        console.log('⌨️ Enhanced Enter key sent via AppleScript with focus management');
      } else {
        // Enhanced Linux automation with focus management
        await execAsync('xdotool search --name "WhatsApp" windowactivate --sync');
        await this.delay(500);
        await execAsync('xdotool key Return');
        console.log('⌨️ Enhanced Enter key sent via xdotool with focus management');
      }
    } catch (error) {
      console.error('❌ Failed to send Enter key:', error);
      throw error;
    }
  }

  /**
   * Send multiple messages with full automation (including clicking send)
   */
  async sendBulkMessages(messages, config = {}) {
    if (this.automationInProgress) {
      throw new Error('Automation is already in progress. Please wait for the current batch to complete.');
    }

    this.automationInProgress = true;

    try {
      console.log(`🚀 Starting bulk WhatsApp automation for ${messages.length} messages`);

      // Choose automation method based on config
      const automationMethod = config.automationMethod || 'browser'; // 'browser' or 'desktop'

      let result;
      if (automationMethod === 'desktop') {
        console.log('🖥️ Using desktop keyboard automation method');
        result = await this.sendBulkMessagesWithDesktop(messages, config);
      } else {
        console.log('🌐 Using browser automation method');
        result = await this.sendBulkMessagesWithBrowser(messages, config);
      }

      return result;

    } catch (error) {
      console.error('❌ Bulk automation failed:', error);
      throw error;
    } finally {
      this.automationInProgress = false;
    }
  }

  /**
   * Send bulk messages using desktop keyboard automation
   */
  async sendBulkMessagesWithDesktop(messages, config = {}) {
    console.log(`🖥️ Starting desktop keyboard automation for ${messages.length} messages`);

    const results = {
      total: messages.length,
      successful: 0,
      failed: 0,
      results: []
    };

    // Ensure WhatsApp Desktop is running
    const isRunning = await this.isWhatsAppRunning();
    if (!isRunning) {
      console.log('📱 WhatsApp Desktop not running, launching...');
      const launchResult = await this.launchWhatsApp();
      if (!launchResult.success) {
        throw new Error(`Failed to launch WhatsApp Desktop: ${launchResult.error}`);
      }
    }

    // Process messages with delays
    const delay = (config.batchDelay || 5) * 1000; // 5 seconds default

    for (let i = 0; i < messages.length; i++) {
      const message = messages[i];
      console.log(`📱 Sending message ${i + 1}/${messages.length} to ${message.to}`);

      try {
        const result = await this.sendSingleMessage(message.to, message.message);

        results.results.push({
          phone: message.to,
          success: result.success,
          messageId: result.messageId,
          error: result.error
        });

        if (result.success) {
          results.successful++;
        } else {
          results.failed++;
        }

        // Delay between messages
        if (i < messages.length - 1 && delay > 0) {
          console.log(`⏳ Waiting ${delay/1000}s before next message...`);
          await this.delay(delay);
        }

      } catch (error) {
        console.error(`❌ Error processing message for ${message.to}:`, error);
        results.results.push({
          phone: message.to,
          success: false,
          error: error.message
        });
        results.failed++;
      }
    }

    console.log(`✅ Desktop automation completed: ${results.successful} successful, ${results.failed} failed`);
    return results;
  }

  /**
   * Send bulk messages using browser automation for full control
   */
  async sendBulkMessagesWithBrowser(messages, config = {}) {
    console.log(`🌐 Starting browser-based WhatsApp automation for ${messages.length} messages`);

    const results = {
      total: messages.length,
      successful: 0,
      failed: 0,
      results: []
    };

    let browser = null;

    try {
      // Launch browser with WhatsApp Web
      browser = await puppeteer.launch({
        headless: false,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor',
          '--disable-blink-features=AutomationControlled'
        ],
        defaultViewport: { width: 1366, height: 768 }
      });

      const page = await browser.newPage();

      // Navigate to WhatsApp Web
      console.log('🌐 Opening WhatsApp Web...');
      await page.goto('https://web.whatsapp.com', { waitUntil: 'networkidle0' });

      // Wait for QR code or main interface
      console.log('⏳ Waiting for WhatsApp Web to load...');
      await page.waitForSelector('[data-testid="qr-code"], [data-testid="chat-list"]', { timeout: 30000 });

      // Check if logged in
      const isLoggedIn = await page.$('[data-testid="chat-list"]');

      if (!isLoggedIn) {
        console.log('❌ Please scan QR code in WhatsApp Web to login first');
        await browser.close();
        return {
          total: messages.length,
          successful: 0,
          failed: messages.length,
          results: messages.map(msg => ({
            phone: msg.to,
            success: false,
            error: 'Not logged in to WhatsApp Web - please scan QR code'
          }))
        };
      }

      console.log('✅ WhatsApp Web is ready, starting message sending...');

      // Process messages with delays
      const batchSize = config.maxBatchSize || 10; // Smaller batches for browser automation
      const delay = (config.batchDelay || 5) * 1000; // Longer delays for stability

      for (let i = 0; i < messages.length; i++) {
        const message = messages[i];
        console.log(`📱 Sending message ${i + 1}/${messages.length} to ${message.to}`);

        try {
          const success = await this.sendSingleMessageWithBrowser(page, message.to, message.message);

          results.results.push({
            phone: message.to,
            success: success,
            messageId: success ? `browser_${Date.now()}_${i}` : null,
            error: success ? null : 'Failed to send message'
          });

          if (success) {
            results.successful++;
            console.log(`✅ Message sent successfully to ${message.to}`);
          } else {
            results.failed++;
            console.log(`❌ Failed to send message to ${message.to}`);
          }

          // Delay between messages
          if (i < messages.length - 1 && delay > 0) {
            console.log(`⏳ Waiting ${delay/1000}s before next message...`);
            await this.delay(delay);
          }

        } catch (error) {
          console.error(`❌ Error sending to ${message.to}:`, error);
          results.results.push({
            phone: message.to,
            success: false,
            error: error.message
          });
          results.failed++;
        }
      }

      console.log(`✅ Browser automation completed: ${results.successful} successful, ${results.failed} failed`);

      // Close browser if configured
      if (config.autoClose !== false) {
        await browser.close();
      }

      return results;

    } catch (error) {
      console.error('❌ Browser automation failed:', error);
      if (browser) {
        await browser.close();
      }
      throw error;
    }
  }

  /**
   * Send a single message using browser automation
   */
  async sendSingleMessageWithBrowser(page, phoneNumber, message) {
    try {
      // Format phone number
      const cleanPhone = phoneNumber.replace(/\D/g, '');
      const formattedPhone = cleanPhone.length === 10 ? `91${cleanPhone}` : cleanPhone;

      // Create WhatsApp Web URL
      const chatUrl = `https://web.whatsapp.com/send?phone=${formattedPhone}&text=${encodeURIComponent(message)}`;

      console.log(`🔗 Opening chat for ${formattedPhone}`);
      await page.goto(chatUrl, { waitUntil: 'networkidle0', timeout: 15000 });

      // Wait for the message input to be filled
      await this.delay(2000);

      // Wait for send button and click it
      console.log(`📤 Looking for send button...`);

      // Try multiple selectors for the send button
      const sendButtonSelectors = [
        '[data-testid="send"]',
        '[aria-label="Send"]',
        'button[aria-label="Send"]',
        'span[data-icon="send"]',
        'button span[data-icon="send"]'
      ];

      let sendButton = null;
      for (const selector of sendButtonSelectors) {
        try {
          await page.waitForSelector(selector, { timeout: 5000 });
          sendButton = await page.$(selector);
          if (sendButton) {
            console.log(`✅ Found send button with selector: ${selector}`);
            break;
          }
        } catch (error) {
          console.log(`⏭️ Send button not found with selector: ${selector}`);
        }
      }

      if (sendButton) {
        // Click the send button
        console.log(`📤 Clicking send button...`);
        await sendButton.click();

        // Wait for message to be sent
        await this.delay(2000);

        // Verify message was sent by checking if input is empty
        const messageInput = await page.$('[data-testid="conversation-compose-box-input"]');
        if (messageInput) {
          const inputValue = await page.evaluate(el => el.textContent || el.value, messageInput);
          if (!inputValue || inputValue.trim() === '') {
            console.log(`✅ Message sent successfully (input cleared)`);
            return true;
          }
        }

        console.log(`✅ Send button clicked (assuming success)`);
        return true;
      } else {
        // Fallback: Try pressing Enter key
        console.log(`⌨️ Send button not found, trying Enter key...`);
        await page.keyboard.press('Enter');
        await this.delay(1000);
        return true;
      }

    } catch (error) {
      console.error(`❌ Failed to send message to ${phoneNumber}:`, error);
      return false;
    }
  }

  /**
   * Test WhatsApp Desktop automation
   */
  async testAutomation() {
    try {
      console.log('🧪 Testing WhatsApp Desktop automation...');

      // Check if WhatsApp is installed
      const installation = await this.isWhatsAppInstalled();
      if (!installation.installed) {
        return {
          success: false,
          error: 'WhatsApp Desktop is not installed. Please install WhatsApp Desktop from https://www.whatsapp.com/download'
        };
      }

      // Check if WhatsApp is running
      const isRunning = await this.isWhatsAppRunning();
      if (!isRunning) {
        // Try to launch WhatsApp
        const launchResult = await this.launchWhatsApp();
        if (!launchResult.success) {
          return {
            success: false,
            error: `Failed to launch WhatsApp Desktop: ${launchResult.error}`
          };
        }
      }

      return {
        success: true,
        message: 'WhatsApp Desktop automation is ready. WhatsApp Desktop is installed and running.'
      };

    } catch (error) {
      console.error('❌ Automation test failed:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Close WhatsApp Desktop (if needed)
   */
  async closeWhatsApp() {
    try {
      if (process.platform === 'win32') {
        await execAsync('taskkill /F /IM WhatsApp.exe 2>nul');
      } else if (process.platform === 'darwin') {
        await execAsync('pkill -f "WhatsApp"');
      } else {
        await execAsync('pkill -f "whatsapp"');
      }

      this.whatsappRunningState = false;
      console.log('✅ WhatsApp Desktop closed');
      return { success: true };

    } catch (error) {
      console.error('❌ Failed to close WhatsApp Desktop:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Utility function for delays
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get automation status
   */
  getStatus() {
    return {
      isRunning: this.whatsappRunningState,
      automationInProgress: this.automationInProgress,
      processId: this.whatsappProcess?.pid || null
    };
  }
}

// Export singleton instance
const whatsappDesktopAutomation = new WhatsAppDesktopAutomation();
export default whatsappDesktopAutomation;
