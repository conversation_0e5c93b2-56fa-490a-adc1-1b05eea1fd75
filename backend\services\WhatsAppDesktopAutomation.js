// WhatsApp Desktop Automation Service
// backend/services/WhatsAppDesktopAutomation.js

const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const { promisify } = require('util');

const execAsync = promisify(exec);

class WhatsAppDesktopAutomation {
  constructor() {
    this.whatsappProcess = null;
    this.isWhatsAppRunning = false;
    this.automationInProgress = false;
  }

  /**
   * Check if WhatsApp Desktop is installed
   */
  async isWhatsAppInstalled() {
    try {
      // Check common WhatsApp Desktop installation paths
      const possiblePaths = [
        'C:\\Users\\<USER>\\AppData\\Local\\WhatsApp\\WhatsApp.exe',
        'C:\\Program Files\\WhatsApp\\WhatsApp.exe',
        'C:\\Program Files (x86)\\WhatsApp\\WhatsApp.exe',
        '/Applications/WhatsApp.app', // macOS
        '/usr/bin/whatsapp-desktop', // Linux
        '/snap/bin/whatsapp-for-linux' // Linux Snap
      ];

      for (const whatsappPath of possiblePaths) {
        const expandedPath = whatsappPath.replace('%USERNAME%', process.env.USERNAME || process.env.USER);
        if (fs.existsSync(expandedPath)) {
          return { installed: true, path: expandedPath };
        }
      }

      // Try to find WhatsApp in PATH
      try {
        await execAsync('where whatsapp 2>nul || which whatsapp');
        return { installed: true, path: 'whatsapp' };
      } catch (error) {
        // WhatsApp not found in PATH
      }

      return { installed: false, path: null };
    } catch (error) {
      console.error('Error checking WhatsApp installation:', error);
      return { installed: false, path: null };
    }
  }

  /**
   * Launch WhatsApp Desktop application
   */
  async launchWhatsApp() {
    try {
      const installation = await this.isWhatsAppInstalled();
      
      if (!installation.installed) {
        throw new Error('WhatsApp Desktop is not installed. Please install WhatsApp Desktop from https://www.whatsapp.com/download');
      }

      console.log('🚀 Launching WhatsApp Desktop...');

      // Launch WhatsApp Desktop
      if (process.platform === 'win32') {
        // Windows
        this.whatsappProcess = spawn('cmd', ['/c', 'start', '', installation.path], {
          detached: true,
          stdio: 'ignore'
        });
      } else if (process.platform === 'darwin') {
        // macOS
        this.whatsappProcess = spawn('open', ['-a', 'WhatsApp'], {
          detached: true,
          stdio: 'ignore'
        });
      } else {
        // Linux
        this.whatsappProcess = spawn(installation.path, [], {
          detached: true,
          stdio: 'ignore'
        });
      }

      this.isWhatsAppRunning = true;

      // Give WhatsApp time to start
      await this.delay(3000);

      console.log('✅ WhatsApp Desktop launched successfully');
      return { success: true, message: 'WhatsApp Desktop launched successfully' };

    } catch (error) {
      console.error('❌ Failed to launch WhatsApp Desktop:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Check if WhatsApp Desktop is currently running
   */
  async isWhatsAppRunning() {
    try {
      let command;
      if (process.platform === 'win32') {
        command = 'tasklist /FI "IMAGENAME eq WhatsApp.exe" 2>NUL | find /I /N "WhatsApp.exe"';
      } else if (process.platform === 'darwin') {
        command = 'pgrep -f "WhatsApp"';
      } else {
        command = 'pgrep -f "whatsapp"';
      }

      const { stdout } = await execAsync(command);
      return stdout.trim().length > 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * Send a single message via WhatsApp Desktop
   * This creates a WhatsApp URL that opens in the desktop app
   */
  async sendSingleMessage(phoneNumber, message) {
    try {
      console.log(`📱 Sending message to ${phoneNumber}`);

      // Ensure WhatsApp is running
      const isRunning = await this.isWhatsAppRunning();
      if (!isRunning) {
        const launchResult = await this.launchWhatsApp();
        if (!launchResult.success) {
          throw new Error(launchResult.error);
        }
      }

      // Format phone number (remove any non-digits and ensure country code)
      const cleanPhone = phoneNumber.replace(/\D/g, '');
      const formattedPhone = cleanPhone.length === 10 ? `91${cleanPhone}` : cleanPhone;

      // Create WhatsApp URL for desktop app
      const whatsappUrl = `whatsapp://send?phone=${formattedPhone}&text=${encodeURIComponent(message)}`;

      // Open the URL (this will open in WhatsApp Desktop if it's running)
      if (process.platform === 'win32') {
        await execAsync(`start "" "${whatsappUrl}"`);
      } else if (process.platform === 'darwin') {
        await execAsync(`open "${whatsappUrl}"`);
      } else {
        await execAsync(`xdg-open "${whatsappUrl}"`);
      }

      // Give time for the message to be processed
      await this.delay(2000);

      console.log(`✅ Message sent to ${phoneNumber}`);
      return {
        success: true,
        messageId: `desktop_${Date.now()}_${formattedPhone}`,
        phone: formattedPhone
      };

    } catch (error) {
      console.error(`❌ Failed to send message to ${phoneNumber}:`, error);
      return {
        success: false,
        error: error.message,
        phone: phoneNumber
      };
    }
  }

  /**
   * Send multiple messages with automation
   */
  async sendBulkMessages(messages, config = {}) {
    if (this.automationInProgress) {
      throw new Error('Automation is already in progress. Please wait for the current batch to complete.');
    }

    this.automationInProgress = true;

    try {
      console.log(`🚀 Starting bulk WhatsApp automation for ${messages.length} messages`);

      const results = {
        total: messages.length,
        successful: 0,
        failed: 0,
        results: []
      };

      // Ensure WhatsApp Desktop is running
      const isRunning = await this.isWhatsAppRunning();
      if (!isRunning) {
        console.log('📱 WhatsApp Desktop not running, launching...');
        const launchResult = await this.launchWhatsApp();
        if (!launchResult.success) {
          throw new Error(`Failed to launch WhatsApp Desktop: ${launchResult.error}`);
        }
      }

      // Process messages in batches
      const batchSize = config.maxBatchSize || 50;
      const delay = (config.batchDelay || 3) * 1000;

      console.log(`📊 Processing ${messages.length} messages in batches of ${batchSize} with ${delay/1000}s delay`);

      for (let i = 0; i < messages.length; i += batchSize) {
        const batch = messages.slice(i, i + batchSize);
        console.log(`📦 Processing batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(messages.length/batchSize)} (${batch.length} messages)`);

        for (const message of batch) {
          try {
            const result = await this.sendSingleMessage(message.to, message.message);
            
            results.results.push({
              phone: message.to,
              success: result.success,
              messageId: result.messageId,
              error: result.error
            });

            if (result.success) {
              results.successful++;
            } else {
              results.failed++;
            }

            // Delay between messages to avoid rate limiting
            if (delay > 0) {
              await this.delay(delay);
            }

          } catch (error) {
            console.error(`❌ Error processing message for ${message.to}:`, error);
            results.results.push({
              phone: message.to,
              success: false,
              error: error.message
            });
            results.failed++;
          }
        }

        // Longer delay between batches
        if (i + batchSize < messages.length) {
          console.log('⏳ Waiting between batches...');
          await this.delay(delay * 2);
        }
      }

      console.log(`✅ Bulk automation completed: ${results.successful} successful, ${results.failed} failed`);
      return results;

    } catch (error) {
      console.error('❌ Bulk automation failed:', error);
      throw error;
    } finally {
      this.automationInProgress = false;
    }
  }

  /**
   * Test WhatsApp Desktop automation
   */
  async testAutomation() {
    try {
      console.log('🧪 Testing WhatsApp Desktop automation...');

      // Check if WhatsApp is installed
      const installation = await this.isWhatsAppInstalled();
      if (!installation.installed) {
        return {
          success: false,
          error: 'WhatsApp Desktop is not installed. Please install WhatsApp Desktop from https://www.whatsapp.com/download'
        };
      }

      // Check if WhatsApp is running
      const isRunning = await this.isWhatsAppRunning();
      if (!isRunning) {
        // Try to launch WhatsApp
        const launchResult = await this.launchWhatsApp();
        if (!launchResult.success) {
          return {
            success: false,
            error: `Failed to launch WhatsApp Desktop: ${launchResult.error}`
          };
        }
      }

      return {
        success: true,
        message: 'WhatsApp Desktop automation is ready. WhatsApp Desktop is installed and running.'
      };

    } catch (error) {
      console.error('❌ Automation test failed:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Close WhatsApp Desktop (if needed)
   */
  async closeWhatsApp() {
    try {
      if (process.platform === 'win32') {
        await execAsync('taskkill /F /IM WhatsApp.exe 2>nul');
      } else if (process.platform === 'darwin') {
        await execAsync('pkill -f "WhatsApp"');
      } else {
        await execAsync('pkill -f "whatsapp"');
      }

      this.isWhatsAppRunning = false;
      console.log('✅ WhatsApp Desktop closed');
      return { success: true };

    } catch (error) {
      console.error('❌ Failed to close WhatsApp Desktop:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Utility function for delays
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get automation status
   */
  getStatus() {
    return {
      isRunning: this.isWhatsAppRunning,
      automationInProgress: this.automationInProgress,
      processId: this.whatsappProcess?.pid || null
    };
  }
}

// Export singleton instance
const whatsappDesktopAutomation = new WhatsAppDesktopAutomation();
module.exports = whatsappDesktopAutomation;
