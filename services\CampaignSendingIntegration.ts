import { whatsappService, WhatsAppMessage } from './WhatsAppService';

/**
 * Integration service to bridge backend campaign sending with frontend WhatsApp services
 */
class CampaignSendingIntegration {

  /**
   * Handle WhatsApp campaign sending from backend
   */
  async handleWhatsAppCampaignSending(campaignData: {
    id: string;
    name: string;
    whatsapp_content: string;
    recipients: Array<{
      id: string;
      name: string;
      phone?: string;
      whatsapp_number?: string;
    }>;
  }): Promise<{
    success: boolean;
    sent: number;
    failed: number;
    results?: any[];
    error?: string;
  }> {
    try {
      console.log(`💬 [INTEGRATION] Processing WhatsApp campaign: ${campaignData.name}`);

      // Check if WhatsApp is globally enabled
      if (!whatsappService.isGloballyEnabled()) {
        console.log(`💬 [INTEGRATION] WhatsApp is globally disabled`);
        return {
          success: false,
          sent: 0,
          failed: 0,
          error: 'WhatsApp is globally disabled'
        };
      }

      // Validate campaign data
      if (!campaignData.whatsapp_content) {
        console.log(`💬 [INTEGRATION] No WhatsApp content found in campaign`);
        return {
          success: false,
          sent: 0,
          failed: 0,
          error: 'No WhatsApp content in campaign'
        };
      }

      // Get valid recipients with WhatsApp numbers
      const validRecipients = campaignData.recipients.filter(recipient => {
        const phone = recipient.whatsapp_number || recipient.phone;
        const isValid = phone && whatsappService.isValidPhoneNumber(phone);
        if (!isValid) {
          console.log(`💬 [INTEGRATION] Skipping recipient ${recipient.name} - invalid WhatsApp number`);
        }
        return isValid;
      });

      if (validRecipients.length === 0) {
        console.log(`💬 [INTEGRATION] No valid WhatsApp recipients found`);
        return {
          success: false,
          sent: 0,
          failed: 0,
          error: 'No valid WhatsApp recipients'
        };
      }

      console.log(`💬 [INTEGRATION] Found ${validRecipients.length} valid WhatsApp recipients`);

      // Prepare WhatsApp messages
      const messages: WhatsAppMessage[] = validRecipients.map(recipient => ({
        to: recipient.whatsapp_number || recipient.phone || '',
        message: campaignData.whatsapp_content.replace(/\{name\}/g, recipient.name || 'there'),
        subscriberId: recipient.id,
        subscriberName: recipient.name
      }));

      console.log(`💬 [INTEGRATION] Using unified WhatsApp service for bulk sending`);
      
      // Show user notification about WhatsApp sending
      this.showWhatsAppSendingNotification(campaignData.name, messages.length);
      
      // Send via bulk desktop automation
      const result = await whatsappService.sendBulkViaDesktop(messages);
      
      // Show completion notification
      this.showWhatsAppCompletionNotification(result);
      
      return {
        success: true,
        sent: result.successful,
        failed: result.failed,
        results: result.results
      };

    } catch (error) {
      console.error(`❌ [INTEGRATION] WhatsApp campaign sending failed:`, error);
      return {
        success: false,
        sent: 0,
        failed: campaignData.recipients?.length || 0,
        error: error.message
      };
    }
  }

  /**
   * Show notification about WhatsApp sending starting
   */
  private showWhatsAppSendingNotification(campaignName: string, messageCount: number): void {
    try {
      // Create a notification or modal
      const notification = document.createElement('div');
      notification.id = 'whatsapp-sending-notification';
      notification.className = 'fixed top-4 right-4 bg-green-500 text-white p-4 rounded-lg shadow-lg z-50';
      notification.innerHTML = `
        <div class="flex items-center space-x-3">
          <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
          <div>
            <div class="font-medium">WhatsApp Campaign: ${campaignName}</div>
            <div class="text-sm">Sending ${messageCount} messages via WhatsApp automation...</div>
          </div>
        </div>
      `;
      
      document.body.appendChild(notification);
      
      // Auto-remove after 10 seconds
      setTimeout(() => {
        const element = document.getElementById('whatsapp-sending-notification');
        if (element) {
          element.remove();
        }
      }, 10000);
      
    } catch (error) {
      console.warn('Failed to show WhatsApp notification:', error);
    }
  }

  /**
   * Show notification about WhatsApp sending completion
   */
  private showWhatsAppCompletionNotification(result: any): void {
    try {
      // Remove sending notification
      const sendingNotification = document.getElementById('whatsapp-sending-notification');
      if (sendingNotification) {
        sendingNotification.remove();
      }
      
      // Create completion notification
      const notification = document.createElement('div');
      notification.className = 'fixed top-4 right-4 bg-blue-500 text-white p-4 rounded-lg shadow-lg z-50';
      notification.innerHTML = `
        <div class="flex items-center space-x-3">
          <div class="text-2xl">✅</div>
          <div>
            <div class="font-medium">WhatsApp Campaign Complete</div>
            <div class="text-sm">Sent: ${result.successful}, Failed: ${result.failed}</div>
          </div>
        </div>
      `;
      
      document.body.appendChild(notification);
      
      // Auto-remove after 5 seconds
      setTimeout(() => {
        notification.remove();
      }, 5000);
      
    } catch (error) {
      console.warn('Failed to show completion notification:', error);
    }
  }

  /**
   * Listen for campaign sending events from backend
   */
  setupCampaignSendingListener(): void {
    // Listen for custom events from the backend integration
    window.addEventListener('whatsapp-campaign-trigger', async (event: any) => {
      const campaignData = event.detail;
      console.log(`💬 [INTEGRATION] Received WhatsApp campaign trigger:`, campaignData);
      
      try {
        const result = await this.handleWhatsAppCampaignSending(campaignData);
        
        // Dispatch completion event
        window.dispatchEvent(new CustomEvent('whatsapp-campaign-complete', {
          detail: { campaignId: campaignData.id, result }
        }));
        
      } catch (error) {
        console.error(`❌ [INTEGRATION] Campaign sending failed:`, error);
        
        // Dispatch error event
        window.dispatchEvent(new CustomEvent('whatsapp-campaign-error', {
          detail: { campaignId: campaignData.id, error: error.message }
        }));
      }
    });
  }

  /**
   * Trigger WhatsApp campaign from frontend
   */
  async triggerWhatsAppCampaign(campaignId: string): Promise<void> {
    try {
      console.log(`💬 [INTEGRATION] Processing WhatsApp campaign: ${campaignId}`);

      // Get pending WhatsApp messages for this campaign
      const logsResponse = await fetch(`/api/campaign-sending-logs/${campaignId}`);
      const logs = await logsResponse.json();

      // Filter for pending WhatsApp messages
      const pendingWhatsAppLogs = logs.filter((log: any) =>
        log.channel === 'whatsapp' && log.status === 'pending'
      );

      if (pendingWhatsAppLogs.length === 0) {
        console.log(`💬 [INTEGRATION] No pending WhatsApp messages found for campaign ${campaignId}`);
        return;
      }

      console.log(`💬 [INTEGRATION] Found ${pendingWhatsAppLogs.length} pending WhatsApp messages`);

      // Process each pending message
      await this.processPendingWhatsAppMessages(pendingWhatsAppLogs);

    } catch (error) {
      console.error(`❌ [INTEGRATION] Failed to trigger WhatsApp campaign:`, error);
      throw error;
    }
  }

  /**
   * Process pending WhatsApp messages
   */
  private async processPendingWhatsAppMessages(pendingLogs: any[]): Promise<void> {
    try {
      console.log(`💬 [INTEGRATION] Processing ${pendingLogs.length} pending WhatsApp messages`);

      // Prepare messages for WhatsApp service
      const messages: WhatsAppMessage[] = pendingLogs.map(log => {
        // Extract message from WhatsApp URL or use direct content
        let phone = '';
        let message = '';
        
        if (log.message_id && log.message_id.includes('phone=')) {
          // Extract from URL format
          const url = log.message_id;
          const phoneMatch = url.match(/phone=([^&]+)/);
          const textMatch = url.match(/text=(.+)/);
          phone = phoneMatch ? decodeURIComponent(phoneMatch[1]) : '';
          message = textMatch ? decodeURIComponent(textMatch[1]) : '';
        } else {
          // Use direct values
          phone = log.recipient_phone || log.recipient_whatsapp || '';
          message = log.message_content || '';
        }

        return {
          to: phone,
          message: message,
          subscriberId: log.recipient_id,
          subscriberName: log.recipient_name
        };
      });

      // Show user notification
      this.showWhatsAppSendingNotification(`Campaign Messages`, messages.length);

      // Send via WhatsApp service
      const result = await whatsappService.sendBulkViaDesktop(messages);

      console.log(`💬 [INTEGRATION] WhatsApp sending result:`, result);

      // Update the sending logs with results
      await this.updateSendingLogsWithResults(pendingLogs, result);

      // Show completion notification
      this.showWhatsAppCompletionNotification(result);

    } catch (error) {
      console.error(`❌ [INTEGRATION] Failed to process pending WhatsApp messages:`, error);
      throw error;
    }
  }

  /**
   * Update sending logs with WhatsApp results
   */
  private async updateSendingLogsWithResults(pendingLogs: any[], result: any): Promise<void> {
    try {
      // Update each log entry based on results
      for (let i = 0; i < pendingLogs.length; i++) {
        const log = pendingLogs[i];
        const wasSuccessful = i < result.successful;

        const updateData = {
          status: wasSuccessful ? 'sent' : 'failed',
          error_message: wasSuccessful ? null : 'WhatsApp sending failed',
          message_id: wasSuccessful ? `whatsapp_sent_${Date.now()}_${i}` : log.message_id
        };

        // Update via API
        await fetch(`/api/campaign-sending-logs/${log.id}`, {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(updateData)
        });
      }

      console.log(`💬 [INTEGRATION] Updated ${pendingLogs.length} sending log entries`);

    } catch (error) {
      console.error(`❌ [INTEGRATION] Failed to update sending logs:`, error);
    }
  }
}

// Export singleton instance
export const campaignSendingIntegration = new CampaignSendingIntegration();

// Auto-setup listener when module loads
if (typeof window !== 'undefined') {
  campaignSendingIntegration.setupCampaignSendingListener();
}

export default campaignSendingIntegration;
