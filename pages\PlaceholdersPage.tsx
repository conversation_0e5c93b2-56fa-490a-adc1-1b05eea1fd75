import React, { useState, useMemo, useEffect } from 'react';
import { useNavigate } from 'react-router-dom'; 
import Header from '../components/Header';
import Table, { Column } from '../components/Table';
import ColumnSelector from '../components/ColumnSelector';
import { PlaceholderManager, PlaceholderContext, FormatType, AuditActionType, UserTablePreferences } from '../types'; 
import { PlusIcon, EditIcon, DeleteIcon, EyeIcon } from '../components/icons';
import { addAuditLog } from '../utils/auditUtilsDB'; 
import ConfirmationModal from '../components/ConfirmationModal'; 
import { useAuth } from '../contexts/AuthContextDB';
import { placeholderService } from '../services/placeholderService';
import { browserDatabaseService } from '../services/BrowserDatabaseService';

const PLACEHOLDERS_TABLE_KEY = 'placeholdersListTable'; // Unique key

const PlaceholdersPage: React.FC = () => {
  const navigate = useNavigate(); 
  const { currentUser } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [placeholders, setPlaceholders] = useState<PlaceholderManager[]>([]);
  const [loading, setLoading] = useState(true);
  const [feedbackMessage, setFeedbackMessage] = useState<string | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false); 
  const [placeholderToDelete, setPlaceholderToDelete] = useState<PlaceholderManager | null>(null); 

  const allTableColumns: Column<PlaceholderManager>[] = useMemo(() => [
    { 
      id: 'display_name',
      header: 'Display Name', 
      accessor: 'display_name', 
      className: 'font-medium text-primary',
      sortable: true,
      sortValue: item => item.display_name.toLowerCase(),
      defaultVisible: true,
      isNonRemovable: true,
    },
    { 
      id: 'name',
      header: 'Name (ID)', 
      accessor: 'name', 
      sortable: true,
      sortValue: item => item.name.toLowerCase(),
      defaultVisible: true,
    },
    { 
      id: 'context',
      header: 'Context', 
      accessor: 'context',
      sortable: true,
      sortValue: item => item.context,
      defaultVisible: true,
    },
    { 
      id: 'field_path',
      header: 'Field Path', 
      accessor: 'field_path', 
      sortable: true,
      sortValue: item => item.field_path.toLowerCase(),
      defaultVisible: true,
    },
    { 
      id: 'default_value',
      header: 'Default Value', 
      accessor: 'default_value',
      sortable: true,
      sortValue: item => item.default_value.toLowerCase(),
      defaultVisible: true,
    },
    { 
      id: 'format_type',
      header: 'Format Type', 
      accessor: 'format_type',
      sortable: true,
      sortValue: item => item.format_type,
      defaultVisible: false,
    },
    { 
      id: 'is_active',
      header: 'Active', 
      accessor: 'is_active',
      sortable: true,
      defaultVisible: true,
    },
    { 
      id: 'created_at',
      header: 'Created At', 
      accessor: 'created_at', 
      render: (item) => new Date(item.created_at).toLocaleDateString(),
      sortable: true,
      sortValue: item => new Date(item.created_at).getTime(),
      defaultVisible: false,
    },
    {
      id: 'actions',
      header: 'Actions',
      accessor: 'id',
      render: (placeholder) => (
        <div className="flex space-x-2">
          <button
            onClick={() => handleViewPlaceholder(placeholder.id)}
            className="text-green-600 hover:text-green-800 p-1"
            title={`View ${placeholder.display_name}`}
            aria-label={`View ${placeholder.display_name}`}
          >
            <EyeIcon className="h-5 w-5" />
          </button>
          <button
            onClick={() => handleEditPlaceholder(placeholder.id)}
            className="text-primary hover:text-opacity-80 p-1"
            title={`Edit ${placeholder.display_name}`}
            aria-label={`Edit ${placeholder.display_name}`}
          >
            <EditIcon className="h-5 w-5" />
          </button>
          <button
            onClick={() => handleDeletePlaceholder(placeholder)}
            className="text-red-500 hover:text-red-700 p-1"
            title={`Delete ${placeholder.display_name}`}
            aria-label={`Delete ${placeholder.display_name}`}
          >
            <DeleteIcon className="h-5 w-5" />
          </button>
        </div>
      ),
      defaultVisible: true,
      isNonRemovable: true,
    },
  ], []);

  const defaultVisibleColumnIds = useMemo(() => allTableColumns.filter(c => c.defaultVisible).map(c => c.id), [allTableColumns]);
  const [visibleColumnIds, setVisibleColumnIds] = useState<string[]>(defaultVisibleColumnIds);

  // Load placeholders from API
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        // Load table preferences first
        if (currentUser?.user_id) {
          const sql = `
            SELECT table_preferences 
            FROM users 
            WHERE user_id = ?
          `;
          const result = await browserDatabaseService.query(sql, [currentUser.user_id]);
          if (result.length > 0 && result[0].table_preferences) {
            const preferences = JSON.parse(result[0].table_preferences);
            if (preferences[PLACEHOLDERS_TABLE_KEY]) {
              setVisibleColumnIds(preferences[PLACEHOLDERS_TABLE_KEY]);
              console.log('✅ Loaded saved column preferences:', preferences[PLACEHOLDERS_TABLE_KEY]);
            }
          }
        }

        // Then load placeholders
        setLoading(true);
        const data = await placeholderService.getAllPlaceholders();
        setPlaceholders(data);
      } catch (error) {
        console.error('Error loading initial data:', error);
        showFeedback('Error loading data: ' + error.message);
      } finally {
        setLoading(false);
      }
    };

    loadInitialData();
  }, [currentUser]);

  useEffect(() => {
    // Remove the localStorage event listener since we're using API now
  }, []);
  
  const handleColumnVisibilityChange = async (newVisibleIds: string[]) => {
    setVisibleColumnIds(newVisibleIds);
    
    if (currentUser?.user_id) {
      try {
        const sql = `
          UPDATE users 
          SET table_preferences = json_set(
            COALESCE(table_preferences, '{}'),
            '$."${PLACEHOLDERS_TABLE_KEY}"',
            json(?)
          )
          WHERE user_id = ?
        `;
        await browserDatabaseService.query(sql, [JSON.stringify(newVisibleIds), currentUser.user_id]);
        console.log(`✅ Saved column preferences for table ${PLACEHOLDERS_TABLE_KEY}`);
      } catch (error) {
        console.error(`❌ Failed to save column preferences for table ${PLACEHOLDERS_TABLE_KEY}:`, error);
      }
    }
  };

  const showFeedback = (message: string) => {
    setFeedbackMessage(message);
    setTimeout(() => {
      setFeedbackMessage(null);
    }, 3000);
  };

  const handleAddPlaceholder = () => {
    navigate('/placeholders/add'); 
  };

  const handleViewPlaceholder = (placeholderId: string) => {
    navigate(`/placeholders/view/${placeholderId}`);
  };

  const handleEditPlaceholder = (placeholderId: string) => {
    navigate(`/placeholders/edit/${placeholderId}`);
  };

  const confirmDeletePlaceholder = async () => {
    if (!placeholderToDelete) return;
    
    try {
      await placeholderService.deletePlaceholder(placeholderToDelete.id);
      const updatedPlaceholders = placeholders.filter(p => p.id !== placeholderToDelete.id);
      setPlaceholders(updatedPlaceholders);
      addAuditLog(AuditActionType.DELETE, 'Placeholder', { entityId: placeholderToDelete.id, entityName: placeholderToDelete.display_name, userId: currentUser?.user_id });
      showFeedback(`Placeholder "${placeholderToDelete.display_name}" deleted successfully.`);
    } catch (error) {
      console.error('Error deleting placeholder:', error);
      showFeedback('Error deleting placeholder: ' + error.message);
    }
    
    setShowDeleteModal(false);
    setPlaceholderToDelete(null);
  };

  const handleDeletePlaceholder = (placeholder: PlaceholderManager) => {
    setPlaceholderToDelete(placeholder);
    setShowDeleteModal(true);
  };

  const filteredPlaceholders = useMemo(() => {
    if (!searchTerm.trim()) {
      return placeholders;
    }
    const lowercasedFilter = searchTerm.toLowerCase();
    return placeholders.filter(placeholder =>
      placeholder.name.toLowerCase().includes(lowercasedFilter) ||
      placeholder.display_name.toLowerCase().includes(lowercasedFilter) ||
      placeholder.context.toLowerCase().includes(lowercasedFilter) ||
      placeholder.field_path.toLowerCase().includes(lowercasedFilter) ||
      placeholder.format_type.toLowerCase().includes(lowercasedFilter)
    );
  }, [placeholders, searchTerm]);
  

  return (
    <div className="text-textPrimary w-full max-w-full">
      <Header title="Placeholder Manager" subtitle="Define and manage dynamic placeholders." />
      {feedbackMessage && (
        <div className="mb-4 p-3 bg-green-100 text-green-700 border border-green-300 rounded-md shadow-sm text-sm dark:bg-green-700 dark:text-green-100 dark:border-green-500" role="alert">
          {feedbackMessage}
        </div>
      )}
      
      {loading ? (
        <div className="flex justify-center items-center py-8">
          <div className="text-lg text-textSecondary">Loading placeholders...</div>
        </div>
      ) : (
        <>
          <div className="mb-6 flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4 w-full">
        <input
          type="text"
          placeholder="Search placeholders (name, context, path)..."
          className="w-full lg:w-auto lg:min-w-80 px-4 py-2 border border-border bg-surface rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          aria-label="Search placeholders"
        />
        <div className="flex items-center gap-2 w-full lg:w-auto">
            <ColumnSelector
                allColumns={allTableColumns}
                visibleColumnIds={visibleColumnIds}
                onSave={handleColumnVisibilityChange}
                defaultVisibleColumnIds={defaultVisibleColumnIds}
                tableKey={PLACEHOLDERS_TABLE_KEY}
                userId={currentUser?.user_id}
            />
            <button
            onClick={handleAddPlaceholder}
            className="w-full lg:w-auto bg-primary hover:bg-opacity-80 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transition duration-150 ease-in-out flex items-center justify-center whitespace-nowrap"
            aria-label="Add new placeholder"
            >
            <PlusIcon className="h-5 w-5 mr-2" />
            Add New Placeholder
            </button>
        </div>
          </div>
        <Table<PlaceholderManager> 
          allColumns={allTableColumns} 
          visibleColumnIds={visibleColumnIds}
          data={filteredPlaceholders} 
          caption="List of Placeholders" 
          rowKey="id"
          onRowDoubleClick={(placeholder) => handleEditPlaceholder(placeholder.id)}
        />
        </>
      )}
      {showDeleteModal && placeholderToDelete && (
        <ConfirmationModal
          isOpen={showDeleteModal}
          title="Confirm Deletion"
          message={<>Are you sure you want to delete placeholder: <strong>{placeholderToDelete.display_name}</strong>?</>}
          onConfirm={confirmDeletePlaceholder}
          onCancel={() => { setShowDeleteModal(false); setPlaceholderToDelete(null); }}
          confirmText="Delete"
        />
      )}
    </div>
  );
};

export default PlaceholdersPage;
