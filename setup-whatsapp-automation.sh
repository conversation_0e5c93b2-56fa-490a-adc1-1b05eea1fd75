#!/bin/bash

# WhatsApp Automation Setup Script
# Installs and configures robotjs automation for CRM4CA

echo "🚀 Setting up WhatsApp Automation for CRM4CA..."
echo "=================================================="

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: Not in CRM project root directory"
    echo "Please run this script from E:\\Projects\\CRM-AIstudio"
    exit 1
fi

# Install robotjs in main project
echo "📦 Installing robotjs in main project..."
npm install robotjs

if [ $? -ne 0 ]; then
    echo "⚠️  Standard installation failed, trying with --force..."
    npm install --force robotjs
fi

# Install robotjs in backend
echo "📦 Installing robotjs in backend..."
cd backend
npm install robotjs

if [ $? -ne 0 ]; then
    echo "⚠️  Backend installation failed, trying with --force..."
    npm install --force robotjs
fi

cd ..

# Check platform-specific requirements
echo "🔍 Checking platform requirements..."

case "$OSTYPE" in
  linux*)
    echo "🐧 Linux detected - checking dependencies..."
    if ! dpkg -l | grep -q libxtst6; then
        echo "⚠️  Missing libxtst6, install with: sudo apt-get install libxtst6"
    fi
    if ! dpkg -l | grep -q libxrandr2; then
        echo "⚠️  Missing libxrandr2, install with: sudo apt-get install libxrandr2"
    fi
    ;;
  darwin*)
    echo "🍎 macOS detected"
    echo "⚠️  You may need to grant accessibility permissions:"
    echo "   System Preferences → Security & Privacy → Accessibility"
    echo "   System Preferences → Security & Privacy → Screen Recording"
    ;;
  msys*|cygwin*|win*)
    echo "🪟 Windows detected"
    echo "✅ No additional dependencies required"
    ;;
esac

# Check WhatsApp Desktop installation
echo "📱 Checking WhatsApp Desktop..."

if command -v whatsapp &> /dev/null; then
    echo "✅ WhatsApp Desktop found"
elif [ -d "/Applications/WhatsApp.app" ]; then
    echo "✅ WhatsApp Desktop found (macOS)"
elif [ -f "/usr/bin/whatsapp-for-linux" ]; then
    echo "✅ WhatsApp Desktop found (Linux)"
else
    echo "⚠️  WhatsApp Desktop not found. Please install from:"
    echo "   - Windows: Microsoft Store or whatsapp.com"
    echo "   - macOS: Mac App Store or whatsapp.com"  
    echo "   - Linux: sudo snap install whatsapp-for-linux"
fi

# Create necessary directories
echo "📁 Creating automation directories..."
mkdir -p assets/whatsapp-templates
mkdir -p assets/screenshots
mkdir -p backend/data
mkdir -p backend/logs

echo "✅ Setup completed!"
echo ""
echo "🎯 Next Steps:"
echo "1. Start the application: npm run dev"
echo "2. Navigate to Settings → WhatsApp Automation"
echo "3. Run system check to verify installation"
echo "4. Test with a few messages before bulk campaigns"
echo ""
echo "📚 Documentation: DevDocs/WHATSAPP_AUTOMATION_IMPLEMENTATION_COMPLETE.md"
echo ""
echo "🎉 WhatsApp automation is ready to use!"
