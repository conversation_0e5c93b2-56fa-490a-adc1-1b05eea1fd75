import React from 'react';
import {
  HomeIcon,
  CampaignIcon,
  TemplateIcon,
  CogIcon,
  UserIcon,
  SubscribersIcon,
  TagIcon,
  GiftIcon,
  ShieldCheckIcon,
  InformationCircleIcon,
  ClipboardListIcon,
  BookOpenIcon,
  AdjustmentsHorizontalIcon,
  SignatureIcon,
  PlaceholderIcon,
  DatabaseIcon,
} from './icons';
import { NavigationGroup } from './NavigationDropdown';

export const navigationGroups: NavigationGroup[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: <HomeIcon className="h-5 w-5" />,
    path: '/dashboard'
  },
  {
    id: 'customers',
    label: 'Client Management',
    icon: <SubscribersIcon className="h-5 w-5" />,
    items: [
      { 
        id: 'subscribers', 
        label: 'Subscribers', 
        path: '/subscribers',
        requiresRegistration: true
      },
      { 
        id: 'areas', 
        label: 'Areas of Interest', 
        path: '/areas-of-interest',
        requiresRegistration: true
      }
    ]
  },
  {
    id: 'campaigns',
    label: 'Campaign Management', 
    icon: <CampaignIcon className="h-5 w-5" />,
    items: [
      { 
        id: 'campaigns', 
        label: 'Campaigns', 
        path: '/campaigns',
        requiresRegistration: true
      },
      { 
        id: 'birthdays', 
        label: 'Birthday Automations', 
        path: '/birthday-automations',
        requiresRegistration: true
      }
    ]
  },
  {
    id: 'content',
    label: 'Content Management',
    icon: <TemplateIcon className="h-5 w-5" />,
    items: [
      { 
        id: 'templates', 
        label: 'Templates', 
        path: '/templates',
        requiresRegistration: true
      },
      { 
        id: 'signatures', 
        label: 'Signatures', 
        path: '/signatures',
        requiresRegistration: true
      },
      { 
        id: 'placeholders', 
        label: 'Placeholders', 
        path: '/placeholders',
        requiresRegistration: true
      }
    ]
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: <CogIcon className="h-5 w-5" />,
    items: [
      { 
        id: 'display', 
        label: 'Display Settings', 
        path: '/settings/display',
        requiresRegistration: true
      },
      { 
        id: 'email', 
        label: 'Email Configuration', 
        path: '/settings/email-configuration',
        requiresRegistration: true
      },
      {
        id: 'whatsapp',
        label: 'WhatsApp Configuration',
        path: '/settings/whatsapp',
        requiresRegistration: true
      },
      {
        id: 'whatsapp-automation',
        label: 'WhatsApp Automation',
        path: '/settings/whatsapp-automation',
        requiresRegistration: true
      },
      { 
        id: 'sms', 
        label: 'SMS Configuration', 
        path: '/settings/sms',
        requiresRegistration: true
      },
      { 
        id: 'registration', 
        label: 'Product Registration', 
        path: '/settings/product-registration',
        requiresRegistration: false // Always accessible
      },
      { 
        id: 'audit', 
        label: 'Audit Trail', 
        path: '/settings/audit-trail',
        requiresRegistration: true
      }
    ]
  },
  {
    id: 'administration',
    label: 'Administration',
    icon: <UserIcon className="h-5 w-5" />,
    adminOnly: true,
    items: [
      {
        id: 'users',
        label: 'User Management',
        path: '/users',
        adminOnly: true,
        requiresRegistration: true
      },
      {
        id: 'backup',
        label: 'Backup Management',
        path: '/backup-management',
        adminOnly: true,
        requiresRegistration: true
      }
    ]
  },
  {
    id: 'help',
    label: 'Help & Information',
    icon: <BookOpenIcon className="h-5 w-5" />,
    items: [
      { 
        id: 'manual', 
        label: 'User Manual', 
        path: '/user-manual',
        requiresRegistration: false // Always accessible
      },
      { 
        id: 'about', 
        label: 'About', 
        path: '/about',
        requiresRegistration: false // Always accessible
      }
    ]
  }
];
