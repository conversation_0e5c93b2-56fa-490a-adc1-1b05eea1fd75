import React, { useEffect } from 'react';
import { HashRouter, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import Layout from './components/Layout';
import DashboardPage from './pages/DashboardPage';
import CampaignsPage from './pages/CampaignsPage';
import TemplatesPage from './pages/TemplatesPage';
import SignaturesPage from './pages/SignaturesPage';
import UsersPage from './pages/UsersPage';
import SubscribersPage from './pages/SubscribersPage';
import PlaceholdersPage from './pages/PlaceholdersPage';
import BirthdayAutomationsPage from './pages/BirthdayAutomationsPage';
import AddEditTemplatePage from './pages/AddEditTemplatePage';
import AddEditSignaturePage from './pages/AddEditSignaturePage';
import AddEditSubscriberPage from './pages/AddEditSubscriberPage';
import AddEditCampaignPage from './pages/AddEditCampaignPage';
import AddEditUserPage from './pages/AddEditUserPage';
import AddEditPlaceholderPage from './pages/AddEditPlaceholderPage';
import PostImportAreasPage from './pages/PostImportAreasPage';
import EmailConfigurationPage from './pages/EmailConfigurationPage';
import SMSConfigurationPage from './pages/SMSConfigurationPage';
import WhatsAppDualConfigurationPage from './pages/WhatsAppDualConfigurationPage';
import AreasOfInterestPage from './pages/AreasOfInterestPage';
import AddEditAreaOfInterestPage from './pages/AddEditAreaOfInterestPage';
import ProductRegistrationPage from './pages/ProductRegistrationPage';
import AboutPage from './pages/AboutPage';
import DisplaySettingsPage from './pages/DisplaySettingsPage';
import LoginPage from './pages/LoginPage';
import AuditTrailPage from './pages/AuditTrailPage';
import UserManualPage from './pages/UserManualPage';
import BackupManagementPage from './pages/BackupManagementPage';
import WhatsAppManualSendingPage from './pages/WhatsAppManualSendingPage';
import ChannelCountsTestPage from './pages/ChannelCountsTestPage';
import WhatsAppAutomationConfigPage from './services/whatsapp-automation/WhatsAppAutomationConfigPage';
import CampaignSendingLogsPage from './pages/CampaignSendingLogsPage';
import AllCampaignSendingLogsPage from './pages/AllCampaignSendingLogsPage';
import CampaignSubscribersPage from './pages/CampaignSubscribersPage';

import { ThemeProvider } from './contexts/ThemeContext';
import { DisplaySettingsProvider } from './contexts/DisplaySettingsContext';
import { SidebarProvider } from './contexts/SidebarContext';
import { AuthProvider, useAuth } from './contexts/AuthContextDB-simple';
import { RegistrationProvider, useRegistration } from './contexts/RegistrationContext';
import { browserDatabaseService } from './services/BrowserDatabaseService';

/**
 * Professional CRM Application - Production Ready
 * Database-backed with complete localStorage removal
 */
const AppContent: React.FC = () => {
  const { isAppRegistered, isLoadingRegistration } = useRegistration();
  const { isAuthenticated, isLoadingAuth } = useAuth();
  const location = useLocation();

  useEffect(() => {
    const initializeApp = async () => {
      try {
        console.log('🚀 [CRM4CA] Professional application loaded - Database-backed architecture');
        
        // Initialize SQLite database
        await browserDatabaseService.initialize();
        
      } catch (error) {
        console.error('❌ [CRM4CA] Database initialization failed:', error);
      }
    };
    
    initializeApp();
  }, []);

  // Enhanced loading state
  if (isLoadingRegistration || isLoadingAuth) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-background text-textPrimary">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <div className="text-lg font-medium">Initializing CRM Application...</div>
          <div className="text-sm text-gray-600 mt-2">
            {isLoadingAuth ? 'Authenticating with database...' : 'Loading application state...'}
          </div>
        </div>
      </div>
    );
  }

  // For now, skip registration check to get straight to the app
  // Production environments can enable this check
  const BYPASS_REGISTRATION = true;

  if (!BYPASS_REGISTRATION && !isAppRegistered) {
    // Allow access to registration pages
    if (location.pathname === '/settings/product-registration' || 
        location.pathname === '/login' || 
        location.pathname === '/about') {
      return (
        <div className="min-h-screen bg-background text-textPrimary">
          <Routes>
            <Route path="/login" element={<LoginPage />} />
            <Route path="/settings/product-registration" element={<ProductRegistrationPage />} />
            <Route path="/about" element={<AboutPage />} />
            <Route path="*" element={<Navigate to="/settings/product-registration" replace />} />
          </Routes>
        </div>
      );
    }
    return <Navigate to="/settings/product-registration" replace />;
  }

  // Full CRM Application Routes
  return (
    <Layout>
      <Routes>
        <Route path="/" element={<Navigate to="/dashboard" replace />} />
        <Route path="/dashboard" element={<DashboardPage />} />
        <Route path="/campaigns" element={<CampaignsPage />} />
        <Route path="/campaigns/add" element={<AddEditCampaignPage />} />
        <Route path="/campaigns/edit/:campaignId" element={<AddEditCampaignPage />} />
        <Route path="/campaigns/:campaignId/subscribers" element={<CampaignSubscribersPage />} />
        <Route path="/campaigns/sending-logs" element={<AllCampaignSendingLogsPage />} />
        <Route path="/campaigns/:campaignId/sending-logs" element={<CampaignSendingLogsPage />} />
        <Route path="/birthday-automations" element={<BirthdayAutomationsPage />} />
        <Route path="/templates" element={<TemplatesPage />} />
        <Route path="/templates/add" element={<AddEditTemplatePage />} />
        <Route path="/templates/edit/:templateId" element={<AddEditTemplatePage />} />
        <Route path="/signatures" element={<SignaturesPage />} />
        <Route path="/signatures/add" element={<AddEditSignaturePage />} />
        <Route path="/signatures/edit/:signatureId" element={<AddEditSignaturePage />} />
        <Route path="/placeholders" element={<PlaceholdersPage />} />
        <Route path="/placeholders/add" element={<AddEditPlaceholderPage />} />
        <Route path="/placeholders/edit/:placeholderId" element={<AddEditPlaceholderPage />} />
        <Route path="/users" element={<UsersPage />} />
        <Route path="/users/add" element={<AddEditUserPage />} />
        <Route path="/users/edit/:userId" element={<AddEditUserPage />} />
        <Route path="/backup-management" element={<BackupManagementPage />} />
        <Route path="/whatsapp-manual-sending" element={<WhatsAppManualSendingPage />} />
        <Route path="/test/channel-counts" element={<ChannelCountsTestPage />} />

        <Route path="/subscribers" element={<SubscribersPage />} />
        <Route path="/subscribers/add" element={<AddEditSubscriberPage />} />
        <Route path="/subscribers/edit/:subscriberId" element={<AddEditSubscriberPage />} />
        <Route path="/subscribers/post-import-areas" element={<PostImportAreasPage />} />

        {/* Areas of Interest Routes */}
        <Route path="/areas-of-interest" element={<AreasOfInterestPage />} />
        <Route path="/areas-of-interest/add" element={<AddEditAreaOfInterestPage />} />
        <Route path="/areas-of-interest/edit/:areaId" element={<AddEditAreaOfInterestPage />} />
        
        <Route path="/settings/email-configuration" element={<EmailConfigurationPage />} />
        <Route path="/settings/whatsapp" element={<WhatsAppDualConfigurationPage />} />
        <Route path="/settings/whatsapp-automation" element={<WhatsAppAutomationConfigPage />} />
        <Route path="/settings/sms" element={<SMSConfigurationPage />} />
        <Route path="/settings/display" element={<DisplaySettingsPage />} />
        <Route path="/settings/product-registration" element={<ProductRegistrationPage />} />
        <Route path="/settings/audit-trail" element={<AuditTrailPage />} />
        <Route path="/user-manual" element={<UserManualPage />} />
        <Route path="/about" element={<AboutPage />} />
        <Route path="/login" element={<LoginPage />} />
        <Route path="*" element={<Navigate to="/dashboard" replace />} />
      </Routes>
    </Layout>
  );
};

const App: React.FC = () => {
  return (
    <HashRouter>
      <ThemeProvider>
        <DisplaySettingsProvider>
          <SidebarProvider>
            <RegistrationProvider>
              <AuthProvider>
                <AppContent />
              </AuthProvider>
            </RegistrationProvider>
          </SidebarProvider>
        </DisplaySettingsProvider>
      </ThemeProvider>
    </HashRouter>
  );
};

export default App;
