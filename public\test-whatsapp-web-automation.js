/**
 * WhatsApp Web Automation Test
 * This file can be loaded in the browser to test WhatsApp Web automation
 */

// Test function that can be called from browser console
window.testWhatsAppAutomation = async function() {
  console.log('🤖 Starting WhatsApp Web automation test...');

  try {
    // Test 1: Check if automation API is available
    console.log('📡 Testing automation API...');
    const systemCheck = await fetch('/api/whatsapp-automation/system-check');
    const systemResult = await systemCheck.json();

    console.log('🔍 System check result:', systemResult);

    if (!systemResult.robotjsAvailable) {
      console.error('❌ WhatsApp Web automation is not available');
      return false;
    }

    // Test 2: Test basic automation functionality
    console.log('🧪 Testing automation functionality...');
    const testResponse = await fetch('/api/whatsapp-automation/test', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const testResult = await testResponse.json();
    console.log('🧪 Test result:', testResult);

    if (testResult.success) {
      console.log('✅ WhatsApp Web automation is working!');

      // Test 3: Check preferences
      console.log('⚙️ Checking automation preferences...');
      const prefsResponse = await fetch('/api/whatsapp-automation/preferences');
      const preferences = await prefsResponse.json();
      console.log('⚙️ Current preferences:', preferences);

      // Test 4: Enable automation if not enabled
      if (!preferences.enableRobotjsAutomation) {
        console.log('🔧 Enabling WhatsApp Web automation...');
        const updateResponse = await fetch('/api/whatsapp-automation/preferences', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            ...preferences,
            enableRobotjsAutomation: true
          })
        });

        const updateResult = await updateResponse.json();
        console.log('🔧 Preferences updated:', updateResult);
      }

      console.log('🎉 WhatsApp Web automation test completed successfully!');
      console.log('📋 Next steps:');
      console.log('   1. Go to Settings → WhatsApp Configuration');
      console.log('   2. Enable "WhatsApp Web Automation"');
      console.log('   3. Click "Test Automation" button');
      console.log('   4. Initialize WhatsApp Web (scan QR code)');
      console.log('   5. Create a test campaign and try automation');

      return true;
    } else {
      console.error('❌ Automation test failed:', testResult.message);
      return false;
    }

  } catch (error) {
    console.error('❌ Automation test error:', error);
    return false;
  }
};

// Test function for basic WhatsApp Web automation functionality
window.testWhatsAppWebBasic = async function() {
  console.log('🤖 Testing basic WhatsApp Web automation functionality...');

  try {
    const response = await fetch('/api/whatsapp-automation/test', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        testType: 'basic'
      })
    });

    const result = await response.json();
    console.log('🧪 Basic WhatsApp Web test result:', result);

    if (result.success) {
      console.log('✅ WhatsApp Web automation is working correctly!');
      console.log('📋 You can now use WhatsApp automation features');
    } else {
      console.error('❌ WhatsApp Web automation test failed:', result.message);
      console.log('🔧 Troubleshooting:');
      console.log('   - Make sure Puppeteer is properly installed');
      console.log('   - Check if Chrome browser is available');
      console.log('   - Try restarting the application');
    }

    return result.success;
  } catch (error) {
    console.error('❌ Error testing WhatsApp Web automation:', error);
    return false;
  }
};

// Initialize WhatsApp Web automation
window.initializeWhatsAppWeb = async function() {
  console.log('🚀 Initializing WhatsApp Web automation...');

  try {
    const response = await fetch('/api/whatsapp-automation/initialize', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const result = await response.json();
    console.log('🚀 Initialization result:', result);

    if (result.success) {
      console.log('✅ WhatsApp Web automation initialized successfully!');
      console.log('📱 Please scan the QR code in the opened browser window');
    } else {
      console.error('❌ Failed to initialize WhatsApp Web automation:', result.message);
    }

    return result.success;
  } catch (error) {
    console.error('❌ Error initializing WhatsApp Web automation:', error);
    return false;
  }
};

// Auto-run basic test when page loads
document.addEventListener('DOMContentLoaded', function() {
  console.log('🚀 WhatsApp Web automation test script loaded');
  console.log('📋 Available test functions:');
  console.log('   - testWhatsAppAutomation() - Full automation test');
  console.log('   - testWhatsAppWebBasic() - Basic WhatsApp Web test');
  console.log('   - initializeWhatsAppWeb() - Initialize WhatsApp Web');
  console.log('');
  console.log('💡 Run testWhatsAppAutomation() in console to test the automation system');
});
