{"name": "crm4ca-backend", "version": "1.0.0", "description": "Backend API server for CRM4CA application with SQLite database", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "node --watch server.js", "init-db": "node scripts/init-database.js", "migrate": "node scripts/migrate-from-localstorage.js", "seed": "node scripts/seed-database.js"}, "dependencies": {"bcryptjs": "^2.4.3", "better-sqlite3": "^11.10.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "express-rate-limit": "^7.2.0", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "luxon": "^3.6.1", "multer": "^1.4.5-lts.1", "node-cron": "^4.1.0", "nodemailer": "^7.0.3", "puppeteer": "^24.10.1", "sqlite3": "^5.1.7"}, "devDependencies": {"@types/better-sqlite3": "^7.6.13", "@types/node": "^20.12.7"}, "keywords": ["crm", "chartered-accountant", "sqlite", "api"], "author": "Your Organization", "license": "MIT"}