import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeftIcon } from '../components/icons';
import { PlaceholderManager } from '../types';
import { placeholderService } from '../services/placeholderService';

const ViewPlaceholderPage: React.FC = () => {
  const { placeholderId } = useParams<{ placeholderId: string }>();
  const navigate = useNavigate();
  const [placeholder, setPlaceholder] = useState<PlaceholderManager | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadPlaceholder = async () => {
      if (!placeholderId) {
        setError('No placeholder ID provided');
        setLoading(false);
        return;
      }

      try {
        const placeholderData = await placeholderService.getPlaceholderById(placeholderId);
        setPlaceholder(placeholderData);
      } catch (err) {
        console.error('Error loading placeholder:', err);
        setError('Failed to load placeholder details');
      } finally {
        setLoading(false);
      }
    };

    loadPlaceholder();
  }, [placeholderId]);

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not set';
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return dateString;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-textPrimary">Loading placeholder details...</div>
      </div>
    );
  }

  if (error || !placeholder) {
    return (
      <div className="min-h-screen bg-background p-6">
        <div className="max-w-4xl mx-auto">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h2 className="text-lg font-semibold text-red-800 mb-2">Error</h2>
            <p className="text-red-600">{error || 'Placeholder not found'}</p>
            <button
              onClick={() => navigate('/placeholders')}
              className="mt-4 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
            >
              Back to Placeholders
            </button>
          </div>
        </div>
      </div>
    );
  }

  const fieldClass = "block text-sm font-medium text-textPrimary mb-1";
  const valueClass = "w-full p-3 border border-border rounded-lg bg-gray-50 text-textPrimary";

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-4xl mx-auto p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate('/placeholders')}
              className="flex items-center text-primary hover:text-opacity-80"
            >
              <ArrowLeftIcon className="h-5 w-5 mr-2" />
              Back to Placeholders
            </button>
            <h1 className="text-2xl font-bold text-textPrimary">View Placeholder</h1>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => navigate(`/placeholders/edit/${placeholder.id}`)}
              className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-opacity-90"
            >
              Edit Placeholder
            </button>
          </div>
        </div>

        {/* Placeholder Details */}
        <div className="bg-cardBackground border border-border rounded-lg shadow-sm">
          <div className="p-6">
            <h2 className="text-lg font-semibold text-textPrimary mb-6">Placeholder Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Basic Information */}
              <div>
                <label className={fieldClass}>Placeholder Name</label>
                <div className={valueClass}>{placeholder.name}</div>
              </div>

              <div>
                <label className={fieldClass}>Display Name</label>
                <div className={valueClass}>{placeholder.display_name}</div>
              </div>

              <div>
                <label className={fieldClass}>Context</label>
                <div className={valueClass}>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    placeholder.context === 'campaign' ? 'bg-blue-100 text-blue-800' :
                    placeholder.context === 'signature' ? 'bg-green-100 text-green-800' :
                    'bg-purple-100 text-purple-800'
                  }`}>
                    {placeholder.context.toUpperCase()}
                  </span>
                </div>
              </div>

              <div>
                <label className={fieldClass}>Format Type</label>
                <div className={valueClass}>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    placeholder.format_type === 'text' ? 'bg-gray-100 text-gray-800' :
                    placeholder.format_type === 'date' ? 'bg-blue-100 text-blue-800' :
                    placeholder.format_type === 'currency' ? 'bg-green-100 text-green-800' :
                    placeholder.format_type === 'number' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-purple-100 text-purple-800'
                  }`}>
                    {placeholder.format_type.toUpperCase()}
                  </span>
                </div>
              </div>

              <div>
                <label className={fieldClass}>Field Path</label>
                <div className={valueClass}>{placeholder.field_path}</div>
              </div>

              <div>
                <label className={fieldClass}>Default Value</label>
                <div className={valueClass}>{placeholder.default_value || 'None'}</div>
              </div>

              <div>
                <label className={fieldClass}>Active</label>
                <div className={valueClass}>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    placeholder.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  }`}>
                    {placeholder.is_active ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>

              <div>
                <label className={fieldClass}>Created At</label>
                <div className={valueClass}>{formatDate(placeholder.created_at)}</div>
              </div>
            </div>

            {/* Description */}
            {placeholder.description && (
              <div className="mt-6">
                <label className={fieldClass}>Description</label>
                <div className="w-full p-3 border border-border rounded-lg bg-gray-50 text-textPrimary min-h-[100px] whitespace-pre-wrap">
                  {placeholder.description}
                </div>
              </div>
            )}

            {/* Usage Information */}
            <div className="mt-6">
              <label className={fieldClass}>Usage in Templates/Campaigns</label>
              <div className="w-full p-3 border border-border rounded-lg bg-blue-50 text-textPrimary">
                <p className="text-sm">
                  Use this placeholder in your content by typing: <code className="bg-white px-2 py-1 rounded font-mono text-blue-600">{'{{' + placeholder.name + '}}'}</code>
                </p>
                <p className="text-sm mt-2 text-gray-600">
                  This placeholder can be used in: <strong>{placeholder.context === 'both' ? 'Campaigns and Signatures' : placeholder.context}</strong>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewPlaceholderPage;
