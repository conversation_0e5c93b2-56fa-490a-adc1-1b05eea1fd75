# CRM SMS Gateway Android App

A dedicated Android application for sending SMS messages through your CRM system. This app integrates with your CRM backend to automatically send SMS messages and provides manual SMS sending capabilities.

## Features

- **Automatic SMS Sending**: Polls your CRM server for pending SMS messages and sends them automatically
- **Manual SMS Sending**: Send individual SMS messages manually through the app
- **Real-time Status Updates**: Reports delivery status back to your CRM system
- **LAN Network Support**: Works within your local network (same LAN as CRM system)
- **Background Operation**: Continues to work even when app is in background
- **Permission Management**: Handles SMS permissions automatically
- **Message History**: View sent/failed message history
- **Configuration Management**: Easy setup and configuration

## Requirements

- Android 6.0 (API level 23) or higher
- SMS permission
- Network access to your CRM server (same LAN)
- Minimum 50MB storage space

## Installation

### Option 1: Build from Source

1. **Prerequisites**:
   ```bash
   # Install Node.js (16 or higher)
   # Install React Native CLI
   npm install -g react-native-cli
   
   # Install Android Studio and Android SDK
   # Set up Android development environment
   ```

2. **Clone and Setup**:
   ```bash
   cd sms-android-app
   npm install
   ```

3. **Build APK**:
   ```bash
   # For debug build (easier for testing)
   npm run build-debug
   
   # For release build (optimized)
   npm run build-android
   ```

4. **Find APK**:
   - Debug APK: `android/app/build/outputs/apk/debug/app-debug.apk`
   - Release APK: `android/app/build/outputs/apk/release/app-release.apk`

### Option 2: Pre-built APK (if provided)

1. Download the APK file
2. Enable "Install from Unknown Sources" in Android settings
3. Install the APK file

## Configuration

1. **Open the app** and go to Configuration section

2. **Set Server URL**:
   ```
   http://[YOUR_CRM_SERVER_IP]:3001
   Example: http://*************:3001
   ```

3. **Set API Key** (if your CRM requires authentication)

4. **Enable Auto Mode** to automatically poll for pending messages

5. **Test Connection** to verify connectivity

## CRM Backend Integration

Your CRM system needs to provide these API endpoints:

### 1. SMS Status Endpoint
```
GET /api/sms/status
```
Returns server status for connection testing.

### 2. Pending Messages Endpoint
```
GET /api/sms/pending
```
Returns array of pending SMS messages:
```json
[
  {
    "id": "msg_123",
    "recipient": "+1234567890",
    "message": "Your message content",
    "campaignId": "camp_456"
  }
]
```

### 3. Status Report Endpoint
```
POST /api/sms/status
```
Receives delivery status reports:
```json
{
  "messageId": "msg_123",
  "status": "sent|failed",
  "error": null,
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## Usage

### Automatic Mode
1. Configure server URL and API key
2. Enable "Auto Mode"
3. App will automatically poll for pending messages every 5 seconds
4. Messages will be sent automatically and status reported back

### Manual Mode
1. Enter recipient phone number (with country code)
2. Type your message
3. Tap "Send SMS"
4. Status will be shown in message history

## Permissions

The app requires the following permissions:
- **SEND_SMS**: To send SMS messages
- **READ_SMS**: To verify message delivery
- **INTERNET**: To communicate with CRM server
- **ACCESS_NETWORK_STATE**: To check network connectivity
- **WAKE_LOCK**: To keep app active in background
- **RECEIVE_BOOT_COMPLETED**: To start automatically on device boot

## Troubleshooting

### Connection Issues
- Ensure your Android device is on the same network as CRM server
- Check if CRM server is running on port 3001
- Verify server URL format: `http://IP:PORT`
- Test with browser: open `http://[SERVER_IP]:3001` in phone browser

### SMS Not Sending
- Check SMS permissions in Android settings
- Ensure phone has SMS capability
- Check if phone number format includes country code
- Verify network connectivity

### App Not Working in Background
- Disable battery optimization for the app
- Allow app to run in background
- Check if "Auto Mode" is enabled

## Security Notes

- App communicates over HTTP within LAN (secure local network)
- API key is stored securely on device
- No SMS content is logged or stored permanently
- All communication is within your local network

## Support

For issues related to:
- **App functionality**: Check this README and troubleshooting section
- **CRM integration**: Ensure your CRM provides the required API endpoints
- **Network connectivity**: Verify both devices are on same network

## Version History

- **v1.0.0**: Initial release with basic SMS sending and CRM integration

## Technical Details

- **Framework**: React Native 0.72.6
- **SMS Library**: react-native-sms
- **Permissions**: react-native-permissions
- **Storage**: AsyncStorage for configuration
- **Network**: Fetch API for HTTP requests
- **Target SDK**: Android 33
- **Minimum SDK**: Android 23 (Android 6.0)
