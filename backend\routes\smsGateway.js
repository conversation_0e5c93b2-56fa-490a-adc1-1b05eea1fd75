const express = require('express');
const router = express.Router();
const Database = require('better-sqlite3');
const path = require('path');

// Initialize database connection
const dbPath = path.join(__dirname, '../../crm4ca.db');
const db = new Database(dbPath);

// Traccar SMS Gateway configuration
const TRACCAR_CONFIG = {
  baseUrl: 'http://*************:8080', // Replace with your Android IP
  apiKey: 'your-api-key-from-app',      // Replace with actual API key
  timeout: 10000,
  enabled: false // Set to true when configured
};

// Send SMS via Traccar Gateway
async function sendSMSViaTraccar(phone, message) {
  if (!TRACCAR_CONFIG.enabled) {
    return { success: false, error: 'Traccar not configured' };
  }

  try {
    const response = await fetch(`${TRACCAR_CONFIG.baseUrl}/send`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${TRACCAR_CONFIG.apiKey}`
      },
      body: JSON.stringify({
        to: phone,
        message: message
      }),
      timeout: TRACCAR_CONFIG.timeout
    });

    if (response.ok) {
      const result = await response.json();
      return { success: true, messageId: result.id || Date.now() };
    } else {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  } catch (error) {
    console.error('Traccar SMS failed:', error);
    return { success: false, error: error.message };
  }
}

// SMS Gateway API Routes for Android App Integration

/**
 * GET /api/sms/status
 * Health check endpoint for Android app to test connectivity
 */
router.get('/status', (req, res) => {
  try {
    // Simple health check
    const result = db.prepare('SELECT 1 as status').get();
    
    res.json({
      status: 'online',
      timestamp: new Date().toISOString(),
      server: 'CRM SMS Gateway',
      version: '1.0.0',
      database: result ? 'connected' : 'disconnected'
    });
  } catch (error) {
    console.error('SMS Gateway status check error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Server error',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/sms/pending
 * Get pending SMS messages for Android app to send
 */
router.get('/pending', (req, res) => {
  try {
    // Get pending SMS messages from campaign_subscribers table
    const pendingMessages = db.prepare(`
      SELECT 
        cs.id as messageId,
        s.phone as recipient,
        cs.personalized_sms_content as message,
        cs.campaign_id as campaignId,
        c.name as campaignName,
        s.firstName,
        s.lastName
      FROM campaign_subscribers cs
      JOIN subscribers s ON cs.subscriber_id = s.id
      JOIN campaigns c ON cs.campaign_id = c.id
      WHERE cs.status = 'pending'
        AND cs.personalized_sms_content IS NOT NULL
        AND cs.personalized_sms_content != ''
        AND s.phone IS NOT NULL
        AND s.phone != ''
        AND s.allowSms = 1
      ORDER BY cs.created_at ASC
      LIMIT 50
    `).all();

    // Format messages for Android app
    const formattedMessages = pendingMessages.map(msg => ({
      id: msg.messageId,
      recipient: msg.recipient,
      message: msg.message,
      campaignId: msg.campaignId,
      campaignName: msg.campaignName,
      subscriberName: `${msg.firstName || ''} ${msg.lastName || ''}`.trim()
    }));

    console.log(`📱 SMS Gateway: Found ${formattedMessages.length} pending SMS messages`);
    
    res.json(formattedMessages);
  } catch (error) {
    console.error('Error fetching pending SMS messages:', error);
    res.status(500).json({
      error: 'Failed to fetch pending messages',
      message: error.message
    });
  }
});

/**
 * POST /api/sms/status
 * Receive delivery status updates from Android app
 */
router.post('/status', (req, res) => {
  try {
    const { messageId, status, error, timestamp } = req.body;

    if (!messageId || !status) {
      return res.status(400).json({
        error: 'Missing required fields',
        required: ['messageId', 'status']
      });
    }

    // Update campaign_subscribers status
    const updateResult = db.prepare(`
      UPDATE campaign_subscribers 
      SET 
        status = ?,
        sent_at = CASE WHEN ? = 'sent' THEN ? ELSE sent_at END,
        error_message = ?,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `).run(
      status === 'sent' ? 'sent' : 'failed',
      status,
      status === 'sent' ? timestamp || new Date().toISOString() : null,
      error || null,
      messageId
    );

    if (updateResult.changes === 0) {
      console.warn(`📱 SMS Gateway: Message ID ${messageId} not found for status update`);
      return res.status(404).json({
        error: 'Message not found',
        messageId
      });
    }

    // Log the status update
    console.log(`📱 SMS Gateway: Message ${messageId} status updated to ${status}`);

    // If successful, also update campaign statistics
    if (status === 'sent') {
      try {
        // Get campaign ID for this message
        const messageInfo = db.prepare(`
          SELECT campaign_id FROM campaign_subscribers WHERE id = ?
        `).get(messageId);

        if (messageInfo) {
          // Update campaign sent count (this is a simple increment, you might want more sophisticated tracking)
          db.prepare(`
            UPDATE campaigns 
            SET sent_in_current_day_count = COALESCE(sent_in_current_day_count, 0) + 1,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
          `).run(messageInfo.campaign_id);
        }
      } catch (statsError) {
        console.error('Error updating campaign statistics:', statsError);
        // Don't fail the main request for stats errors
      }
    }

    res.json({
      success: true,
      messageId,
      status,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error updating SMS status:', error);
    res.status(500).json({
      error: 'Failed to update message status',
      message: error.message
    });
  }
});

/**
 * POST /api/sms/send
 * Manual SMS sending endpoint (for testing or manual sends)
 */
router.post('/send', (req, res) => {
  try {
    const { recipient, message, campaignId } = req.body;

    if (!recipient || !message) {
      return res.status(400).json({
        error: 'Missing required fields',
        required: ['recipient', 'message']
      });
    }

    // Create a manual SMS entry
    const messageId = `manual_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Insert into a manual SMS log table (you might want to create this table)
    try {
      db.prepare(`
        INSERT INTO sms_manual_log (
          id, recipient, message, campaign_id, status, created_at
        ) VALUES (?, ?, ?, ?, 'pending', CURRENT_TIMESTAMP)
      `).run(messageId, recipient, message, campaignId || null);
    } catch (dbError) {
      // If table doesn't exist, just log it
      console.log('Manual SMS log table not found, skipping database insert');
    }

    console.log(`📱 SMS Gateway: Manual SMS queued - ID: ${messageId}, Recipient: ${recipient}`);

    res.json({
      success: true,
      messageId,
      recipient,
      message: 'SMS queued for sending',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error queuing manual SMS:', error);
    res.status(500).json({
      error: 'Failed to queue SMS',
      message: error.message
    });
  }
});

/**
 * GET /api/sms/stats
 * Get SMS statistics for monitoring
 */
router.get('/stats', (req, res) => {
  try {
    const stats = {
      pending: 0,
      sent: 0,
      failed: 0,
      total: 0
    };

    // Get SMS statistics from campaign_subscribers
    const results = db.prepare(`
      SELECT 
        status,
        COUNT(*) as count
      FROM campaign_subscribers 
      WHERE personalized_sms_content IS NOT NULL 
        AND personalized_sms_content != ''
      GROUP BY status
    `).all();

    results.forEach(row => {
      stats[row.status] = row.count;
      stats.total += row.count;
    });

    // Get recent activity (last 24 hours)
    const recentActivity = db.prepare(`
      SELECT 
        status,
        COUNT(*) as count
      FROM campaign_subscribers 
      WHERE personalized_sms_content IS NOT NULL 
        AND personalized_sms_content != ''
        AND updated_at >= datetime('now', '-1 day')
      GROUP BY status
    `).all();

    const recent = { pending: 0, sent: 0, failed: 0 };
    recentActivity.forEach(row => {
      recent[row.status] = row.count;
    });

    res.json({
      overall: stats,
      last24Hours: recent,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error fetching SMS statistics:', error);
    res.status(500).json({
      error: 'Failed to fetch statistics',
      message: error.message
    });
  }
});

/**
 * POST /api/sms/traccar/config
 * Configure Traccar SMS Gateway settings
 */
router.post('/traccar/config', (req, res) => {
  try {
    const { baseUrl, apiKey, enabled } = req.body;

    if (!baseUrl || !apiKey) {
      return res.status(400).json({
        error: 'Missing required fields',
        required: ['baseUrl', 'apiKey']
      });
    }

    // Update configuration
    TRACCAR_CONFIG.baseUrl = baseUrl;
    TRACCAR_CONFIG.apiKey = apiKey;
    TRACCAR_CONFIG.enabled = enabled !== false;

    console.log(`📱 Traccar SMS Gateway configured: ${baseUrl}`);

    res.json({
      success: true,
      message: 'Traccar configuration updated',
      config: {
        baseUrl: TRACCAR_CONFIG.baseUrl,
        enabled: TRACCAR_CONFIG.enabled
      }
    });

  } catch (error) {
    console.error('Error configuring Traccar:', error);
    res.status(500).json({
      error: 'Failed to configure Traccar',
      message: error.message
    });
  }
});

/**
 * GET /api/sms/traccar/test
 * Test Traccar SMS Gateway connection
 */
router.get('/traccar/test', async (req, res) => {
  try {
    if (!TRACCAR_CONFIG.enabled) {
      return res.status(400).json({
        success: false,
        error: 'Traccar not configured'
      });
    }

    // Test connection to Traccar gateway
    const response = await fetch(`${TRACCAR_CONFIG.baseUrl}/status`, {
      headers: {
        'Authorization': `Bearer ${TRACCAR_CONFIG.apiKey}`
      },
      timeout: 5000
    });

    if (response.ok) {
      res.json({
        success: true,
        message: 'Traccar connection successful',
        status: 'connected',
        url: TRACCAR_CONFIG.baseUrl
      });
    } else {
      res.json({
        success: false,
        error: `HTTP ${response.status}: ${response.statusText}`,
        status: 'failed'
      });
    }

  } catch (error) {
    console.error('Traccar connection test failed:', error);
    res.json({
      success: false,
      error: error.message,
      status: 'error'
    });
  }
});

/**
 * POST /api/sms/traccar/send
 * Send SMS via Traccar Gateway
 */
router.post('/traccar/send', async (req, res) => {
  try {
    const { recipient, message, messageId } = req.body;

    if (!recipient || !message) {
      return res.status(400).json({
        error: 'Missing required fields',
        required: ['recipient', 'message']
      });
    }

    const result = await sendSMSViaTraccar(recipient, message);

    if (result.success) {
      // Update database if messageId provided
      if (messageId) {
        try {
          db.prepare(`
            UPDATE campaign_subscribers
            SET
              status = 'sent',
              sent_at = CURRENT_TIMESTAMP,
              updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
          `).run(messageId);
        } catch (dbError) {
          console.error('Error updating message status:', dbError);
        }
      }

      res.json({
        success: true,
        messageId: result.messageId,
        method: 'traccar',
        recipient,
        timestamp: new Date().toISOString()
      });
    } else {
      // Update database with failed status if messageId provided
      if (messageId) {
        try {
          db.prepare(`
            UPDATE campaign_subscribers
            SET
              status = 'failed',
              error_message = ?,
              updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
          `).run(result.error, messageId);
        } catch (dbError) {
          console.error('Error updating message status:', dbError);
        }
      }

      res.json({
        success: false,
        error: result.error,
        method: 'traccar',
        recipient
      });
    }

  } catch (error) {
    console.error('Error sending SMS via Traccar:', error);
    res.status(500).json({
      error: 'Failed to send SMS',
      message: error.message
    });
  }
});

/**
 * POST /api/sms/traccar/send-bulk
 * Send multiple SMS messages via Traccar Gateway
 */
router.post('/traccar/send-bulk', async (req, res) => {
  try {
    const { messages } = req.body; // Array of {messageId, recipient, message}

    if (!Array.isArray(messages) || messages.length === 0) {
      return res.status(400).json({
        error: 'Invalid messages array',
        required: 'Array of {messageId, recipient, message}'
      });
    }

    const results = [];
    let sent = 0;
    let failed = 0;

    for (const msg of messages) {
      const result = await sendSMSViaTraccar(msg.recipient, msg.message);

      if (result.success) {
        sent++;
        // Update database
        if (msg.messageId) {
          try {
            db.prepare(`
              UPDATE campaign_subscribers
              SET
                status = 'sent',
                sent_at = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP
              WHERE id = ?
            `).run(msg.messageId);
          } catch (dbError) {
            console.error('Error updating message status:', dbError);
          }
        }
      } else {
        failed++;
        // Update database with failed status
        if (msg.messageId) {
          try {
            db.prepare(`
              UPDATE campaign_subscribers
              SET
                status = 'failed',
                error_message = ?,
                updated_at = CURRENT_TIMESTAMP
              WHERE id = ?
            `).run(result.error, msg.messageId);
          } catch (dbError) {
            console.error('Error updating message status:', dbError);
          }
        }
      }

      results.push({
        messageId: msg.messageId,
        recipient: msg.recipient,
        success: result.success,
        error: result.error,
        traccarMessageId: result.messageId
      });

      // Small delay between messages to avoid overwhelming the gateway
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log(`📱 Traccar Bulk SMS: Sent ${sent}, Failed ${failed}`);

    res.json({
      success: true,
      summary: { sent, failed, total: messages.length },
      results,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error sending bulk SMS via Traccar:', error);
    res.status(500).json({
      error: 'Failed to send bulk SMS',
      message: error.message
    });
  }
});

module.exports = router;
