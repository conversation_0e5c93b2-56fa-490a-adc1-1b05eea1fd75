import express from 'express';
import Database from 'better-sqlite3';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const router = express.Router();
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Initialize database connection
const dbPath = join(__dirname, '../../crm4ca.db');
const db = new Database(dbPath);

// MyPhoneExplorer Configuration
const MYPHONEEXPLORER_CONFIG = {
  executablePath: 'C:\\Program Files (x86)\\MyPhoneExplorer\\MyPhoneExplorer.exe',
  alternativePaths: [
    'C:\\Program Files\\MyPhoneExplorer\\MyPhoneExplorer.exe',
    'MyPhoneExplorer.exe' // If in PATH
  ],
  enabled: true,
  phoneNumber: '',
  timeout: 30000
};

// Send SMS via Traccar Gateway
async function sendSMSViaTraccar(phone, message) {
  if (!TRACCAR_CONFIG.enabled) {
    console.log('❌ Traccar not enabled');
    return { success: false, error: '<PERSON><PERSON><PERSON> not configured' };
  }

  const sendUrl = `${TRACCAR_CONFIG.baseUrl}/send`;
  console.log(`📱 Sending SMS via Traccar to ${phone}`);
  console.log(`🔗 URL: ${sendUrl}`);
  console.log(`🔑 API Key: ${TRACCAR_CONFIG.apiKey.substring(0, 8)}...`);

  try {
    // Traccar SMS Gateway expects specific parameter format
    const requestBody = {
      phone: phone,  // Try 'phone' instead of 'to'
      message: message
    };

    // Also try the alternative format
    const alternativeBody = {
      to: phone,
      text: message  // Try 'text' instead of 'message'
    };

    console.log('📦 Request body:', requestBody);

    // Traccar SMS Gateway uses token-based authentication
    const headers = {
      'Content-Type': 'application/json'
    };

    // Traccar expects the token as a query parameter, not in headers
    const tokenParam = `?token=${TRACCAR_CONFIG.apiKey}`;
    const finalUrl = sendUrl + tokenParam;

    console.log('🔗 Final URL with token:', finalUrl.replace(TRACCAR_CONFIG.apiKey, '***'));
    console.log('🔑 Headers:', headers);
    console.log('📦 Request body (primary):', requestBody);

    let response = await fetch(finalUrl, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestBody),
      timeout: TRACCAR_CONFIG.timeout
    });

    console.log(`📊 Traccar response (primary): ${response.status} ${response.statusText}`);

    // If first attempt fails with 401, try alternative format
    if (response.status === 401) {
      console.log('🔄 Trying alternative request format...');
      console.log('📦 Request body (alternative):', alternativeBody);

      response = await fetch(finalUrl, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(alternativeBody),
        timeout: TRACCAR_CONFIG.timeout
      });

      console.log(`📊 Traccar response (alternative): ${response.status} ${response.statusText}`);
    }

    if (response.ok) {
      const result = await response.json();
      console.log('✅ Traccar success:', result);
      return { success: true, messageId: result.id || Date.now() };
    } else {
      const errorText = await response.text();
      console.log('❌ Traccar error response:', errorText);
      throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
    }
  } catch (error) {
    console.error('💥 Traccar SMS failed:', error);
    return { success: false, error: error.message };
  }
}

// Send SMS via MyPhoneExplorer
async function sendSMSViaMyPhoneExplorer(phone, message) {
  console.log(`📞 Sending SMS via MyPhoneExplorer to ${phone}`);
  console.log(`📱 Message: ${message.substring(0, 50)}...`);

  try {
    // Import child_process for executing MyPhoneExplorer
    const { exec } = await import('child_process');
    const { promisify } = await import('util');
    const execAsync = promisify(exec);

    // MyPhoneExplorer executable paths to try
    const possiblePaths = [
      'C:\\Program Files (x86)\\MyPhoneExplorer\\MyPhoneExplorer.exe',
      'C:\\Program Files\\MyPhoneExplorer\\MyPhoneExplorer.exe',
      'MyPhoneExplorer.exe' // If in PATH
    ];

    let executablePath = null;

    // Find the correct MyPhoneExplorer path
    for (const path of possiblePaths) {
      try {
        await execAsync(`"${path}" /?`); // Test if executable exists
        executablePath = path;
        break;
      } catch (error) {
        // Continue to next path
      }
    }

    if (!executablePath) {
      throw new Error('MyPhoneExplorer not found. Please install MyPhoneExplorer or check installation path.');
    }

    console.log(`📞 Using MyPhoneExplorer at: ${executablePath}`);

    // Clean phone number (remove any non-digits except +)
    const cleanPhone = phone.replace(/[^\d+]/g, '');

    // Escape message for command line
    const escapedMessage = message.replace(/"/g, '\\"');

    // Build MyPhoneExplorer command
    const command = `"${executablePath}" action=sendmessage savetosent=1 number=${cleanPhone} text="${escapedMessage}"`;

    console.log(`📞 Executing command: ${command.replace(escapedMessage, '[MESSAGE]')}`);

    // Execute MyPhoneExplorer command
    const { stdout, stderr } = await execAsync(command, {
      timeout: 30000, // 30 second timeout
      windowsHide: true
    });

    console.log(`📞 MyPhoneExplorer stdout:`, stdout);
    if (stderr) {
      console.log(`📞 MyPhoneExplorer stderr:`, stderr);
    }

    // MyPhoneExplorer doesn't return detailed status, so we assume success if no error
    console.log(`✅ MyPhoneExplorer SMS sent successfully to ${phone}`);

    return {
      success: true,
      messageId: `mpe_${Date.now()}`,
      method: 'MyPhoneExplorer',
      stdout: stdout,
      stderr: stderr
    };

  } catch (error) {
    console.error(`💥 MyPhoneExplorer SMS failed for ${phone}:`, error);

    return {
      success: false,
      error: error.message,
      method: 'MyPhoneExplorer'
    };
  }
}

// SMS Gateway API Routes for Android App Integration

/**
 * GET /api/sms/status
 * Health check endpoint for Android app to test connectivity
 */
router.get('/status', (req, res) => {
  try {
    // Simple health check
    const result = db.prepare('SELECT 1 as status').get();
    
    res.json({
      status: 'online',
      timestamp: new Date().toISOString(),
      server: 'CRM SMS Gateway',
      version: '1.0.0',
      database: result ? 'connected' : 'disconnected'
    });
  } catch (error) {
    console.error('SMS Gateway status check error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Server error',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/sms/pending
 * Get pending SMS messages for Android app to send
 */
router.get('/pending', (req, res) => {
  try {
    let formattedMessages = [];

    try {
      // Try to get pending SMS messages from campaign_subscribers table
      const pendingMessages = db.prepare(`
        SELECT
          cs.id as messageId,
          s.phone as recipient,
          cs.personalized_sms_content as message,
          cs.campaign_id as campaignId,
          c.name as campaignName,
          s.firstName,
          s.lastName
        FROM campaign_subscribers cs
        JOIN subscribers s ON cs.subscriber_id = s.id
        JOIN campaigns c ON cs.campaign_id = c.id
        WHERE cs.status = 'pending'
          AND cs.personalized_sms_content IS NOT NULL
          AND cs.personalized_sms_content != ''
          AND s.phone IS NOT NULL
          AND s.phone != ''
          AND s.allowSms = 1
        ORDER BY cs.created_at ASC
        LIMIT 50
      `).all();

      // Format messages for Android app
      formattedMessages = pendingMessages.map(msg => ({
        id: msg.messageId,
        recipient: msg.recipient,
        message: msg.message,
        campaignId: msg.campaignId,
        campaignName: msg.campaignName,
        subscriberName: `${msg.firstName || ''} ${msg.lastName || ''}`.trim()
      }));

    } catch (tableError) {
      // If campaign_subscribers table doesn't exist, create sample data for testing
      console.log('📱 campaign_subscribers table not found, creating sample SMS data for testing...');

      // Get subscribers with phone numbers for testing
      const subscribers = db.prepare(`
        SELECT id, firstName, lastName, phone, email
        FROM subscribers
        WHERE phone IS NOT NULL AND phone != '' AND allowSms = 1
        LIMIT 3
      `).all();

      // Create sample SMS messages for testing
      formattedMessages = subscribers.map((subscriber, index) => ({
        id: `test_sms_${Date.now()}_${index}`,
        recipient: subscriber.phone,
        message: `Hello ${subscriber.firstName}, this is a test SMS from your CRM system! This is message #${index + 1}.`,
        campaignId: 'test_campaign',
        campaignName: 'Test SMS Campaign',
        subscriberName: `${subscriber.firstName || ''} ${subscriber.lastName || ''}`.trim()
      }));
    }

    console.log(`📱 SMS Gateway: Found ${formattedMessages.length} pending SMS messages`);

    res.json(formattedMessages);
  } catch (error) {
    console.error('Error fetching pending SMS messages:', error);
    res.status(500).json({
      error: 'Failed to fetch pending messages',
      message: error.message
    });
  }
});

/**
 * POST /api/sms/status
 * Receive delivery status updates from Android app
 */
router.post('/status', (req, res) => {
  try {
    const { messageId, status, error, timestamp } = req.body;

    if (!messageId || !status) {
      return res.status(400).json({
        error: 'Missing required fields',
        required: ['messageId', 'status']
      });
    }

    // Update campaign_subscribers status
    const updateResult = db.prepare(`
      UPDATE campaign_subscribers 
      SET 
        status = ?,
        sent_at = CASE WHEN ? = 'sent' THEN ? ELSE sent_at END,
        error_message = ?,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `).run(
      status === 'sent' ? 'sent' : 'failed',
      status,
      status === 'sent' ? timestamp || new Date().toISOString() : null,
      error || null,
      messageId
    );

    if (updateResult.changes === 0) {
      console.warn(`📱 SMS Gateway: Message ID ${messageId} not found for status update`);
      return res.status(404).json({
        error: 'Message not found',
        messageId
      });
    }

    // Log the status update
    console.log(`📱 SMS Gateway: Message ${messageId} status updated to ${status}`);

    // If successful, also update campaign statistics
    if (status === 'sent') {
      try {
        // Get campaign ID for this message
        const messageInfo = db.prepare(`
          SELECT campaign_id FROM campaign_subscribers WHERE id = ?
        `).get(messageId);

        if (messageInfo) {
          // Update campaign sent count (this is a simple increment, you might want more sophisticated tracking)
          db.prepare(`
            UPDATE campaigns 
            SET sent_in_current_day_count = COALESCE(sent_in_current_day_count, 0) + 1,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
          `).run(messageInfo.campaign_id);
        }
      } catch (statsError) {
        console.error('Error updating campaign statistics:', statsError);
        // Don't fail the main request for stats errors
      }
    }

    res.json({
      success: true,
      messageId,
      status,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error updating SMS status:', error);
    res.status(500).json({
      error: 'Failed to update message status',
      message: error.message
    });
  }
});

/**
 * POST /api/sms/send
 * Manual SMS sending endpoint (for testing or manual sends)
 */
router.post('/send', (req, res) => {
  try {
    const { recipient, message, campaignId } = req.body;

    if (!recipient || !message) {
      return res.status(400).json({
        error: 'Missing required fields',
        required: ['recipient', 'message']
      });
    }

    // Create a manual SMS entry
    const messageId = `manual_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Insert into a manual SMS log table (you might want to create this table)
    try {
      db.prepare(`
        INSERT INTO sms_manual_log (
          id, recipient, message, campaign_id, status, created_at
        ) VALUES (?, ?, ?, ?, 'pending', CURRENT_TIMESTAMP)
      `).run(messageId, recipient, message, campaignId || null);
    } catch (dbError) {
      // If table doesn't exist, just log it
      console.log('Manual SMS log table not found, skipping database insert');
    }

    console.log(`📱 SMS Gateway: Manual SMS queued - ID: ${messageId}, Recipient: ${recipient}`);

    res.json({
      success: true,
      messageId,
      recipient,
      message: 'SMS queued for sending',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error queuing manual SMS:', error);
    res.status(500).json({
      error: 'Failed to queue SMS',
      message: error.message
    });
  }
});

/**
 * GET /api/sms/stats
 * Get SMS statistics for monitoring
 */
router.get('/stats', (req, res) => {
  try {
    let stats = {
      pending: 0,
      sent: 0,
      failed: 0,
      total: 0
    };

    let recent = { pending: 0, sent: 0, failed: 0 };

    try {
      // Get SMS statistics from campaign_subscribers
      const results = db.prepare(`
        SELECT
          status,
          COUNT(*) as count
        FROM campaign_subscribers
        WHERE personalized_sms_content IS NOT NULL
          AND personalized_sms_content != ''
        GROUP BY status
      `).all();

      results.forEach(row => {
        stats[row.status] = row.count;
        stats.total += row.count;
      });

      // Get recent activity (last 24 hours)
      const recentActivity = db.prepare(`
        SELECT
          status,
          COUNT(*) as count
        FROM campaign_subscribers
        WHERE personalized_sms_content IS NOT NULL
          AND personalized_sms_content != ''
          AND updated_at >= datetime('now', '-1 day')
        GROUP BY status
      `).all();

      recentActivity.forEach(row => {
        recent[row.status] = row.count;
      });

    } catch (tableError) {
      // If campaign_subscribers table doesn't exist, provide sample stats
      console.log('📱 campaign_subscribers table not found, providing sample statistics...');

      // Get count of subscribers with SMS enabled for sample stats
      const subscriberCount = db.prepare(`
        SELECT COUNT(*) as count
        FROM subscribers
        WHERE phone IS NOT NULL AND phone != '' AND allowSms = 1
      `).get();

      stats = {
        pending: subscriberCount.count || 0,
        sent: 0,
        failed: 0,
        total: subscriberCount.count || 0
      };

      recent = { pending: subscriberCount.count || 0, sent: 0, failed: 0 };
    }

    res.json({
      overall: stats,
      last24Hours: recent,
      timestamp: new Date().toISOString(),
      note: stats.total === 0 ? 'No SMS campaigns found. Create a campaign to see statistics.' : null
    });

  } catch (error) {
    console.error('Error fetching SMS statistics:', error);
    res.status(500).json({
      error: 'Failed to fetch statistics',
      message: error.message
    });
  }
});

/**
 * POST /api/sms/traccar/config
 * Configure Traccar SMS Gateway settings
 */
router.post('/traccar/config', (req, res) => {
  try {
    const { baseUrl, apiKey, enabled } = req.body;

    if (!baseUrl || !apiKey) {
      return res.status(400).json({
        error: 'Missing required fields',
        required: ['baseUrl', 'apiKey']
      });
    }

    // Update configuration
    TRACCAR_CONFIG.baseUrl = baseUrl;
    TRACCAR_CONFIG.apiKey = apiKey;
    TRACCAR_CONFIG.enabled = enabled !== false;

    console.log(`📱 Traccar SMS Gateway configured: ${baseUrl}`);

    res.json({
      success: true,
      message: 'Traccar configuration updated',
      config: {
        baseUrl: TRACCAR_CONFIG.baseUrl,
        enabled: TRACCAR_CONFIG.enabled
      }
    });

  } catch (error) {
    console.error('Error configuring Traccar:', error);
    res.status(500).json({
      error: 'Failed to configure Traccar',
      message: error.message
    });
  }
});

/**
 * GET /api/sms/traccar/test
 * Test Traccar SMS Gateway connection
 */
router.get('/traccar/test', async (req, res) => {
  try {
    if (!TRACCAR_CONFIG.enabled || !TRACCAR_CONFIG.baseUrl || !TRACCAR_CONFIG.apiKey) {
      return res.status(200).json({
        success: false,
        error: 'Traccar not configured. Please configure Traccar settings first.',
        status: 'not_configured',
        details: {
          enabled: TRACCAR_CONFIG.enabled,
          hasBaseUrl: !!TRACCAR_CONFIG.baseUrl,
          hasApiKey: !!TRACCAR_CONFIG.apiKey
        }
      });
    }

    // Test connection to Traccar gateway
    const response = await fetch(`${TRACCAR_CONFIG.baseUrl}/status`, {
      headers: {
        'Authorization': `Bearer ${TRACCAR_CONFIG.apiKey}`
      },
      timeout: 5000
    });

    if (response.ok) {
      res.json({
        success: true,
        message: 'Traccar connection successful',
        status: 'connected',
        url: TRACCAR_CONFIG.baseUrl
      });
    } else {
      res.json({
        success: false,
        error: `HTTP ${response.status}: ${response.statusText}`,
        status: 'failed'
      });
    }

  } catch (error) {
    console.error('Traccar connection test failed:', error);
    res.json({
      success: false,
      error: error.message,
      status: 'error'
    });
  }
});

/**
 * POST /api/sms/traccar/send
 * Send SMS via Traccar Gateway
 */
router.post('/traccar/send', async (req, res) => {
  try {
    const { recipient, message, messageId } = req.body;

    if (!recipient || !message) {
      return res.status(400).json({
        error: 'Missing required fields',
        required: ['recipient', 'message']
      });
    }

    const result = await sendSMSViaTraccar(recipient, message);

    if (result.success) {
      // Update database if messageId provided
      if (messageId) {
        try {
          db.prepare(`
            UPDATE campaign_subscribers
            SET
              status = 'sent',
              sent_at = CURRENT_TIMESTAMP,
              updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
          `).run(messageId);
        } catch (dbError) {
          console.error('Error updating message status:', dbError);
        }
      }

      res.json({
        success: true,
        messageId: result.messageId,
        method: 'traccar',
        recipient,
        timestamp: new Date().toISOString()
      });
    } else {
      // Update database with failed status if messageId provided
      if (messageId) {
        try {
          db.prepare(`
            UPDATE campaign_subscribers
            SET
              status = 'failed',
              error_message = ?,
              updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
          `).run(result.error, messageId);
        } catch (dbError) {
          console.error('Error updating message status:', dbError);
        }
      }

      res.json({
        success: false,
        error: result.error,
        method: 'traccar',
        recipient
      });
    }

  } catch (error) {
    console.error('Error sending SMS via Traccar:', error);
    res.status(500).json({
      error: 'Failed to send SMS',
      message: error.message
    });
  }
});

/**
 * POST /api/sms/traccar/send-bulk
 * Send multiple SMS messages via Traccar Gateway
 */
router.post('/traccar/send-bulk', async (req, res) => {
  try {
    const { messages } = req.body; // Array of {messageId, recipient, message}

    if (!Array.isArray(messages) || messages.length === 0) {
      return res.status(400).json({
        error: 'Invalid messages array',
        required: 'Array of {messageId, recipient, message}'
      });
    }

    const results = [];
    let sent = 0;
    let failed = 0;

    for (const msg of messages) {
      const result = await sendSMSViaTraccar(msg.recipient, msg.message);

      if (result.success) {
        sent++;
        // Update database
        if (msg.messageId) {
          try {
            db.prepare(`
              UPDATE campaign_subscribers
              SET
                status = 'sent',
                sent_at = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP
              WHERE id = ?
            `).run(msg.messageId);
          } catch (dbError) {
            console.error('Error updating message status:', dbError);
          }
        }
      } else {
        failed++;
        // Update database with failed status
        if (msg.messageId) {
          try {
            db.prepare(`
              UPDATE campaign_subscribers
              SET
                status = 'failed',
                error_message = ?,
                updated_at = CURRENT_TIMESTAMP
              WHERE id = ?
            `).run(result.error, msg.messageId);
          } catch (dbError) {
            console.error('Error updating message status:', dbError);
          }
        }
      }

      results.push({
        messageId: msg.messageId,
        recipient: msg.recipient,
        success: result.success,
        error: result.error,
        traccarMessageId: result.messageId
      });

      // Small delay between messages to avoid overwhelming the gateway
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log(`📱 Traccar Bulk SMS: Sent ${sent}, Failed ${failed}`);

    res.json({
      success: true,
      summary: { sent, failed, total: messages.length },
      results,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error sending bulk SMS via Traccar:', error);
    res.status(500).json({
      error: 'Failed to send bulk SMS',
      message: error.message
    });
  }
});

/**
 * POST /api/sms/myphoneexplorer/send-bulk
 * Send multiple SMS messages via MyPhoneExplorer
 */
router.post('/myphoneexplorer/send-bulk', async (req, res) => {
  try {
    const { messages } = req.body; // Array of {messageId, recipient, message}

    if (!Array.isArray(messages) || messages.length === 0) {
      return res.status(400).json({
        error: 'Invalid messages array',
        required: 'Array of {messageId, recipient, message}'
      });
    }

    console.log(`📞 MyPhoneExplorer: Processing ${messages.length} SMS messages`);

    const results = [];
    let sent = 0;
    let failed = 0;

    for (const msg of messages) {
      const result = await sendSMSViaMyPhoneExplorer(msg.recipient, msg.message);

      if (result.success) {
        sent++;
        // Update database
        if (msg.messageId) {
          try {
            db.prepare(`
              UPDATE campaign_subscribers
              SET
                status = 'sent',
                sent_at = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP
              WHERE id = ?
            `).run(msg.messageId);
          } catch (dbError) {
            console.error('Error updating message status:', dbError);
          }
        }
      } else {
        failed++;
        // Update database with failed status
        if (msg.messageId) {
          try {
            db.prepare(`
              UPDATE campaign_subscribers
              SET
                status = 'failed',
                error_message = ?,
                updated_at = CURRENT_TIMESTAMP
              WHERE id = ?
            `).run(result.error, msg.messageId);
          } catch (dbError) {
            console.error('Error updating message status:', dbError);
          }
        }
      }

      results.push({
        messageId: msg.messageId,
        recipient: msg.recipient,
        success: result.success,
        error: result.error
      });

      // Small delay between messages to avoid overwhelming MyPhoneExplorer
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    console.log(`📞 MyPhoneExplorer Bulk SMS: Sent ${sent}, Failed ${failed}`);

    res.json({
      success: true,
      summary: { sent, failed, total: messages.length },
      results,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error sending bulk SMS via MyPhoneExplorer:', error);
    res.status(500).json({
      error: 'Failed to send bulk SMS via MyPhoneExplorer',
      message: error.message
    });
  }
});

export default router;
