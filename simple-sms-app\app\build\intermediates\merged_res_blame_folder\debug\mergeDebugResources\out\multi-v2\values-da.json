{"logs": [{"outputFile": "com.crmsms.app-mergeDebugResources-26:/values-da/values-da.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c49fe9f01bdf72f5546a74fa012334ab\\transformed\\appcompat-1.6.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,867,958,1051,1144,1238,1332,1425,1520,1618,1709,1800,1879,1987,2094,2190,2303,2406,2507,2660,2757", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "200,294,410,495,595,708,786,862,953,1046,1139,1233,1327,1420,1515,1613,1704,1795,1874,1982,2089,2185,2298,2401,2502,2655,2752,2832"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,422,516,632,717,817,930,1008,1084,1175,1268,1361,1455,1549,1642,1737,1835,1926,2017,2096,2204,2311,2407,2520,2623,2724,2877,7830", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "417,511,627,712,812,925,1003,1079,1170,1263,1356,1450,1544,1637,1732,1830,1921,2012,2091,2199,2306,2402,2515,2618,2719,2872,2969,7905"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3de57968c40646ce2b4050186252d063\\transformed\\material-1.8.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,350,426,504,601,681,781,930,1008,1072,1158,1231,1291,1378,1442,1504,1566,1634,1699,1755,1873,1931,1992,2048,2123,2249,2335,2415,2556,2634,2714,2800,2856,2907,2973,3041,3115,3204,3276,3354,3424,3497,3581,3658,3746,3835,3909,3982,4067,4116,4182,4262,4345,4407,4471,4534,4642,4737,4838,4933,4993,5048", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,77,75,77,96,79,99,148,77,63,85,72,59,86,63,61,61,67,64,55,117,57,60,55,74,125,85,79,140,77,79,85,55,50,65,67,73,88,71,77,69,72,83,76,87,88,73,72,84,48,65,79,82,61,63,62,107,94,100,94,59,54,79", "endOffsets": "267,345,421,499,596,676,776,925,1003,1067,1153,1226,1286,1373,1437,1499,1561,1629,1694,1750,1868,1926,1987,2043,2118,2244,2330,2410,2551,2629,2709,2795,2851,2902,2968,3036,3110,3199,3271,3349,3419,3492,3576,3653,3741,3830,3904,3977,4062,4111,4177,4257,4340,4402,4466,4529,4637,4732,4833,4928,4988,5043,5123"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2974,3052,3128,3206,3303,3383,3483,3632,3710,3774,3860,3933,3993,4080,4144,4206,4268,4336,4401,4457,4575,4633,4694,4750,4825,4951,5037,5117,5258,5336,5416,5502,5558,5609,5675,5743,5817,5906,5978,6056,6126,6199,6283,6360,6448,6537,6611,6684,6769,6818,6884,6964,7047,7109,7173,7236,7344,7439,7540,7635,7695,7750", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "endColumns": "12,77,75,77,96,79,99,148,77,63,85,72,59,86,63,61,61,67,64,55,117,57,60,55,74,125,85,79,140,77,79,85,55,50,65,67,73,88,71,77,69,72,83,76,87,88,73,72,84,48,65,79,82,61,63,62,107,94,100,94,59,54,79", "endOffsets": "317,3047,3123,3201,3298,3378,3478,3627,3705,3769,3855,3928,3988,4075,4139,4201,4263,4331,4396,4452,4570,4628,4689,4745,4820,4946,5032,5112,5253,5331,5411,5497,5553,5604,5670,5738,5812,5901,5973,6051,6121,6194,6278,6355,6443,6532,6606,6679,6764,6813,6879,6959,7042,7104,7168,7231,7339,7434,7535,7630,7690,7745,7825"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\6325cad999e7e25eaa9301d33937688b\\transformed\\core-1.9.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "96", "startColumns": "4", "startOffsets": "7910", "endColumns": "100", "endOffsets": "8006"}}]}]}