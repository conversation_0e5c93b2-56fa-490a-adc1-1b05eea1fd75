-- Add channel-specific columns to campaigns table
-- This script adds the missing columns for WhatsApp and SMS channel handling

-- Add recipient count columns for each channel
ALTER TABLE campaigns ADD COLUMN email_recipients_count INTEGER DEFAULT 0;
ALTER TABLE campaigns ADD COLUMN whatsapp_recipients_count INTEGER DEFAULT 0;
ALTER TABLE campaigns ADD COLUMN sms_recipients_count INTEGER DEFAULT 0;

-- Add channel enabled flags
ALTER TABLE campaigns ADD COLUMN email_enabled BOOLEAN DEFAULT 1;
ALTER TABLE campaigns ADD COLUMN whatsapp_enabled BOOLEAN DEFAULT 1;
ALTER TABLE campaigns ADD COLUMN sms_enabled BOOLEAN DEFAULT 1;

-- Update existing campaigns with default values
UPDATE campaigns 
SET 
  email_recipients_count = COALESCE(email_recipients_count, total_recipients),
  whatsapp_recipients_count = COALESCE(whatsapp_recipients_count, 0),
  sms_recipients_count = COALESCE(sms_recipients_count, 0),
  email_enabled = COALESCE(email_enabled, 1),
  whatsapp_enabled = COALESCE(whatsapp_enabled, 1),
  sms_enabled = COALESCE(sms_enabled, 1)
WHERE 
  email_recipients_count IS NULL OR
  whatsapp_recipients_count IS NULL OR
  sms_recipients_count IS NULL OR
  email_enabled IS NULL OR
  whatsapp_enabled IS NULL OR
  sms_enabled IS NULL;
