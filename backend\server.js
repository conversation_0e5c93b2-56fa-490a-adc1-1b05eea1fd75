import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { networkInterfaces } from 'os';
import dotenv from 'dotenv';

// Import route handlers
import campaignRoutes from './routes/campaigns.js';
import campaignQueueRoutes from './routes/campaign-queue.js';
import subscriberRoutes from './routes/subscribers.js';
import templateRoutes from './routes/templates.js';
import signatureRoutes from './routes/signatures.js';
import userRoutes from './routes/users.js';
import areasOfInterestRoutes from './routes/areasOfInterest.js';
import placeholderRoutes from './routes/placeholders.js';
import settingsRoutes from './routes/settings.js';
import auditRoutes from './routes/audit.js';
import authRoutes from './routes/auth.js';
import overdueCampaignRoutes from './routes/overdue-campaigns.js';
import backupRoutes from './routes/backup-routes.js';
import databaseBackupRoutes from './routes/database-backup.js';
import simpleCampaignRoutes from './routes/simple-campaigns.js';
import whatsappRoutes from './routes/whatsapp.js';

// Import database initialization
import { initializeDatabase } from './database/init.js';
import { database } from './database/connection.js';
import { EnhancedTemplateScheduler } from './services/enhanced-template-scheduler.js';
import { campaignExecutionService } from './services/campaign-execution-service.js';
import { campaignSendingService } from './services/campaign-sending-service.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Global scheduler instance
let globalScheduler = null;

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Security middleware
app.use(helmet({
  crossOriginEmbedderPolicy: false,
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https:"],
      scriptSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "http://localhost:*", "http://192.168.1.*", "http://10.*", "http://172.16.*", "https:"],
    },
  },
}));

// Rate limiting - More permissive for development
const limiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute window
  max: 500, // Limit each IP to 500 requests per minute (more permissive)
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    // Skip rate limiting for development environments
    return process.env.NODE_ENV === 'development' || req.ip === '127.0.0.1' || req.ip === '::1';
  }
});

app.use(limiter);

// Compression middleware
app.use(compression());

// CORS configuration
app.use(cors({
  origin: [
    // Localhost variants
    'http://localhost:3000',
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:5175',
    'http://localhost:5176',
    'http://localhost:5177',
    'http://localhost:5178',
    'http://localhost:4173',
    'http://127.0.0.1:3000',
    'http://127.0.0.1:5173',
    'http://127.0.0.1:5174',
    'http://127.0.0.1:5175',
    'http://127.0.0.1:5176',
    'http://127.0.0.1:5177',
    'http://127.0.0.1:5178',
    'http://127.0.0.1:4173',
    // Specific network IP
    'http://************',
    'http://************:80',
    'http://************:5173',
    'http://************:5174',
    'http://************:5175',
    'http://************:5176',
    'http://************:5177',
    'http://************:5178',
    'http://************:3000',
    'http://************/crm',
    // Allow all local network IPs for development
    /^http:\/\/192\.168\.1\.\d+/,
    /^http:\/\/192\.168\.1\.\d+:\d+/,
    /^http:\/\/10\.\d+\.\d+\.\d+/,
    /^http:\/\/10\.\d+\.\d+\.\d+:\d+/,
    /^http:\/\/172\.16\.\d+\.\d+/,
    /^http:\/\/172\.16\.\d+\.\d+:\d+/,
    /^http:\/\/localhost:\d+/
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Body parsing middleware
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    database: 'SQLite'
  });
});

// Server info endpoint for dashboard
app.get('/api/server-info', (req, res) => {
  const hostname = req.get('host')?.split(':')[0] || 'localhost';
  res.status(200).json({
    backendPort: PORT,
    frontendPort: process.env.VITE_PORT || 5176,
    hostname: hostname,
    apiBaseUrl: `http://${hostname}:${PORT}/api`,
    frontendUrl: `http://${hostname}:${process.env.VITE_PORT || 5176}`,
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/campaigns', campaignRoutes);
app.use('/api/simple-campaigns', simpleCampaignRoutes);
app.use('/api/campaign-queue', campaignQueueRoutes);
app.use('/api/overdue-campaigns', overdueCampaignRoutes);

// Manual scheduler trigger endpoint
app.post('/api/trigger-scheduler', async (req, res) => {
  try {
    if (globalScheduler) {
      await globalScheduler.triggerQueueCheck();
      res.json({ success: true, message: 'Enhanced scheduler executed successfully' });
    } else {
      res.status(500).json({ success: false, message: 'Scheduler not initialized' });
    }
  } catch (error) {
    console.error('Error triggering scheduler:', error);
    res.status(500).json({ success: false, message: 'Failed to execute scheduler' });
  }
});

// Manual campaign execution trigger endpoint
app.post('/api/trigger-campaign-execution', async (req, res) => {
  try {
    await campaignExecutionService.triggerExecution();
    res.json({ success: true, message: 'Campaign execution triggered successfully' });
  } catch (error) {
    console.error('Error triggering campaign execution:', error);
    res.status(500).json({ success: false, message: 'Failed to trigger campaign execution' });
  }
});

// Campaign execution statistics endpoint
app.get('/api/campaign-execution-stats', async (req, res) => {
  try {
    const stats = await campaignExecutionService.getExecutionStats();
    res.json(stats);
  } catch (error) {
    console.error('Error getting execution stats:', error);
    res.status(500).json({ error: 'Failed to get execution statistics' });
  }
});

// Manual campaign sending trigger endpoint
app.post('/api/trigger-campaign-sending', async (req, res) => {
  try {
    await campaignSendingService.triggerSending();
    res.json({ success: true, message: 'Campaign sending triggered successfully' });
  } catch (error) {
    console.error('Error triggering campaign sending:', error);
    res.status(500).json({ success: false, message: 'Failed to trigger campaign sending' });
  }
});

// Campaign sending statistics endpoint
app.get('/api/campaign-sending-stats', async (req, res) => {
  try {
    const stats = await campaignSendingService.getSendingStats();
    res.json(stats);
  } catch (error) {
    console.error('Error getting sending stats:', error);
    res.status(500).json({ error: 'Failed to get sending statistics' });
  }
});

// Active sending operations endpoint
app.get('/api/active-sends', async (req, res) => {
  try {
    const activeSends = campaignSendingService.getActiveSends();
    res.json(activeSends);
  } catch (error) {
    console.error('Error getting active sends:', error);
    res.status(500).json({ error: 'Failed to get active sending operations' });
  }
});

// Schema update endpoint
app.post('/api/update-campaign-schema', async (req, res) => {
  try {
    await updateCampaignSchema();
    res.json({ success: true, message: 'Campaign schema updated successfully' });
  } catch (error) {
    console.error('Error updating campaign schema:', error);
    res.status(500).json({ success: false, message: 'Failed to update campaign schema', error: error.message });
  }
});

// Campaign sending logs endpoints
app.get('/api/campaign-sending-logs', async (req, res) => {
  try {
    const logs = await database.all(`
      SELECT 
        csl.id,
        csl.campaign_id,
        csl.campaign_name,
        csl.recipient_id,
        csl.recipient_name,
        csl.recipient_email,
        csl.recipient_phone,
        csl.recipient_whatsapp,
        csl.channel,
        csl.status,
        csl.sent_at,
        csl.error_message,
        csl.message_id,
        csl.message_content,
        csl.created_at,
        csl.updated_at,
        c.created_at as campaign_created_at
      FROM campaign_sending_logs csl
      LEFT JOIN campaigns c ON csl.campaign_id = c.id
      ORDER BY csl.sent_at DESC, csl.campaign_id ASC
      LIMIT 1000
    `);
    res.json(logs);
  } catch (error) {
    console.error('Error getting campaign sending logs:', error);
    res.status(500).json({ error: 'Failed to get campaign sending logs' });
  }
});

app.get('/api/campaign-sending-logs/:campaignId', async (req, res) => {
  try {
    const { campaignId } = req.params;
    const logs = await database.all(`
      SELECT 
        csl.id,
        csl.campaign_id,
        csl.campaign_name,
        csl.recipient_id,
        csl.recipient_name,
        csl.recipient_email,
        csl.recipient_phone,
        csl.recipient_whatsapp,
        csl.channel,
        csl.status,
        csl.sent_at,
        csl.error_message,
        csl.message_id,
        csl.message_content,
        csl.created_at,
        csl.updated_at,
        c.created_at as campaign_created_at
      FROM campaign_sending_logs csl
      LEFT JOIN campaigns c ON csl.campaign_id = c.id
      WHERE csl.campaign_id = ?
      ORDER BY csl.sent_at DESC
    `, [campaignId]);
    res.json(logs);
  } catch (error) {
    console.error('Error getting campaign sending logs:', error);
    res.status(500).json({ error: 'Failed to get campaign sending logs' });
  }
});

// Update individual sending log entry
app.patch('/api/campaign-sending-logs/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { status, error_message, message_id } = req.body;

    // Build update query dynamically based on provided fields
    const updateFields = [];
    const values = [];

    if (status) {
      updateFields.push('status = ?');
      values.push(status);
    }

    if (error_message !== undefined) {
      updateFields.push('error_message = ?');
      values.push(error_message);
    }

    if (message_id) {
      updateFields.push('message_id = ?');
      values.push(message_id);
    }

    if (updateFields.length === 0) {
      return res.status(400).json({ error: 'No valid fields to update' });
    }

    // Add updated timestamp
    updateFields.push('sent_at = CURRENT_TIMESTAMP');
    values.push(id);

    const query = `UPDATE campaign_sending_logs SET ${updateFields.join(', ')} WHERE id = ?`;

    await database.run(query, values);

    // Get updated log entry
    const updatedLog = await database.get('SELECT * FROM campaign_sending_logs WHERE id = ?', [id]);

    res.json({
      success: true,
      message: 'Sending log updated successfully',
      log: updatedLog
    });

  } catch (error) {
    console.error('Error updating campaign sending log:', error);
    res.status(500).json({ error: 'Failed to update campaign sending log' });
  }
});
app.use('/api/subscribers', subscriberRoutes);
app.use('/api/templates', templateRoutes);
app.use('/api/signatures', signatureRoutes);
app.use('/api/users', userRoutes);
app.use('/api/areas-of-interest', areasOfInterestRoutes);
app.use('/api/placeholders', placeholderRoutes);
app.use('/api/settings', settingsRoutes);
app.use('/api/audit', auditRoutes);
app.use('/api/backups', backupRoutes);
app.use('/api/database', databaseBackupRoutes);
app.use('/api/whatsapp', whatsappRoutes);
app.use('/api/sms', require('./routes/smsGateway'));

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  
  if (err.type === 'entity.parse.failed') {
    return res.status(400).json({
      error: 'Invalid JSON payload',
      message: 'Please check your request body format'
    });
  }
  
  if (err.code === 'SQLITE_CONSTRAINT') {
    return res.status(409).json({
      error: 'Database constraint violation',
      message: 'The operation violates database constraints'
    });
  }
  
  res.status(500).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not found',
    message: `Route ${req.method} ${req.originalUrl} not found`
  });
});

// Update campaign schema function
const updateCampaignSchema = async () => {
  try {
    console.log('🔧 Checking campaign schema...');

    // Check current schema
    const tableInfo = await database.all("PRAGMA table_info(campaigns)");
    const existingColumns = tableInfo.map(col => col.name);

    // Add columns if they don't exist
    const columnsToAdd = [
      { name: 'sent_count', type: 'INTEGER DEFAULT 0' },
      { name: 'failed_count', type: 'INTEGER DEFAULT 0' },
      { name: 'completed_date', type: 'TEXT' },
      { name: 'error_message', type: 'TEXT' }
    ];

    let addedColumns = 0;
    for (const column of columnsToAdd) {
      if (!existingColumns.includes(column.name)) {
        try {
          await database.run(`ALTER TABLE campaigns ADD COLUMN ${column.name} ${column.type}`);
          console.log(`   ✅ Added column: ${column.name}`);
          addedColumns++;
        } catch (error) {
          console.log(`   ⚠️ Column ${column.name} might already exist:`, error.message);
        }
      }
    }

    if (addedColumns > 0) {
      // Update existing records
      await database.run(`
        UPDATE campaigns
        SET sent_count = 0, failed_count = 0
        WHERE sent_count IS NULL OR failed_count IS NULL
      `);
      console.log(`✅ Campaign schema updated - added ${addedColumns} columns`);
    } else {
      console.log('✅ Campaign schema is up to date');
    }

  } catch (error) {
    console.error('❌ Error updating campaign schema:', error);
  }
};

// Create sending logs table
const createSendingLogsTable = async () => {
  try {
    // Check if table already exists
    const tableExists = await database.get(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='campaign_sending_logs'
    `);

    if (tableExists) {
      console.log('✅ campaign_sending_logs table already exists');
      return;
    }

    console.log('📋 Creating campaign_sending_logs table...');

    // Create the table
    await database.run(`
      CREATE TABLE campaign_sending_logs (
        id TEXT PRIMARY KEY,
        campaign_id TEXT NOT NULL,
        campaign_name TEXT NOT NULL,
        recipient_id TEXT,
        recipient_name TEXT NOT NULL,
        recipient_email TEXT,
        recipient_phone TEXT,
        recipient_whatsapp TEXT,
        channel TEXT NOT NULL CHECK (channel IN ('email', 'whatsapp', 'sms')),
        status TEXT NOT NULL CHECK (status IN ('sent', 'failed', 'pending')),
        sent_at TEXT NOT NULL,
        error_message TEXT,
        message_id TEXT,
        message_content TEXT,
        created_at TEXT NOT NULL DEFAULT (datetime('now')),
        updated_at TEXT NOT NULL DEFAULT (datetime('now')),
        FOREIGN KEY (campaign_id) REFERENCES campaigns (id)
      )
    `);

    // Create indexes
    await database.run(`CREATE INDEX idx_campaign_sending_logs_campaign_id ON campaign_sending_logs(campaign_id)`);
    await database.run(`CREATE INDEX idx_campaign_sending_logs_status ON campaign_sending_logs(status)`);
    await database.run(`CREATE INDEX idx_campaign_sending_logs_channel ON campaign_sending_logs(channel)`);

    console.log('✅ campaign_sending_logs table created with indexes');

  } catch (error) {
    console.error('❌ Error creating sending logs table:', error);
  }
};

// Add channel toggle columns
const addChannelToggleColumns = async () => {
  try {
    // Check and add columns to campaign_templates
    const templateTableInfo = await database.all("PRAGMA table_info(campaign_templates)");
    const templateColumns = templateTableInfo.map(col => col.name);

    const templateColumnsToAdd = [
      { name: 'email_enabled', type: 'BOOLEAN DEFAULT 1' },
      { name: 'whatsapp_enabled', type: 'BOOLEAN DEFAULT 1' },
      { name: 'sms_enabled', type: 'BOOLEAN DEFAULT 1' }
    ];

    for (const column of templateColumnsToAdd) {
      if (!templateColumns.includes(column.name)) {
        await database.run(`ALTER TABLE campaign_templates ADD COLUMN ${column.name} ${column.type}`);
        console.log(`✅ Added to campaign_templates: ${column.name}`);
      }
    }

    // Check and add columns to campaigns
    const campaignTableInfo = await database.all("PRAGMA table_info(campaigns)");
    const campaignColumns = campaignTableInfo.map(col => col.name);

    const campaignColumnsToAdd = [
      { name: 'email_enabled', type: 'BOOLEAN DEFAULT 1' },
      { name: 'whatsapp_enabled', type: 'BOOLEAN DEFAULT 1' },
      { name: 'sms_enabled', type: 'BOOLEAN DEFAULT 1' }
    ];

    for (const column of campaignColumnsToAdd) {
      if (!campaignColumns.includes(column.name)) {
        await database.run(`ALTER TABLE campaigns ADD COLUMN ${column.name} ${column.type}`);
        console.log(`✅ Added to campaigns: ${column.name}`);
      }
    }

    console.log('✅ Channel toggle columns verified');

  } catch (error) {
    console.error('❌ Error adding channel toggle columns:', error);
  }
};

// Initialize database and start server
const startServer = async () => {
  try {
    await initializeDatabase();
    console.log('✅ Database initialized successfully');

    // Update campaign schema if needed
    await updateCampaignSchema();

    // Create sending logs table if needed
    await createSendingLogsTable();

    // Add channel toggle columns if needed
    await addChannelToggleColumns();
    
    // Start enhanced template scheduler with campaign pre-queuing
    globalScheduler = new EnhancedTemplateScheduler();
    await globalScheduler.start();

    // Start campaign execution service
    await campaignExecutionService.start();

    // Start campaign sending service
    await campaignSendingService.start();
    
    app.listen(PORT, '0.0.0.0', () => {
      const getNetworkIP = () => {
        const nets = networkInterfaces();
        for (const interfaceName in nets) {
          const addresses = nets[interfaceName];
          for (const address of addresses) {
            if (address.family === 'IPv4' && !address.internal) {
              return address.address;
            }
          }
        }
        return 'localhost';
      };
      
      const networkIP = getNetworkIP();
      
      console.log(`🚀 CRM4CA Backend Server running on:`);
      console.log(`   📍 Local:    http://localhost:${PORT}`);
      console.log(`   📍 Network:  http://${networkIP}:${PORT}`);
      console.log(`📊 Health check available at:`);
      console.log(`   📍 Local:    http://localhost:${PORT}/health`);
      console.log(`   📍 Network:  http://${networkIP}:${PORT}/health`);
      console.log(`🔗 API endpoints available at:`);
      console.log(`   📍 Local:    http://localhost:${PORT}/api/`);
      console.log(`   📍 Network:  http://${networkIP}:${PORT}/api/`);
      console.log(`📚 Environment: ${process.env.NODE_ENV || 'development'}`);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});

startServer();
