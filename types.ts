

export enum Channel {
  EMAIL = 'email',
  WHATSAPP = 'whatsapp',
  SMS = 'sms',
}

export enum CampaignType {
  NEWSLETTER = 'newsletter',
  TAX_ADVISORY = 'tax_advisory',
  PROMOTION = 'promotion',
  EVENT_INVITATION = 'event_invitation',
  BIRTHDAY_WISH = 'birthday_wish', // New campaign type
  AD_HOC = 'ad_hoc', // New campaign type for targeted subscriber campaigns
  OTHER = 'other',
}

// Updated TemplateStatus enum
export enum TemplateStatus {
  ACTIVE = 'active',
  ARCHIVED = 'archived',
}

export enum CampaignStatus {
  DRAFT = 'draft',
  SCHEDULED = 'scheduled', // For regular campaigns, means scheduled. For Birthday, means "Active/Running". Also used for partially sent campaigns.
  SENDING = 'sending', // In-process of sending a batch or entire campaign
  SENT = 'sent',
  FAILED = 'failed',
  ARCHIVED = 'archived',
}

// Renamed to avoid conflict with new SubscriberProfileStatus
export enum CampaignSubscriberInteractionStatus {
  PENDING = 'pending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  OPENED = 'opened',
  CLICKED = 'clicked',
  FAILED = 'failed',
  BOUNCED = 'bounced',
}

export enum PlaceholderContext {
  CAMPAIGN = 'campaign',
  SIGNATURE = 'signature',
  BOTH = 'both',
}

export enum FormatType {
  TEXT = 'text',
  DATE = 'date',
  CURRENCY = 'currency',
  NUMBER = 'number',
  PERCENTAGE = 'percentage',
}

export enum UserRole {
  ADMIN = 'admin',
  EDITOR = 'editor',
  VIEWER = 'viewer',
}

// New Enum for Subscriber Profile Status
export enum SubscriberProfileStatus {
  ACTIVE = 'active',
  UNSUBSCRIBED = 'unsubscribed',
  PENDING_CONFIRMATION = 'pending_confirmation',
  CLEANED = 'cleaned', // e.g. email bounced hard
  ARCHIVED = 'archived',
}

export enum RecurrenceType {
  MONTHLY = 'monthly',
  SPECIFIC_DATES = 'specific_dates',
  // Future: DAILY, WEEKLY, ANNUALLY
}

export enum EncryptionType {
  TLS = 'tls',
  SSL = 'ssl',
  NONE = 'none', // Typically not used with Gmail
}

export enum HolidayHandlingRule {
  SEND_ON_HOLIDAY = 'send_on_holiday',
  SEND_DAY_BEFORE_IF_HOLIDAY = 'send_day_before_if_holiday',
  SEND_DAY_AFTER_IF_HOLIDAY = 'send_day_after_if_holiday',
}

export interface User {
  user_id: string; // UUID
  name: string;
  email?: string; // Made optional
  password?: string; // Added password field (store hashed in real app)
  role: UserRole;
  permissions?: string[]; // Array of permissions (read, write) - Made optional
  created_at: string; // DateTime
  updated_at: string; // DateTime
  is_active: boolean;
  is_email_verified: boolean;
  last_login?: string; // DateTime (nullable)
  can_manage_campaigns: boolean;
  can_edit_subscribers: boolean;
  can_view_analytics: boolean;
  default_sender_name?: string;
  default_sender_email?: string;
  default_sender_phone?: string;
  table_preferences?: Record<string, string[]>; // For user-specific column visibility: {[tableKey: string]: columnId[]}
  is_admin_only?: boolean;
  owner_user_id?: string;
  token?: string; // Authentication token
}

export interface MessageSignature {
  id: string; // AutoField (using string for frontend mock)
  name: string;
  channel: Channel;
  content: string;
  html_content?: string;
  is_default: boolean;
  is_active: boolean;
  uses_placeholders: boolean;
  available_placeholders: string[]; // JSONField (list of strings)
  created_at: string; // DateTime
  updated_at: string; // DateTime
  created_by: string; // ForeignKey(User) - using user_id or name
  is_admin_only?: boolean; 
  owner_user_id?: string; 
}

export interface AreaOfInterest {
  id: string;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
  is_admin_only?: boolean; 
  owner_user_id?: string | null; 
  is_active?: boolean;
}

// New Subscriber Profile Interface
export interface Subscriber {
  id: string; // UUID
  email: string; // Unique
  firstName?: string;
  lastName?: string;
  entityName?: string; // New field
  phone?: string;
  birthDate?: string; // YYYY-MM-DD format, optional
  status: SubscriberProfileStatus;
  subscribed_at: string; // DateTime
  unsubscribed_at?: string; // DateTime (nullable)
  areasOfInterestIds: string[]; // Array of AreaOfInterest IDs
  customFields?: Record<string, any>; // JSONField for extra data
  allowWhatsApp: boolean; // New field
  allowSms: boolean; // New field
  created_at: string; // DateTime
  updated_at: string; // DateTime
  _daysUntilBirthday?: number; // Optional property for dashboard calculation
  _nextBirthdayDate?: Date; // Optional property for dashboard calculation
  is_admin_only?: boolean;
  owner_user_id?: string;
  remarks?: string; // New field for subscriber notes
}


export interface CampaignTemplate {
  id: string; // AutoField
  template_name: string;
  display_name: string;
  description: string;
  campaign_type: CampaignType;
  status: TemplateStatus; // Uses updated enum
  subject_template: string;
  email_content_template: string;
  email_html_template?: string;
  whatsapp_content_template?: string;
  sms_content_template?: string;
  email_signature_id?: string; // ForeignKey(MessageSignature)
  whatsapp_signature_id?: string; // ForeignKey(MessageSignature)
  sms_signature_id?: string; // ForeignKey(MessageSignature)
  requires_approval: boolean;
  sender_name: string;
  sender_email: string;
  sender_phone?: string;
  merge_fields: Record<string, string>; // JSONField (Dictionary)
  target_segments: string[]; // JSONField (List)
  interest_area_id?: string; // ForeignKey(AreaOfInterest) - Changed from interest_areas: string[]
  is_active: boolean; // This field might become redundant if TemplateStatus.ACTIVE serves the same purpose
  is_public: boolean;
  version: number; // PositiveIntegerField
  uses_placeholders: boolean;
  available_placeholders: string[]; // JSONField (List)
  // Rich text mode settings
  email_is_rich_text?: boolean; // Whether email uses rich text mode
  whatsapp_is_rich_text?: boolean; // Whether WhatsApp uses rich text mode  
  sms_is_rich_text?: boolean; // Whether SMS uses rich text mode
  created_by: string; // ForeignKey(User)
  created_at: string; // DateTime
  updated_at: string; // DateTime

  // Recurrence fields
  is_recurring?: boolean;
  recurrence_type?: RecurrenceType;
  recurrence_days_of_month?: number[]; // For MONTHLY
  recurrence_months_of_year?: number[]; // Optional for MONTHLY (1-12)
  recurrence_specific_dates?: string[]; // For SPECIFIC_DATES (MM-DD)

  email_enabled?: boolean;
  whatsapp_enabled?: boolean;
  sms_enabled?: boolean;
  is_admin_only?: boolean;
  owner_user_id?: string;
}

export interface Campaign {
  id: string; // AutoField
  name: string;
  subject: string;
  template_id: string; // ForeignKey(CampaignTemplate)
  email_content: string;
  email_html_content?: string;
  whatsapp_content?: string;
  sms_content?: string;
  email_signature_id?: string; // ForeignKey(MessageSignature)
  whatsapp_signature_id?: string; // ForeignKey(MessageSignature)
  sms_signature_id?: string; // ForeignKey(MessageSignature)
  status: CampaignStatus;
  campaign_type: CampaignType;
  uses_placeholders: boolean;
  available_placeholders: string[]; // JSONField (List)
  campaign_specific_placeholder_values?: Record<string, string>; // New field for campaign-level placeholder values
  sender_name?: string; // Campaign-specific sender name
  sender_email?: string; // Campaign-specific sender email
  sender_phone?: string; // Campaign-specific sender phone
  created_by: string; // ForeignKey(User)
  created_at: string; // DateTime
  updated_at: string; // DateTime
  scheduled_date?: string; // DateTime - For regular campaigns
  sent_date?: string; // DateTime
  total_recipients: number; // PositiveIntegerField
  opened: number; // PositiveIntegerField
  clicked: number; // PositiveIntegerField
  bounced: number; // PositiveIntegerField

  // Fields for Birthday Campaigns
  birthday_send_offset_days?: number; // 0 for on birthday, 1 for 1 day before, etc.
  birthday_send_time?: string; // HH:MM format, e.g., "09:00"
  holiday_handling_rule?: HolidayHandlingRule; // New field for holiday logic
  triggered_from_template_recurrence_date?: string; // For auto-scheduled campaigns

  // Fields for batch sending
  processed_recipients_count?: number;
  last_batch_sent_date?: string;
  
  // Fields for hourly/daily rate limiting
  sent_in_current_hour_count?: number;
  current_hour_window_start_date?: string;
  sent_in_current_day_count?: number;
  current_day_window_start_date?: string;
  next_batch_eligible_at?: string; // Timestamp for when the next batch can start

  // Fields for ad-hoc campaigns with selected subscribers
  is_ad_hoc_campaign?: boolean; // Flag to indicate if this is an ad-hoc campaign
  selected_subscriber_ids?: string[]; // Array of specific subscriber IDs for ad-hoc campaigns
  subscriber_selection_mode?: 'all' | 'segment' | 'manual'; // How subscribers were selected
  
  // Attachments for email and whatsapp
  attachments?: EmailAttachment[]; // JSON array of attachments
  use_bcc_for_attachments?: boolean; // Whether to use BCC mode for emails with attachments

  email_enabled?: boolean;
  whatsapp_enabled?: boolean;
  sms_enabled?: boolean;
  is_admin_only?: boolean;
  owner_user_id?: string;
  remarks?: string; // New field for campaign notes
}

export interface CampaignSubscriber {
  id: string; // AutoField - This is the ID of the CampaignSubscriber link/entry
  campaign_id: string; // ForeignKey(Campaign)
  subscriber_id: string; // ForeignKey(Subscriber) - This is the ID of the actual Subscriber profile
  status: CampaignSubscriberInteractionStatus; // Using the renamed enum
  personalized_subject?: string;
  personalized_email_content?: string;
  personalized_whatsapp_content?: string;
  personalized_sms_content?: string;
  sent_at?: string; // DateTime
  delivered_at?: string; // DateTime
  opened_at?: string; // DateTime
  clicked_at?: string; // DateTime
  error_message?: string;
  retry_count: number; // PositiveIntegerField
  created_at?: string; // DateTime - Added
  updated_at?: string; // DateTime - Added
}

export interface PlaceholderManager {
  id: string; // AutoField
  name: string;
  display_name: string;
  description: string;
  context: PlaceholderContext;
  field_path: string;
  default_value: string;
  format_type: FormatType;
  is_active: boolean;
  created_at: string; // DateTime
}

export interface EmailSettings {
  smtpServer: string;
  smtpPort: number;
  encryption: EncryptionType;
  username: string;
  password?: string; // Password or App Password
  senderName: string;
  senderEmail: string;
  whatsAppNumber?: string;
  smsNumber?: string;
  dailyEmailLimit?: number;
  dailyWhatsAppLimit?: number;
  dailySmsLimit?: number;
  hourlyEmailLimit?: number; // New
  hourlyWhatsAppLimit?: number; // New
  hourlySmsLimit?: number; // New
  // BCC Settings for Attachments
  useBccForAttachments?: boolean; // Enable BCC sending for emails with attachments
  bccSenderEmail?: string; // Email to use as sender when using BCC (defaults to senderEmail)
}

export interface RegistrationDetails {
  isRegistered: boolean;
  registeredName: string; // This will store "Contact Person Name"
  registeredOrganizationName: string; // This is the new "Registered To" field, compulsory.
  registeredEmail: string;
  registeredPhoneNumber: string; // Compulsory
  registrationKey?: string; // This is only for submission, not stored long-term
  registeredAddress?: string; 
  registeredCity: string; // Compulsory
  registeredPinCode?: string; 
}

// Audit Trail Types
export enum AuditActionType {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  EXPORT = 'EXPORT',
  IMPORT = 'IMPORT',
  SEND_MANUAL = 'SEND_MANUAL', // For campaign "Send Now"
  SEND_BATCH = 'SEND_BATCH', // For campaign batch sending
  SYSTEM_ACTION = 'SYSTEM_ACTION', // General system action
  LOGIN = 'LOGIN', 
  LOGOUT = 'LOGOUT', 
  SECURITY = 'SECURITY', // e.g., Password change, 2FA enable/disable
  CONFIGURATION = 'CONFIGURATION', // e.g., Email settings saved
  VIEW = 'VIEW', // For viewing/accessing data
  ERROR = 'ERROR' // For error conditions
}

export interface AuditLogUser {
  id?: string; // User ID if applicable
  name: string; // User name, or "System", "Anonymous"
}

export interface AuditLogEntry {
  id: string; // Unique ID for the log entry
  timestamp: string; // ISO string
  user: AuditLogUser;
  action: AuditActionType;
  entityType: string; // e.g., 'Campaign', 'Template', 'Subscriber'
  entityId?: string; // ID of the entity affected
  entityName?: string; // Display name of the entity
  description: string; // Human-readable description of the action
  details?: {
    oldValues?: Record<string, any>; // Previous values for UPDATE actions
    newValues?: Record<string, any>; // New values for CREATE/UPDATE actions
    affectedCount?: number; // Number of records affected (for bulk operations)
    metadata?: Record<string, any>; // Additional context data
  };
}

// Theme types
export enum Theme {
  LIGHT = 'light',
  DARK = 'dark',
  PROFESSIONAL = 'professional', // "Professional Blue" theme
  RAINBOW = 'rainbow' // Vibrant rainbow theme
}

// Database Source types
export enum DatabaseSource {
  PRODUCTION = 'production',
  STAGING = 'staging',
  DEVELOPMENT = 'development',
  LOCAL_MOCK = 'local_mock',
}

// Display Settings
export interface DisplaySettings {
  autoResizeTableWidth: boolean;
  showWeatherReport: boolean;
  campaignQueueDays: number; // Days before scheduled date to queue campaigns (default: 15)
}

// Backup System Types
export enum BackupType {
  AUTOMATIC = 'automatic',
  MANUAL = 'manual',
  SCHEDULED = 'scheduled'
}

export enum BackupFrequency {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  CUSTOM = 'custom'
}

export enum BackupStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export interface BackupSettings {
  // Automatic backup settings (developer/maintenance use)
  automaticBackup: {
    enabled: boolean;
    frequency: BackupFrequency;
    time: string; // HH:MM format (e.g., "19:00" for 7 PM)
    maxVersions: number; // Maximum backup versions to keep (default: 5)
    location: string; // Relative path for automatic backups (e.g., "./backups/auto")
    includeUploads: boolean; // Whether to include uploaded files
    compression: boolean; // Whether to compress backup files
  };

  // Admin-controlled backup settings
  customBackup: {
    enabled: boolean;
    frequency: BackupFrequency;
    customCronExpression?: string; // For custom frequency
    time: string; // HH:MM format
    location: string; // Admin-specified backup location
    maxVersions: number; // Admin-specified max versions
    includeUploads: boolean;
    compression: boolean;
    emailNotification: boolean; // Send email notification on completion
    notificationEmail?: string; // Email address for notifications
  };

  // Retention settings
  retention: {
    deleteOldBackups: boolean;
    retentionDays: number; // Days to keep backups before deletion
    archiveOldBackups: boolean; // Archive instead of delete
    archiveLocation?: string; // Location for archived backups
  };
}

export interface BackupRecord {
  id: string;
  type: BackupType;
  status: BackupStatus;
  filename: string;
  filepath: string;
  size: number; // File size in bytes
  created_at: string;
  completed_at?: string;
  error_message?: string;
  metadata: {
    databaseSize: number;
    recordCounts: Record<string, number>; // Table name -> record count
    version: string;
    compression: boolean;
    includesUploads: boolean;
    checksum?: string; // File integrity checksum
  };
  created_by: string; // User ID who initiated the backup
}

export interface RestoreOperation {
  id: string;
  backupId: string;
  status: BackupStatus;
  created_at: string;
  completed_at?: string;
  error_message?: string;
  metadata: {
    originalDatabaseSize: number;
    restoredRecordCounts: Record<string, number>;
    validationResults: {
      integrityCheck: boolean;
      checksumMatch: boolean;
      schemaCompatible: boolean;
    };
  };
  created_by: string;
}

// Rich Text Editor Settings per composition context
export interface RichTextSettings {
  campaign_email: boolean;
  campaign_whatsapp: boolean;
  campaign_sms: boolean;
  template_email: boolean;
  template_whatsapp: boolean;
  template_sms: boolean;
  signature_email: boolean;
  signature_whatsapp: boolean;
  signature_sms: boolean;
  area_description: boolean;
}

// User Table Preferences
export type UserTablePreferences = Record<string, Record<string, string[]>>; // {[userId: string]: {[tableKey: string]: columnId[]}}

// ==================== EMAIL SYSTEM TYPES ====================

export enum EmailProvider {
  GMAIL_SMTP = 'gmail_smtp',
  GMAIL_OAUTH = 'gmail_oauth',
  OUTLOOK_SMTP = 'outlook_smtp',
  SMTP_CUSTOM = 'smtp_custom',
  SENDGRID = 'sendgrid',
  MAILGUN = 'mailgun'
}

export enum EmailAuthType {
  SMTP_PASSWORD = 'smtp_password',
  SMTP_APP_PASSWORD = 'smtp_app_password',
  OAUTH2 = 'oauth2',
  API_KEY = 'api_key'
}

export enum EmailStatus {
  PENDING = 'pending',
  SENDING = 'sending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  OPENED = 'opened',
  CLICKED = 'clicked',
  FAILED = 'failed',
  BOUNCED = 'bounced',
  COMPLAINED = 'complained',
  UNSUBSCRIBED = 'unsubscribed'
}

export enum EmailPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent'
}

export interface EmailConfiguration {
  id: string;
  name: string;
  provider: EmailProvider;
  authType: EmailAuthType;
  isActive: boolean;
  isDefault: boolean;
  settings: EmailProviderSettings;
  rateLimits: EmailRateLimits;
  created_at: string;
  updated_at: string;
  created_by: string;
}

export interface EmailProviderSettings {
  // SMTP Settings
  smtp?: {
    host: string;
    port: number;
    secure: boolean; // true for 465, false for other ports
    username: string;
    password: string; // App password or regular password
    authMethod?: 'PLAIN' | 'LOGIN' | 'XOAUTH2';
  };
  
  // OAuth2 Settings
  oauth2?: {
    clientId: string;
    clientSecret: string;
    refreshToken: string;
    accessToken?: string;
    scope: string[];
    authUrl: string;
    tokenUrl: string;
  };
  
  // API Settings (for services like SendGrid, Mailgun)
  api?: {
    baseUrl: string;
    apiKey: string;
    region?: string;
    domain?: string;
  };
  
  // Sender Information
  defaultSender: {
    name: string;
    email: string;
    replyTo?: string;
  };
  
  // Advanced Settings
  advanced?: {
    enableTracking: boolean;
    enableSignature: boolean;
    timeout: number;
    retryAttempts: number;
    customHeaders?: Record<string, string>;
  };
}

export interface EmailRateLimits {
  perSecond: number;
  perMinute: number;
  perHour: number;
  perDay: number;
  burstLimit: number;
}

export interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  htmlContent: string;
  textContent: string;
  variables: string[];
  category: string;
  isActive: boolean;
  created_at: string;
  updated_at: string;
  created_by: string;
}

export interface EmailMessage {
  id: string;
  campaign_id?: string;
  template_id?: string;
  configuration_id: string;
  to: EmailRecipient[];
  cc?: EmailRecipient[];
  bcc?: EmailRecipient[];
  from: EmailRecipient;
  replyTo?: EmailRecipient;
  subject: string;
  htmlContent: string;
  textContent: string;
  attachments?: EmailAttachment[];
  headers?: Record<string, string>;
  priority: EmailPriority;
  status: EmailStatus;
  scheduledAt?: string;
  sentAt?: string;
  deliveredAt?: string;
  openedAt?: string;
  clickedAt?: string;
  failureReason?: string;
  trackingId: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface EmailRecipient {
  name?: string;
  email: string;
  variables?: Record<string, string>;
}

export interface EmailAttachment {
  filename: string;
  content: string; // base64 encoded
  contentType: string;
  disposition?: 'attachment' | 'inline';
  contentId?: string; // for inline attachments
}

export interface EmailQueue {
  id: string;
  message_id: string;
  priority: EmailPriority;
  attempts: number;
  maxAttempts: number;
  nextAttempt: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  error?: string;
  created_at: string;
  updated_at: string;
}

export interface EmailAnalytics {
  message_id: string;
  opens: number;
  clicks: number;
  bounces: number;
  complaints: number;
  unsubscribes: number;
  firstOpenAt?: string;
  lastOpenAt?: string;
  openLocations?: Array<{
    country: string;
    city: string;
    timestamp: string;
  }>;
  clickUrls?: Array<{
    url: string;
    clicks: number;
    lastClickAt: string;
  }>;
  userAgent?: string;
  ipAddress?: string;
}

export interface EmailCampaignStats {
  campaign_id: string;
  totalSent: number;
  totalDelivered: number;
  totalOpened: number;
  totalClicked: number;
  totalBounced: number;
  totalComplained: number;
  totalUnsubscribed: number;
  openRate: number;
  clickRate: number;
  bounceRate: number;
  complaintRate: number;
  unsubscribeRate: number;
  lastUpdated: string;
}

export interface OAuthCredentials {
  access_token: string;
  refresh_token: string;
  scope: string;
  token_type: string;
  expiry_date: number;
}

export interface EmailServiceHealth {
  provider: EmailProvider;
  status: 'healthy' | 'degraded' | 'down';
  lastChecked: string;
  responseTime: number;
  errorRate: number;
  rateLimitStatus: {
    remaining: number;
    resetTime: string;
  };
}

export interface DatabaseUserRow {
  user_id: string;
  name: string;
  email: string;
  password: string;
  role: string;
  permissions: string;
  created_at: string;
  updated_at: string;
  is_active: number;
  is_email_verified: number;
  last_login?: string;
  can_manage_campaigns: number;
  can_edit_subscribers: number;
  can_view_analytics: number;
  default_sender_name?: string;
  default_sender_email?: string;
  default_sender_phone?: string;
  table_preferences: string;
  is_admin_only: number;
  owner_user_id?: string;
}

export interface DatabaseCampaignRow {
  id: string;
  name: string;
  subject: string;
  template_id: string;
  email_content: string;
  email_html_content?: string;
  whatsapp_content?: string;
  sms_content?: string;
  email_signature_id?: string;
  whatsapp_signature_id?: string;
  sms_signature_id?: string;
  status: string;
  campaign_type: string;
  uses_placeholders: number;
  available_placeholders: string;
  campaign_specific_placeholder_values?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  scheduled_date?: string;
  sent_date?: string;
  total_recipients: number;
  opened: number;
  clicked: number;
  bounced: number;
  birthday_send_offset_days?: number;
  birthday_send_time?: string;
  holiday_handling_rule?: string;
  triggered_from_template_recurrence_date?: string;
  processed_recipients_count?: number;
  last_batch_sent_date?: string;
  sent_in_current_hour_count?: number;
  current_hour_window_start_date?: string;
  sent_in_current_day_count?: number;
  current_day_window_start_date?: string;
  next_batch_eligible_at?: string;
  is_admin_only: number;
  owner_user_id?: string;
}
