import express from 'express';
import { database } from '../database/connection.js';
import { randomUUID } from 'crypto';
import { campaignIdService } from '../services/campaign-id-service.js';

const router = express.Router();

// Get all campaigns
router.get('/', async (req, res) => {
  try {
    const campaigns = await database.all(`
      SELECT c.*,
             t.template_name,
             t.display_name as template_display_name,
             t.target_segments as template_target_segments,
             aoi.name as template_area_name
      FROM campaigns c
      LEFT JOIN campaign_templates t ON c.template_id = t.id
      LEFT JOIN areas_of_interest aoi ON t.interest_area_id = aoi.id
      ORDER BY c.created_at DESC
    `);

    // Parse JSON fields and map field names for frontend compatibility
    const parsedCampaigns = campaigns.map(campaign => {
      // Parse template target segments for display
      let targetSegments = [];
      try {
        if (campaign.template_target_segments) {
          targetSegments = JSON.parse(campaign.template_target_segments);
        }
      } catch (e) {
        console.warn('Error parsing template_target_segments:', e);
      }

      // If no template segments, use area name
      if (targetSegments.length === 0 && campaign.template_area_name) {
        targetSegments = [campaign.template_area_name];
      }

      return {
        ...campaign,
        available_placeholders: campaign.available_placeholders ? JSON.parse(campaign.available_placeholders) : [],
        campaign_specific_placeholder_values: campaign.campaign_specific_placeholder_values ? JSON.parse(campaign.campaign_specific_placeholder_values) : {},
        target_subscriber_ids: campaign.target_subscriber_ids ? JSON.parse(campaign.target_subscriber_ids) : [],
        // Map database field to frontend interface field for compatibility
        selected_subscriber_ids: campaign.target_subscriber_ids ? JSON.parse(campaign.target_subscriber_ids) : [],
        attachments: campaign.attachments ? JSON.parse(campaign.attachments) : [],
        is_active: Boolean(campaign.is_active),
        uses_placeholders: Boolean(campaign.uses_placeholders),
        is_admin_only: Boolean(campaign.is_admin_only),
        // Add target segments for frontend display
        target_segments: targetSegments
      };
    });

    res.json(parsedCampaigns);
  } catch (error) {
    console.error('Error fetching campaigns:', error);
    res.status(500).json({ error: 'Failed to fetch campaigns' });
  }
});

// Get campaign by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const campaign = await database.get(`
      SELECT c.*, t.template_name, t.display_name as template_display_name
      FROM campaigns c
      LEFT JOIN campaign_templates t ON c.template_id = t.id
      WHERE c.id = ?
    `, [id]);
    
    if (!campaign) {
      return res.status(404).json({ error: 'Campaign not found' });
    }
    
    // Parse JSON fields and map field names for frontend compatibility
    const parsedCampaign = {
      ...campaign,
      available_placeholders: campaign.available_placeholders ? JSON.parse(campaign.available_placeholders) : [],
      campaign_specific_placeholder_values: campaign.campaign_specific_placeholder_values ? JSON.parse(campaign.campaign_specific_placeholder_values) : {},
      target_subscriber_ids: campaign.target_subscriber_ids ? JSON.parse(campaign.target_subscriber_ids) : [],
      // Map database field to frontend interface field for compatibility
      selected_subscriber_ids: campaign.target_subscriber_ids ? JSON.parse(campaign.target_subscriber_ids) : [],
      attachments: campaign.attachments ? JSON.parse(campaign.attachments) : [],
      is_active: Boolean(campaign.is_active),
      uses_placeholders: Boolean(campaign.uses_placeholders),
      is_admin_only: Boolean(campaign.is_admin_only)
    };
    
    res.json(parsedCampaign);
  } catch (error) {
    console.error('Error fetching campaign:', error);
    res.status(500).json({ error: 'Failed to fetch campaign' });
  }
});

// Create new campaign
router.post('/', async (req, res) => {
  try {
    const campaignData = req.body;
    console.log('📥 Received campaign data:', campaignData);

    // CRITICAL: Validate that ad-hoc campaigns have recipients
    if (campaignData.campaign_type === 'other' || campaignData.campaign_type === 'ad_hoc') {
      const targetSubscriberIds = campaignData.target_subscriber_ids || campaignData.selected_subscriber_ids || [];
      const recipientCount = Array.isArray(targetSubscriberIds) ? targetSubscriberIds.length : 0;

      if (recipientCount === 0) {
        console.log('❌ Validation failed: No recipients for ad-hoc campaign');
        return res.status(400).json({
          error: 'Cannot create ad-hoc campaign without recipients',
          details: 'Ad-hoc campaigns must have at least one recipient selected'
        });
      }

      console.log(`✅ Validation passed: ${recipientCount} recipients for ad-hoc campaign`);
    }

    const now = new Date().toISOString();

    // Generate unique campaign ID using the centralized service
    const campaignType = campaignData.campaign_type === 'birthday_wish' ? 'birthday' : 'manual';
    const campaignId = await campaignIdService.generateUniqueCampaignId(campaignType);

    console.log('📊 Inserting campaign with unique ID:', campaignId);
    console.log('📊 Campaign parameters:', [
      campaignId,
      campaignData.name,
      campaignData.subject,
      campaignData.template_id || null,
      campaignData.email_content || '',
      campaignData.email_content_type || 'html',
      campaignData.whatsapp_content || null,
      campaignData.whatsapp_content_type || null,
      campaignData.sms_content || null,
      campaignData.email_signature_id || null,
      campaignData.whatsapp_signature_id || null,
      campaignData.sms_signature_id || null,
      campaignData.status,
      campaignData.campaign_type,
      campaignData.uses_placeholders ? 1 : 0,
      'JSON data...', // available_placeholders
      'JSON data...', // campaign_specific_placeholder_values
      campaignData.created_by,
      now,
      now,
      campaignData.scheduled_date || null,
      null, // sent_date
      campaignData.total_recipients || 0,
      campaignData.opened || 0,
      campaignData.clicked || 0,
      campaignData.bounced || 0,
      'JSON data...', // target_subscriber_ids
      'JSON data...', // attachments
      campaignData.birthday_send_offset_days || 0,
      campaignData.birthday_send_time || null,
      campaignData.holiday_handling_rule || null,
      null, // triggered_from_template_recurrence_date
      campaignData.processed_recipients_count || 0,
      null, // last_batch_sent_date
      campaignData.sent_in_current_hour_count || 0,
      null, // current_hour_window_start_date
      campaignData.sent_in_current_day_count || 0,
      null, // current_day_window_start_date
      null, // next_batch_eligible_at
      campaignData.is_admin_only ? 1 : 0,
      campaignData.owner_user_id || null
    ]);
    
    await database.run(`
      INSERT INTO campaigns (
        id, name, subject, template_id, email_content, email_content_type,
        whatsapp_content, whatsapp_content_type, sms_content,
        email_signature_id, whatsapp_signature_id, sms_signature_id,
        status, campaign_type, uses_placeholders, available_placeholders,
        campaign_specific_placeholder_values, sender_name, sender_email, sender_phone,
        created_by, created_at, updated_at,
        scheduled_date, sent_date, total_recipients, opened, clicked, bounced,
        target_subscriber_ids, attachments, birthday_send_offset_days,
        birthday_send_time, holiday_handling_rule, triggered_from_template_recurrence_date,
        processed_recipients_count, last_batch_sent_date, sent_in_current_hour_count,
        current_hour_window_start_date, sent_in_current_day_count, current_day_window_start_date,
        next_batch_eligible_at, is_admin_only, owner_user_id,
        email_recipients_count, whatsapp_recipients_count, sms_recipients_count,
        email_enabled, whatsapp_enabled, sms_enabled, remarks
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      campaignId,
      campaignData.name,
      campaignData.subject,
      campaignData.template_id || null,
      campaignData.email_content || '',
      campaignData.email_content_type || 'html',
      campaignData.whatsapp_content || null,
      campaignData.whatsapp_content_type || null,
      campaignData.sms_content || null,
      campaignData.email_signature_id || null,
      campaignData.whatsapp_signature_id || null,
      campaignData.sms_signature_id || null,
      campaignData.status,
      campaignData.campaign_type,
      campaignData.uses_placeholders ? 1 : 0,
      JSON.stringify(campaignData.available_placeholders || []),
      JSON.stringify(campaignData.campaign_specific_placeholder_values || {}),
      campaignData.sender_name || null,
      campaignData.sender_email || null,
      campaignData.sender_phone || null,
      campaignData.created_by,
      now,
      now,
      campaignData.scheduled_date || null,
      null, // sent_date
      campaignData.total_recipients || 0,
      campaignData.opened || 0,
      campaignData.clicked || 0,
      campaignData.bounced || 0,
      JSON.stringify(campaignData.target_subscriber_ids || []),
      JSON.stringify(campaignData.attachments || []),
      campaignData.birthday_send_offset_days || 0,
      campaignData.birthday_send_time || null,
      campaignData.holiday_handling_rule || null,
      null, // triggered_from_template_recurrence_date
      campaignData.processed_recipients_count || 0,
      null, // last_batch_sent_date
      campaignData.sent_in_current_hour_count || 0,
      null, // current_hour_window_start_date
      campaignData.sent_in_current_day_count || 0,
      null, // current_day_window_start_date
      null, // next_batch_eligible_at
      campaignData.is_admin_only ? 1 : 0,
      campaignData.owner_user_id || null,
      // Channel-specific recipient counts
      campaignData.email_recipients_count || 0,
      campaignData.whatsapp_recipients_count || 0,
      campaignData.sms_recipients_count || 0,
      // Channel enabled flags
      campaignData.email_enabled !== false ? 1 : 0,
      campaignData.whatsapp_enabled !== false ? 1 : 0,
      campaignData.sms_enabled !== false ? 1 : 0,
      // Remarks
      campaignData.remarks || ''
    ]);
    
    // Fetch the created campaign
    const newCampaign = await database.get('SELECT * FROM campaigns WHERE id = ?', [campaignId]);
    
    // Parse JSON fields for response
    const parsedCampaign = {
      ...newCampaign,
      available_placeholders: newCampaign.available_placeholders ? JSON.parse(newCampaign.available_placeholders) : [],
      campaign_specific_placeholder_values: newCampaign.campaign_specific_placeholder_values ? JSON.parse(newCampaign.campaign_specific_placeholder_values) : {},
      target_subscriber_ids: newCampaign.target_subscriber_ids ? JSON.parse(newCampaign.target_subscriber_ids) : [],
      attachments: newCampaign.attachments ? JSON.parse(newCampaign.attachments) : [],
      is_active: Boolean(newCampaign.is_active),
      uses_placeholders: Boolean(newCampaign.uses_placeholders),
      is_admin_only: Boolean(newCampaign.is_admin_only)
    };
    
    console.log('✅ Campaign created successfully:', campaignId);
    res.status(201).json(parsedCampaign);
  } catch (error) {
    console.error('❌ Error creating campaign:', error);
    console.error('❌ Error stack:', error.stack);
    console.error('❌ Error code:', error.code);
    console.error('❌ Campaign data that failed:', JSON.stringify(req.body, null, 2));
    
    // Provide more detailed error information
    let errorMessage = error.message;
    if (error.code === 'SQLITE_CONSTRAINT') {
      errorMessage = 'Database constraint violation - check for duplicate IDs or invalid references';
    } else if (error.code === 'SQLITE_ERROR') {
      errorMessage = 'SQL syntax error or missing columns';
    }
    
    res.status(500).json({ 
      error: 'Failed to create campaign', 
      details: errorMessage,
      code: error.code || 'UNKNOWN_ERROR'
    });
  }
});

// Update campaign
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const campaignData = req.body;
    const now = new Date().toISOString();
    
    // Check if campaign exists
    const existingCampaign = await database.get('SELECT id FROM campaigns WHERE id = ?', [id]);
    if (!existingCampaign) {
      return res.status(404).json({ error: 'Campaign not found' });
    }
    
    await database.run(`
      UPDATE campaigns SET
        name = ?, subject = ?, template_id = ?, email_content = ?, email_content_type = ?,
        whatsapp_content = ?, whatsapp_content_type = ?, sms_content = ?,
        email_signature_id = ?, whatsapp_signature_id = ?, sms_signature_id = ?,
        status = ?, campaign_type = ?, uses_placeholders = ?, available_placeholders = ?,
        campaign_specific_placeholder_values = ?, sender_name = ?, sender_email = ?, sender_phone = ?,
        scheduled_date = ?, sent_date = ?,
        total_recipients = ?, opened = ?, clicked = ?, bounced = ?, target_subscriber_ids = ?,
        attachments = ?, birthday_send_offset_days = ?, birthday_send_time = ?,
        holiday_handling_rule = ?, triggered_from_template_recurrence_date = ?,
        processed_recipients_count = ?, last_batch_sent_date = ?, sent_in_current_hour_count = ?,
        current_hour_window_start_date = ?, sent_in_current_day_count = ?,
        current_day_window_start_date = ?, next_batch_eligible_at = ?,
        is_admin_only = ?, owner_user_id = ?, updated_at = ?,
        email_recipients_count = ?, whatsapp_recipients_count = ?, sms_recipients_count = ?,
        email_enabled = ?, whatsapp_enabled = ?, sms_enabled = ?, remarks = ?
      WHERE id = ?
    `, [
      campaignData.name,
      campaignData.subject,
      campaignData.template_id || null,
      campaignData.email_content || '',
      campaignData.email_content_type || 'html',
      campaignData.whatsapp_content || null,
      campaignData.whatsapp_content_type || null,
      campaignData.sms_content || null,
      campaignData.email_signature_id || null,
      campaignData.whatsapp_signature_id || null,
      campaignData.sms_signature_id || null,
      campaignData.status,
      campaignData.campaign_type,
      campaignData.uses_placeholders ? 1 : 0,
      JSON.stringify(campaignData.available_placeholders || []),
      JSON.stringify(campaignData.campaign_specific_placeholder_values || {}),
      campaignData.sender_name || null,
      campaignData.sender_email || null,
      campaignData.sender_phone || null,
      campaignData.scheduled_date || null,
      campaignData.sent_date || null,
      campaignData.total_recipients || 0,
      campaignData.opened || 0,
      campaignData.clicked || 0,
      campaignData.bounced || 0,
      JSON.stringify(campaignData.target_subscriber_ids || []),
      JSON.stringify(campaignData.attachments || []),
      campaignData.birthday_send_offset_days || 0,
      campaignData.birthday_send_time || null,
      campaignData.holiday_handling_rule || null,
      campaignData.triggered_from_template_recurrence_date || null,
      campaignData.processed_recipients_count || 0,
      campaignData.last_batch_sent_date || null,
      campaignData.sent_in_current_hour_count || 0,
      campaignData.current_hour_window_start_date || null,
      campaignData.sent_in_current_day_count || 0,
      campaignData.current_day_window_start_date || null,
      campaignData.next_batch_eligible_at || null,
      campaignData.is_admin_only ? 1 : 0,
      campaignData.owner_user_id || null,
      now,
      // Channel-specific recipient counts
      campaignData.email_recipients_count || 0,
      campaignData.whatsapp_recipients_count || 0,
      campaignData.sms_recipients_count || 0,
      // Channel enabled flags
      campaignData.email_enabled !== false ? 1 : 0,
      campaignData.whatsapp_enabled !== false ? 1 : 0,
      campaignData.sms_enabled !== false ? 1 : 0,
      // Remarks
      campaignData.remarks || '',
      id
    ]);
    
    // Fetch updated campaign
    const updatedCampaign = await database.get('SELECT * FROM campaigns WHERE id = ?', [id]);
    
    // Parse JSON fields for response
    const parsedCampaign = {
      ...updatedCampaign,
      available_placeholders: updatedCampaign.available_placeholders ? JSON.parse(updatedCampaign.available_placeholders) : [],
      campaign_specific_placeholder_values: updatedCampaign.campaign_specific_placeholder_values ? JSON.parse(updatedCampaign.campaign_specific_placeholder_values) : {},
      target_subscriber_ids: updatedCampaign.target_subscriber_ids ? JSON.parse(updatedCampaign.target_subscriber_ids) : [],
      attachments: updatedCampaign.attachments ? JSON.parse(updatedCampaign.attachments) : [],
      is_active: Boolean(updatedCampaign.is_active),
      uses_placeholders: Boolean(updatedCampaign.uses_placeholders),
      is_admin_only: Boolean(updatedCampaign.is_admin_only)
    };
    
    res.json(parsedCampaign);
  } catch (error) {
    console.error('Error updating campaign:', error);
    res.status(500).json({ error: 'Failed to update campaign' });
  }
});

// Delete campaign
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Check if campaign exists
    const existingCampaign = await database.get('SELECT id FROM campaigns WHERE id = ?', [id]);
    if (!existingCampaign) {
      return res.status(404).json({ error: 'Campaign not found' });
    }
    
    await database.run('DELETE FROM campaigns WHERE id = ?', [id]);
    res.json({ message: 'Campaign deleted successfully' });
  } catch (error) {
    console.error('Error deleting campaign:', error);
    res.status(500).json({ error: 'Failed to delete campaign' });
  }
});

// Get campaign recipients
router.get('/:id/recipients', async (req, res) => {
  try {
    const { id } = req.params;

    // Get campaign details to determine recipient selection
    const campaign = await database.get('SELECT * FROM campaigns WHERE id = ?', [id]);

    if (!campaign) {
      return res.status(404).json({ error: 'Campaign not found' });
    }

    console.log(`📋 Getting recipients for campaign: ${campaign.name} (ID: ${id})`);

    // Get recipients based on campaign configuration
    let recipients = [];

    console.log(`📋 [DEBUG] Campaign selection mode: ${campaign.subscriber_selection_mode}`);
    console.log(`📋 [DEBUG] Selected subscriber IDs: ${campaign.selected_subscriber_ids}`);
    console.log(`📋 [DEBUG] Area of interest IDs: ${campaign.area_of_interest_ids}`);
    console.log(`📋 [DEBUG] Legacy subscriber IDs: ${campaign.subscriber_ids}`);

    // Check different subscriber selection modes
    if (campaign.subscriber_selection_mode === 'specific' && campaign.selected_subscriber_ids && campaign.selected_subscriber_ids.trim() !== '') {
      // Specific subscribers selected
      try {
        const subscriberIds = JSON.parse(campaign.selected_subscriber_ids);
        if (subscriberIds.length > 0) {
          const placeholders = subscriberIds.map(() => '?').join(',');
          recipients = await database.all(`
            SELECT * FROM subscribers
            WHERE id IN (${placeholders}) AND status = 'active'
          `, subscriberIds);
          console.log(`📋 [DEBUG] Found ${recipients.length} specific subscribers`);
        }
      } catch (e) {
        console.log(`📋 [DEBUG] Error parsing selected_subscriber_ids: ${e.message}`);
      }
    } else if (campaign.subscriber_selection_mode === 'areas' && campaign.area_of_interest_ids && campaign.area_of_interest_ids.trim() !== '') {
      // By areas of interest
      try {
        const areaIds = JSON.parse(campaign.area_of_interest_ids);
        if (areaIds.length > 0) {
          const placeholders = areaIds.map(() => '?').join(',');
          recipients = await database.all(`
            SELECT DISTINCT s.* FROM subscribers s
            JOIN subscriber_areas_of_interest sa ON s.id = sa.subscriber_id
            WHERE sa.area_of_interest_id IN (${placeholders}) AND s.status = 'active'
          `, areaIds);
          console.log(`📋 [DEBUG] Found ${recipients.length} subscribers by areas`);
        }
      } catch (e) {
        console.log(`📋 [DEBUG] Error parsing area_of_interest_ids: ${e.message}`);
      }
    } else if (campaign.subscriber_ids && campaign.subscriber_ids.trim() !== '') {
      // Legacy: Specific subscribers (old format)
      try {
        const subscriberIds = JSON.parse(campaign.subscriber_ids);
        if (subscriberIds.length > 0) {
          const placeholders = subscriberIds.map(() => '?').join(',');
          recipients = await database.all(`
            SELECT * FROM subscribers
            WHERE id IN (${placeholders}) AND status = 'active'
          `, subscriberIds);
          console.log(`📋 [DEBUG] Found ${recipients.length} legacy subscribers`);
        }
      } catch (e) {
        console.log(`📋 [DEBUG] Error parsing legacy subscriber_ids: ${e.message}`);
      }
    } else if (campaign.area_of_interest_ids && campaign.area_of_interest_ids.trim() !== '') {
      // Legacy: By areas of interest (old format)
      try {
        const areaIds = JSON.parse(campaign.area_of_interest_ids);
        if (areaIds.length > 0) {
          const placeholders = areaIds.map(() => '?').join(',');
          recipients = await database.all(`
            SELECT DISTINCT s.* FROM subscribers s
            JOIN subscriber_areas_of_interest sa ON s.id = sa.subscriber_id
            WHERE sa.area_of_interest_id IN (${placeholders}) AND s.status = 'active'
          `, areaIds);
          console.log(`📋 [DEBUG] Found ${recipients.length} legacy area subscribers`);
        }
      } catch (e) {
        console.log(`📋 [DEBUG] Error parsing legacy area_of_interest_ids: ${e.message}`);
      }
    } else {
      // All active subscribers (default for 'all' mode or when no specific selection)
      console.log(`📋 [DEBUG] Using 'all active subscribers' mode`);
      recipients = await database.all('SELECT * FROM subscribers WHERE status = "active"');
      console.log(`📋 [DEBUG] Found ${recipients.length} total active subscribers`);
    }

    console.log(`👥 Found ${recipients.length} recipients for campaign ${id}`);

    res.json(recipients);

  } catch (error) {
    console.error('Error getting campaign recipients:', error);
    res.status(500).json({ error: 'Failed to get campaign recipients' });
  }
});

// Send campaign immediately (for "Send Now" button)
router.post('/:id/send', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`🚀 [SEND NOW] Triggering immediate send for campaign: ${id}`);

    // Check if campaign exists
    const campaign = await database.get('SELECT * FROM campaigns WHERE id = ?', [id]);
    if (!campaign) {
      return res.status(404).json({ error: 'Campaign not found' });
    }

    console.log(`📧 [SEND NOW] Campaign found: ${campaign.name} (Status: ${campaign.status})`);

    // Update campaign status to 'sending' for immediate processing
    const now = new Date().toISOString();
    await database.run(`
      UPDATE campaigns
      SET status = 'sending', updated_at = ?
      WHERE id = ?
    `, [now, id]);

    console.log(`✅ [SEND NOW] Campaign status updated to 'sending'`);

    // Import and trigger the campaign sending service
    const { campaignSendingService } = await import('../services/campaign-sending-service.js');

    console.log(`🔄 [SEND NOW] Triggering campaign sending service...`);
    await campaignSendingService.triggerSending();

    // Get updated campaign data
    const updatedCampaign = await database.get('SELECT * FROM campaigns WHERE id = ?', [id]);

    console.log(`📊 [SEND NOW] Campaign after sending - Status: ${updatedCampaign.status}, Sent: ${updatedCampaign.sent_count || 0}, Failed: ${updatedCampaign.failed_count || 0}`);

    res.json({
      success: true,
      message: `Campaign "${campaign.name}" has been triggered for immediate sending`,
      campaign: updatedCampaign
    });

  } catch (error) {
    console.error('❌ [SEND NOW] Error sending campaign:', error);
    res.status(500).json({ error: 'Failed to send campaign', details: error.message });
  }
});

// Send campaign batch
router.post('/:id/send-batch', async (req, res) => {
  try {
    const { id } = req.params;
    const now = new Date();
    const nowISO = now.toISOString();
    
    // Get campaign and email settings
    const campaign = await database.get('SELECT * FROM campaigns WHERE id = ?', [id]);
    if (!campaign) {
      return res.status(404).json({ error: 'Campaign not found' });
    }
    
    const emailSettings = await database.get('SELECT * FROM email_settings ORDER BY id DESC LIMIT 1');
    const hourlyLimit = emailSettings?.hourlyEmailLimit || Infinity;
    const dailyLimit = emailSettings?.dailyEmailLimit || Infinity;
    
    // Reset hourly count if hour window passed
    if (campaign.current_hour_window_start_date && 
        now.getTime() >= new Date(new Date(campaign.current_hour_window_start_date).getTime() + 60 * 60 * 1000).getTime()) {
      await database.run(`
        UPDATE campaigns SET 
          sent_in_current_hour_count = 0, 
          current_hour_window_start_date = NULL 
        WHERE id = ?
      `, [id]);
      campaign.sent_in_current_hour_count = 0;
      campaign.current_hour_window_start_date = null;
    }
    
    // Reset daily count if day window passed
    if (campaign.current_day_window_start_date && 
        now.getTime() >= new Date(new Date(campaign.current_day_window_start_date).getTime() + 24 * 60 * 60 * 1000).getTime()) {
      await database.run(`
        UPDATE campaigns SET 
          sent_in_current_day_count = 0, 
          current_day_window_start_date = NULL 
        WHERE id = ?
      `, [id]);
      campaign.sent_in_current_day_count = 0;
      campaign.current_day_window_start_date = null;
    }
    
    // Check rate limits
    if ((campaign.sent_in_current_hour_count || 0) >= hourlyLimit) {
      const nextEligibleHour = new Date(new Date(campaign.current_hour_window_start_date || nowISO).getTime() + 60 * 60 * 1000);
      await database.run('UPDATE campaigns SET next_batch_eligible_at = ? WHERE id = ?', [nextEligibleHour.toISOString(), id]);
      return res.status(429).json({ 
        error: `Hourly send limit (${hourlyLimit}) reached. Try again after ${nextEligibleHour.toLocaleTimeString()}.` 
      });
    }
    
    if ((campaign.sent_in_current_day_count || 0) >= dailyLimit) {
      const nextEligibleDay = new Date(new Date(campaign.current_day_window_start_date || nowISO).getTime() + 24 * 60 * 60 * 1000);
      await database.run('UPDATE campaigns SET next_batch_eligible_at = ? WHERE id = ?', [nextEligibleDay.toISOString(), id]);
      return res.status(429).json({ 
        error: `Daily send limit (${dailyLimit}) reached. Try again after ${nextEligibleDay.toLocaleDateString()}.` 
      });
    }
    
    const remainingRecipients = (campaign.total_recipients || 0) - (campaign.processed_recipients_count || 0);
    if (remainingRecipients <= 0) {
      await database.run(`
        UPDATE campaigns SET 
          status = 'sent', 
          sent_date = ?, 
          updated_at = ? 
        WHERE id = ?
      `, [nowISO, nowISO, id]);
      return res.json({ message: 'Campaign already fully sent.', campaign });
    }
    
    const batchSize = Math.min(remainingRecipients, Math.max(1, Math.floor(hourlyLimit / 4)), 50); // Increased from 20 to 50
    const actualSentInBatch = Math.min(
      batchSize, 
      hourlyLimit - (campaign.sent_in_current_hour_count || 0), 
      dailyLimit - (campaign.sent_in_current_day_count || 0)
    );
    
    if (actualSentInBatch <= 0) {
      return res.json({ message: "No messages sent in this batch due to rate limits.", campaign });
    }
    
    // Update campaign counters
    const newProcessedCount = (campaign.processed_recipients_count || 0) + actualSentInBatch;
    const newHourlyCount = (campaign.sent_in_current_hour_count || 0) + actualSentInBatch;
    const newDailyCount = (campaign.sent_in_current_day_count || 0) + actualSentInBatch;
    const newStatus = newProcessedCount >= (campaign.total_recipients || 0) ? 'sent' : 'sending';
    
    await database.run(`
      UPDATE campaigns SET
        processed_recipients_count = ?,
        sent_in_current_hour_count = ?,
        sent_in_current_day_count = ?,
        last_batch_sent_date = ?,
        current_hour_window_start_date = COALESCE(current_hour_window_start_date, ?),
        current_day_window_start_date = COALESCE(current_day_window_start_date, ?),
        status = ?,
        sent_date = CASE WHEN ? = 'sent' THEN ? ELSE sent_date END,
        next_batch_eligible_at = NULL,
        updated_at = ?
      WHERE id = ?
    `, [
      newProcessedCount, newHourlyCount, newDailyCount, nowISO, nowISO, nowISO,
      newStatus, newStatus, nowISO, nowISO, id
    ]);
    
    // Get updated campaign
    const updatedCampaign = await database.get('SELECT * FROM campaigns WHERE id = ?', [id]);
    
    res.json({ 
      message: `Successfully sent a batch of ${actualSentInBatch} messages. Total processed: ${newProcessedCount}.`,
      campaign: updatedCampaign
    });
    
  } catch (error) {
    console.error('Error sending campaign batch:', error);
    res.status(500).json({ error: 'Failed to send campaign batch' });
  }
});

// Update campaign targeting (for fixing recipient counts)
router.patch('/:id/targeting', async (req, res) => {
  try {
    const { id } = req.params;
    const { area_of_interest_ids, subscriber_selection_mode, total_recipients } = req.body;

    console.log(`🎯 [TARGETING] Updating targeting for campaign: ${id}`);
    console.log(`   Area IDs: ${area_of_interest_ids}`);
    console.log(`   Selection mode: ${subscriber_selection_mode}`);
    console.log(`   Total recipients: ${total_recipients}`);

    // Get campaign details
    const campaign = await database.get('SELECT * FROM campaigns WHERE id = ?', [id]);
    if (!campaign) {
      return res.status(404).json({ error: 'Campaign not found' });
    }

    // Update campaign targeting
    await database.run(`
      UPDATE campaigns
      SET area_of_interest_ids = ?, subscriber_selection_mode = ?, total_recipients = ?
      WHERE id = ?
    `, [area_of_interest_ids, subscriber_selection_mode, total_recipients, id]);

    console.log(`✅ [TARGETING] Campaign targeting updated successfully`);

    // Get updated campaign
    const updatedCampaign = await database.get('SELECT * FROM campaigns WHERE id = ?', [id]);

    res.json({
      message: 'Campaign targeting updated successfully',
      campaign: updatedCampaign
    });
  } catch (error) {
    console.error('❌ [TARGETING] Error updating campaign targeting:', error);
    res.status(500).json({ error: 'Failed to update campaign targeting' });
  }
});

// Fix campaigns endpoint (temporary debugging tool)
router.post('/fix-data', async (req, res) => {
  try {
    console.log('🔧 Starting campaign data fix...');
    
    // Get campaigns that need fixing
    const campaigns = await database.all(`
      SELECT id, name, status, total_recipients, sender_name, template_id
      FROM campaigns 
      WHERE total_recipients = 0 OR total_recipients IS NULL OR sender_name IS NULL
      ORDER BY created_at DESC
    `);
    
    console.log(`Found ${campaigns.length} campaigns to fix`);
    
    // Get active subscribers count
    const subscriberResult = await database.get(`
      SELECT COUNT(*) as count FROM subscribers WHERE status = 'active'
    `);
    
    const subscriberCount = subscriberResult.count;
    console.log(`Active subscribers: ${subscriberCount}`);
    
    // Get templates with sender defaults
    const templates = await database.all(`
      SELECT id, display_name, sender_name, sender_email 
      FROM campaign_templates 
      WHERE is_active = 1
    `);
    
    const templateMap = {};
    templates.forEach(template => {
      templateMap[template.id] = template;
    });
    
    const results = [];
    
    // Fix each campaign
    for (const campaign of campaigns) {
      console.log(`Fixing campaign: ${campaign.name} (${campaign.id})`);
      
      let recipientCount = campaign.total_recipients;
      let senderName = campaign.sender_name;
      let senderEmail = null;
      
      // Fix recipient count if it's 0 or null
      if (!recipientCount || recipientCount === 0) {
        recipientCount = subscriberCount;
      }
      
      // Fix sender name if it's null and campaign has a template
      if (!senderName && campaign.template_id && templateMap[campaign.template_id]) {
        const template = templateMap[campaign.template_id];
        senderName = template.sender_name;
        senderEmail = template.sender_email;
      }
      
      // Update the campaign
      await database.run(`
        UPDATE campaigns 
        SET total_recipients = ?, 
            sender_name = COALESCE(sender_name, ?), 
            sender_email = COALESCE(sender_email, ?)
        WHERE id = ?
      `, [recipientCount, senderName, senderEmail, campaign.id]);
      
      results.push({
        id: campaign.id,
        name: campaign.name,
        oldRecipients: campaign.total_recipients,
        newRecipients: recipientCount,
        oldSender: campaign.sender_name,
        newSender: senderName
      });
      
      console.log(`  ✅ Updated: Recipients=${recipientCount}, Sender="${senderName || 'NULL'}"`);
    }
    
    // Verify the fixes
    const updatedCampaigns = await database.all(`
      SELECT id, name, total_recipients, sender_name
      FROM campaigns 
      ORDER BY created_at DESC 
      LIMIT 5
    `);
    
    console.log('✅ Campaign data fix completed');
    
    res.json({
      success: true,
      message: `Fixed ${results.length} campaigns`,
      results: results,
      verification: updatedCampaigns
    });
    
  } catch (error) {
    console.error('❌ Error fixing campaign data:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to fix campaign data',
      details: error.message 
    });
  }
});

export default router;
// Get detailed delivery status for a campaign
router.get('/:id/delivery-status', async (req, res) => {
  try {
    const { id } = req.params;

    // Import the campaign sending service
    const { campaignSendingService } = await import('../services/campaign-sending-service.js');

    // Get detailed delivery status
    const deliveryStatus = await campaignSendingService.getCampaignDeliveryStatus(id);

    if (!deliveryStatus) {
      return res.status(404).json({ error: 'Campaign delivery status not found' });
    }

    res.json(deliveryStatus);
  } catch (error) {
    console.error('Error getting campaign delivery status:', error);
    res.status(500).json({ error: 'Failed to get campaign delivery status' });
  }
});

// Get all pending messages across all campaigns
router.get('/pending-messages/all', async (req, res) => {
  try {
    const { database } = await import('../database/connection.js');

    // Get all pending messages with campaign and recipient details
    const pendingMessages = await database.all(`
      SELECT
        csl.id,
        csl.campaign_id,
        csl.campaign_name,
        csl.recipient_name,
        csl.recipient_phone,
        csl.recipient_email,
        csl.channel,
        csl.status,
        csl.created_at,
        csl.message_id,
        csl.error_message
      FROM campaign_sending_logs csl
      WHERE csl.status = 'pending'
      ORDER BY csl.created_at DESC
    `);

    // Group by channel for easier processing
    const groupedMessages = {
      whatsapp: pendingMessages.filter(msg => msg.channel === 'whatsapp'),
      sms: pendingMessages.filter(msg => msg.channel === 'sms'),
      email: pendingMessages.filter(msg => msg.channel === 'email')
    };

    res.json({
      total: pendingMessages.length,
      by_channel: {
        whatsapp: groupedMessages.whatsapp.length,
        sms: groupedMessages.sms.length,
        email: groupedMessages.email.length
      },
      messages: groupedMessages
    });
  } catch (error) {
    console.error('Error getting pending messages:', error);
    res.status(500).json({ error: 'Failed to get pending messages' });
  }
});

// Update WhatsApp message status (must be before parameterized routes)
router.post('/whatsapp/update-status', async (req, res) => {
  try {
    const { messageId, status, timestamp } = req.body;

    if (!messageId || !status) {
      return res.status(400).json({ error: 'Message ID and status are required' });
    }

    if (!['sent', 'failed'].includes(status)) {
      return res.status(400).json({ error: 'Status must be either "sent" or "failed"' });
    }

    const { database } = await import('../database/connection.js');

    // Update the message status in campaign_sending_logs
    const result = await database.run(`
      UPDATE campaign_sending_logs
      SET status = ?, updated_at = ?
      WHERE id = ?
    `, [status, timestamp || new Date().toISOString(), messageId]);

    if (result.changes === 0) {
      return res.status(404).json({ error: 'Message not found' });
    }

    console.log(`📱 WhatsApp message ${messageId} marked as ${status}`);

    res.json({
      success: true,
      message: `Message marked as ${status}`,
      messageId,
      status
    });
  } catch (error) {
    console.error('Error updating WhatsApp message status:', error);
    res.status(500).json({ error: 'Failed to update message status' });
  }
});

// Get sending logs for a specific campaign and channel
router.get('/:id/sending-logs', async (req, res) => {
  try {
    const { id } = req.params;
    const { channel } = req.query;

    const { database } = await import('../database/connection.js');

    let query = `
      SELECT
        csl.id,
        csl.campaign_id,
        csl.campaign_name,
        csl.recipient_name,
        csl.recipient_phone,
        csl.recipient_email,
        csl.channel,
        csl.status,
        csl.created_at,
        csl.message_id,
        csl.error_message
      FROM campaign_sending_logs csl
      WHERE csl.campaign_id = ?
    `;

    const params = [id];

    // Add channel filter if specified
    if (channel && channel !== 'all') {
      query += ' AND csl.channel = ?';
      params.push(channel);
    }

    query += ' ORDER BY csl.created_at DESC';

    const logs = await database.all(query, params);

    res.json(logs);
  } catch (error) {
    console.error('Error getting campaign sending logs:', error);
    res.status(500).json({ error: 'Failed to get campaign sending logs' });
  }
});

export { router as campaignRoutes };
