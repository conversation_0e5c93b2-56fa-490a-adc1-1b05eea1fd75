// SMS Service implementation for multiple providers including Traccar SMS Gateway
// File: E:\Projects\CRM-AIstudio\services\smsService.ts

export interface SMSConfig {
  provider: 'myphoneexplorer' | 'twilio' | 'aws-sns' | 'textlocal' | 'msg91';
  enabled: boolean;

  // MyPhoneExplorer configuration
  myPhoneExplorerPath?: string;
  myPhoneExplorerConnected?: boolean;
  myPhoneExplorerPhoneNumber?: string;
  
  // Twilio
  twilioAccountSid?: string;
  twilioAuthToken?: string;
  twilioPhoneNumber?: string;
  
  // AWS SNS
  awsAccessKeyId?: string;
  awsSecretAccessKey?: string;
  awsRegion?: string;
  
  // Textlocal
  textlocalApiKey?: string;
  textlocalSender?: string;
  
  // MSG91
  msg91ApiKey?: string;
  msg91SenderId?: string;
  
  defaultCountryCode: string;
}

export interface SMSMessage {
  to: string;
  message: string;
  campaignId?: string;
  recipientName?: string;
}

export interface SMSResponse {
  success: boolean;
  messageId?: string;
  error?: string;
  provider?: string;
  timestamp?: string;
}

export class SMSService {
  private config: SMSConfig;

  constructor(config: SMSConfig) {
    this.config = config;
  }

  /**
   * Send SMS using configured provider
   */
  async sendSMS(message: SMSMessage): Promise<SMSResponse> {
    if (!this.config.enabled) {
      throw new Error('SMS service is not enabled');
    }

    const timestamp = new Date().toISOString();

    try {
      console.log(`📱 Sending SMS via ${this.config.provider}...`, {
        to: message.to,
        provider: this.config.provider
      });

      let result: SMSResponse;

      switch (this.config.provider) {
        case 'myphoneexplorer':
          result = await this.sendMyPhoneExplorerSMS(message);
          break;
        case 'twilio':
          result = await this.sendTwilioSMS(message);
          break;
        case 'aws-sns':
          result = await this.sendAWSSMS(message);
          break;
        case 'textlocal':
          result = await this.sendTextlocalSMS(message);
          break;
        case 'msg91':
          result = await this.sendMSG91SMS(message);
          break;
        default:
          throw new Error(`Unsupported SMS provider: ${this.config.provider}`);
      }

      return {
        ...result,
        provider: this.config.provider,
        timestamp
      };

    } catch (error) {
      console.error('❌ SMS service error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        provider: this.config.provider,
        timestamp
      };
    }
  }

  /**
   * MyPhoneExplorer SMS implementation
   */
  private async sendMyPhoneExplorerSMS(message: SMSMessage): Promise<SMSResponse> {
    if (!this.config.myPhoneExplorerPath) {
      throw new Error('MyPhoneExplorer path not configured');
    }

    try {
      // Call the backend MyPhoneExplorer endpoint
      const response = await fetch('/api/sms/myphoneexplorer/send-bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          messages: [{
            messageId: `frontend_${Date.now()}`,
            recipient: message.to,
            message: message.message
          }]
        })
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ MyPhoneExplorer SMS sent successfully:', result);

        if (result.success && result.results && result.results.length > 0) {
          const messageResult = result.results[0];
          return {
            success: messageResult.success,
            messageId: messageResult.messageId || `mpe_${Date.now()}`,
            error: messageResult.error
          };
        } else {
          throw new Error('MyPhoneExplorer SMS failed: No results returned');
        }
      } else {
        const errorText = await response.text();
        console.error('❌ MyPhoneExplorer SMS sending failed:', response.status, errorText);
        throw new Error(`MyPhoneExplorer SMS failed: HTTP ${response.status} - ${errorText}`);
      }

    } catch (error) {
      console.error('❌ MyPhoneExplorer SMS sending error:', error);
      throw error;
    }
  }

  /**
   * Twilio SMS implementation (placeholder)
   */
  private async sendTwilioSMS(message: SMSMessage): Promise<SMSResponse> {
    // TODO: Implement Twilio SMS sending
    throw new Error('Twilio SMS not implemented yet');
  }

  /**
   * AWS SNS implementation (placeholder)
   */
  private async sendAWSSMS(message: SMSMessage): Promise<SMSResponse> {
    // TODO: Implement AWS SNS SMS sending
    throw new Error('AWS SNS SMS not implemented yet');
  }

  /**
   * Textlocal SMS implementation (placeholder)
   */
  private async sendTextlocalSMS(message: SMSMessage): Promise<SMSResponse> {
    // TODO: Implement Textlocal SMS sending
    throw new Error('Textlocal SMS not implemented yet');
  }

  /**
   * MSG91 SMS implementation (placeholder)
   */
  private async sendMSG91SMS(message: SMSMessage): Promise<SMSResponse> {
    // TODO: Implement MSG91 SMS sending
    throw new Error('MSG91 SMS not implemented yet');
  }

  /**
   * Format phone number with country code
   */
  private formatPhoneNumber(phone: string): string {
    // Remove any non-digit characters except +
    let cleanPhone = phone.replace(/[^\d+]/g, '');
    
    // If doesn't start with +, add country code
    if (!cleanPhone.startsWith('+')) {
      cleanPhone = `+${this.config.defaultCountryCode}${cleanPhone}`;
    }
    
    return cleanPhone;
  }

  /**
   * Send bulk SMS messages
   */
  async sendBulkSMS(messages: SMSMessage[]): Promise<SMSResponse[]> {
    const results: SMSResponse[] = [];
    
    for (const message of messages) {
      try {
        const result = await this.sendSMS(message);
        results.push(result);
        
        // Add delay between messages to respect rate limits
        const delay = this.config.provider === 'myphoneexplorer' ? 2000 : 300; // MyPhoneExplorer needs more delay
        await new Promise(resolve => setTimeout(resolve, delay));
      } catch (error) {
        results.push({
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          provider: this.config.provider,
          timestamp: new Date().toISOString()
        });
      }
    }
    
    return results;
  }

  /**
   * Test SMS connection
   */
  async testConnection(): Promise<boolean> {
    try {
      switch (this.config.provider) {
        case 'myphoneexplorer':
          return await this.testMyPhoneExplorerConnection();
        case 'twilio':
          return await this.testTwilioConnection();
        case 'aws-sns':
          return await this.testAWSConnection();
        case 'textlocal':
          return await this.testTextlocalConnection();
        case 'msg91':
          return await this.testMSG91Connection();
        default:
          return false;
      }
    } catch {
      return false;
    }
  }

  /**
   * Test MyPhoneExplorer connection
   */
  private async testMyPhoneExplorerConnection(): Promise<boolean> {
    if (!this.config.myPhoneExplorerPath) {
      console.error('❌ MyPhoneExplorer path not configured');
      return false;
    }

    try {
      // Test the backend MyPhoneExplorer endpoint
      const response = await fetch('/api/sms/status', {
        method: 'GET'
      });

      if (response.ok) {
        const status = await response.json();
        console.log('✅ MyPhoneExplorer backend connection successful:', status);
        return true;
      } else {
        console.error('❌ MyPhoneExplorer backend connection failed:', response.status);
        return false;
      }
    } catch (error) {
      console.error('❌ MyPhoneExplorer connection test failed:', error);
      return false;
    }
  }

  /**
   * Test Traccar Local Service connection with detailed diagnostics (Legacy)
   */
  private async testTraccarConnection(): Promise<boolean> {
    if (!this.config.traccarLocalServiceUrl || !this.config.traccarLocalServicePort) {
      console.error('❌ Traccar Local Service configuration incomplete');
      return false;
    }

    const baseUrl = `${this.config.traccarLocalServiceUrl}:${this.config.traccarLocalServicePort}`;
    
    try {
      console.log(`🔍 Testing Traccar Local Service connection to: ${baseUrl}`);
      
      // Test the root endpoint first (GET request to see if SMS Gateway is responding)
      let rootUrl = `${baseUrl}/`;
      
      // Add API key parameter for Traccar SMS Gateway
      if (this.config.traccarAuthToken) {
        rootUrl += `?key=${encodeURIComponent(this.config.traccarAuthToken)}`;
      }
      
      try {
        console.log(`🔍 Testing SMS Gateway endpoint: ${rootUrl}`);
        
        const headers: Record<string, string> = {};
        
        // Use AbortController for timeout instead of timeout option
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout
        
        const response = await fetch(rootUrl, {
          method: 'GET',
          mode: 'no-cors', // Bypass CORS for testing
          headers,
          signal: controller.signal
        });
        
        clearTimeout(timeoutId);
        
        // With no-cors mode, we get an opaque response
        if (response.type === 'opaque') {
          console.log('✅ SMS Gateway responding (opaque response due to no-cors mode)');
          return true;
        }
        
        console.log(`📡 Response from ${rootUrl}: Status ${response.status}`);
        
        if (response.ok) {
          console.log('✅ Traccar SMS Gateway is responding');
          return true;
        } else if (response.status === 404) {
          // 404 might be OK - server is responding but GET not supported
          console.log('📡 Server responding but GET not supported (expected for SMS Gateway)');
          return true;
        } else {
          console.log(`⚠️ Server responded with status ${response.status}`);
          return true; // Server is responding, which is what we need
        }
        
      } catch (fetchError: any) {
        if (fetchError.name === 'AbortError') {
          console.error('❌ Connection timeout - SMS Gateway not responding');
          return false;
        } else {
          console.error('❌ Connection error:', fetchError.message);
          return false;
        }
      }
      
    } catch (error) {
      console.error('❌ Traccar Local Service connection test failed:', error);
      return false;
    }
  }

  /**
   * Test other provider connections (placeholders)
   */
  private async testTwilioConnection(): Promise<boolean> {
    // TODO: Implement Twilio connection test
    return false;
  }

  private async testAWSConnection(): Promise<boolean> {
    // TODO: Implement AWS SNS connection test
    return false;
  }

  private async testTextlocalConnection(): Promise<boolean> {
    // TODO: Implement Textlocal connection test
    return false;
  }

  private async testMSG91Connection(): Promise<boolean> {
    // TODO: Implement MSG91 connection test
    return false;
  }

  /**
   * Comprehensive diagnostics for SMS connection issues
   */
  async performDiagnostics(): Promise<{
    success: boolean;
    issues: string[];
    suggestions: string[];
    details: Record<string, any>;
  }> {
    const issues: string[] = [];
    const suggestions: string[] = [];
    const details: Record<string, any> = {};

    if (this.config.provider === 'myphoneexplorer') {
      return await this.diagnoseMyPhoneExplorerConnection();
    }

    return {
      success: false,
      issues: ['Provider not supported for diagnostics'],
      suggestions: ['Only MyPhoneExplorer diagnostics are currently available'],
      details: {}
    };
  }

  /**
   * Detailed MyPhoneExplorer diagnostics
   */
  private async diagnoseMyPhoneExplorerConnection(): Promise<{
    success: boolean;
    issues: string[];
    suggestions: string[];
    details: Record<string, any>;
  }> {
    const issues: string[] = [];
    const suggestions: string[] = [];
    const details: Record<string, any> = {};

    // Check configuration completeness
    if (!this.config.myPhoneExplorerPath) {
      issues.push('MyPhoneExplorer path not configured');
      suggestions.push('Enter the full path to MyPhoneExplorer.exe (e.g., C:\\Program Files (x86)\\MyPhoneExplorer\\MyPhoneExplorer.exe)');
    }

    if (!this.config.myPhoneExplorerPhoneNumber) {
      issues.push('Phone number not configured');
      suggestions.push('Enter the phone number of your Android device connected to MyPhoneExplorer');
    }

    details.configuredPath = this.config.myPhoneExplorerPath;
    details.phoneNumberSet = !!this.config.myPhoneExplorerPhoneNumber;
    details.connectionStatus = this.config.myPhoneExplorerConnected ? 'connected' : 'not_connected';

    // Test backend connectivity
    try {
      console.log('🔍 Testing MyPhoneExplorer backend connectivity...');

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      try {
        const response = await fetch('/api/sms/status', {
          method: 'GET',
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (response.ok) {
          const status = await response.json();
          details.backendStatus = 'connected';
          details.backendResponse = status;

          // Test MyPhoneExplorer endpoint specifically
          const mpeTestResponse = await fetch('/api/sms/myphoneexplorer/send-bulk', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ messages: [] })
          });

          if (mpeTestResponse.ok) {
            details.myPhoneExplorerEndpoint = 'available';
          } else {
            details.myPhoneExplorerEndpoint = 'error';
            issues.push('MyPhoneExplorer endpoint not responding correctly');
            suggestions.push('Check if backend server is running with MyPhoneExplorer support');
          }

        } else {
          details.backendStatus = 'error';
          issues.push(`Backend server error: ${response.status} ${response.statusText}`);
          suggestions.push('Check if the CRM backend server is running on port 3001');
        }

      } catch (fetchError: any) {
        clearTimeout(timeoutId);

        if (fetchError.name === 'AbortError') {
          details.backendStatus = 'timeout';
          issues.push('Backend connection timeout');
          suggestions.push('Check if CRM backend server is running and accessible');
        } else {
          details.backendStatus = 'network_error';
          details.error = fetchError.message;
          issues.push(`Backend connection failed: ${fetchError.message}`);
          suggestions.push('Verify CRM backend server is running on http://localhost:3001');
        }
      }

    } catch (error: any) {
      details.backendStatus = 'unknown_error';
      details.error = error.message;
      issues.push(`Diagnostic error: ${error.message}`);
      suggestions.push('Check browser console for detailed error information');
    }

    // MyPhoneExplorer specific checks
    if (this.config.myPhoneExplorerPath) {
      // Check if path looks valid
      if (!this.config.myPhoneExplorerPath.toLowerCase().includes('myphoneexplorer')) {
        issues.push('MyPhoneExplorer path may be incorrect');
        suggestions.push('Ensure path points to MyPhoneExplorer.exe file');
      }

      if (!this.config.myPhoneExplorerPath.toLowerCase().endsWith('.exe')) {
        issues.push('MyPhoneExplorer path should end with .exe');
        suggestions.push('Enter full path including MyPhoneExplorer.exe filename');
      }
    }

    // Connection status checks
    if (!this.config.myPhoneExplorerConnected) {
      issues.push('Phone not connected to MyPhoneExplorer');
      suggestions.push('Open MyPhoneExplorer on PC and connect your Android phone via USB or WiFi');
      suggestions.push('Test SMS sending manually in MyPhoneExplorer first');
    }

    const success = issues.length === 0 && details.backendStatus === 'connected';

    return {
      success,
      issues,
      suggestions,
      details
    };
  }

  /**
   * Detailed Traccar Local Service diagnostics (Legacy)
   */
  private async diagnoseTraccarConnection(): Promise<{
    success: boolean;
    issues: string[];
    suggestions: string[];
    details: Record<string, any>;
  }> {
    const issues: string[] = [];
    const suggestions: string[] = [];
    const details: Record<string, any> = {};

    // Check configuration completeness
    if (!this.config.traccarLocalServiceUrl) {
      issues.push('Phone IP address not configured');
      suggestions.push('Enter the IP address of your Android phone (e.g., http://*************)');
    }

    if (!this.config.traccarLocalServicePort) {
      issues.push('HTTP API port not configured');
      suggestions.push('Enter the port configured in SMS Gateway app (default: 8080)');
    }

    if (!this.config.traccarPhoneNumber) {
      issues.push('Phone number not configured');
      suggestions.push('Enter the phone number of the SMS Gateway device');
    }

    const baseUrl = `${this.config.traccarLocalServiceUrl}:${this.config.traccarLocalServicePort}`;
    details.configuredUrl = baseUrl;
    details.authTokenSet = !!this.config.traccarAuthToken;

    // Test network connectivity
    try {
      const url = new URL(baseUrl);
      details.parsedUrl = {
        protocol: url.protocol,
        hostname: url.hostname,
        port: url.port || this.config.traccarLocalServicePort
      };

      // Test the root endpoint that we know works from browser test
      const testUrl = `${baseUrl}/`;
      details.testUrl = testUrl;

      console.log(`🔍 Testing connectivity to: ${testUrl}`);
      
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      try {
        const response = await fetch(testUrl, {
          method: 'GET',
          mode: 'no-cors', // This bypasses CORS for testing
          signal: controller.signal,
          headers: this.config.traccarAuthToken ? {
            'Authorization': `Bearer ${this.config.traccarAuthToken}`
          } : {}
        });

        clearTimeout(timeoutId);
        
        // With no-cors mode, we can't read response details
        // but if we get here without error, the server is reachable
        if (response.type === 'opaque') {
          // This means the request reached the server but we can't read the response
          details.connectionStatus = 'success_but_opaque';
          details.httpResponse = {
            status: 'unknown (CORS restricted)',
            statusText: 'opaque response',
            type: response.type
          };
        } else {
          details.httpResponse = {
            status: response.status,
            statusText: response.statusText,
            headers: Object.fromEntries(response.headers.entries())
          };

          if (response.ok) {
            details.connectionStatus = 'success';
          } else if (response.status === 404) {
            details.connectionStatus = 'server_responding_but_get_not_supported';
            // This is actually expected - SMS Gateway might not support GET on root
            // But server is responding, which means it's working
            details.connectionStatus = 'success';
          } else if (response.status === 405) {
            details.connectionStatus = 'method_not_allowed_but_server_responding';
            // Method not allowed means server is responding - this is good
            details.connectionStatus = 'success';
          } else if (response.status === 401 || response.status === 403) {
            details.connectionStatus = 'authentication_failed';
            issues.push('Authentication failed');
            suggestions.push('Check if authentication token matches SMS Gateway app configuration');
          } else {
            details.connectionStatus = 'server_error';
            issues.push(`Server responded with error: ${response.status} ${response.statusText}`);
            suggestions.push('Check SMS Gateway app logs for errors');
          }
        }

      } catch (fetchError: any) {
        clearTimeout(timeoutId);
        
        if (fetchError.name === 'AbortError') {
          details.connectionStatus = 'timeout';
          issues.push('Connection timeout - phone not responding');
          suggestions.push('Check if phone is on same network and SMS Gateway app is running');
        } else if (fetchError.message.includes('Failed to fetch')) {
          details.connectionStatus = 'network_error';
          issues.push('Network connection failed');
          suggestions.push('Verify phone IP address and ensure devices are on same network');
        } else {
          details.connectionStatus = 'unknown_error';
          details.error = fetchError.message;
          issues.push(`Connection error: ${fetchError.message}`);
          suggestions.push('Check network connectivity and firewall settings');
        }
      }

    } catch (urlError) {
      details.connectionStatus = 'invalid_url';
      issues.push('Invalid URL format');
      suggestions.push('Check IP address format (e.g., http://*************)');
    }

    // Network diagnostics
    if (this.config.traccarLocalServiceUrl) {
      try {
        const url = new URL(this.config.traccarLocalServiceUrl);
        const hostname = url.hostname;
        
        // Check if it's a local network IP
        if (hostname.startsWith('192.168.') || hostname.startsWith('10.') || hostname.startsWith('172.')) {
          details.networkType = 'local';
          suggestions.push('Ensure both devices are connected to the same WiFi network');
        } else if (hostname === 'localhost' || hostname === '127.0.0.1') {
          details.networkType = 'localhost';
          issues.push('Using localhost - this will only work if SMS Gateway is on same device');
          suggestions.push('Use the actual IP address of the phone instead of localhost');
        } else {
          details.networkType = 'external';
          suggestions.push('Using external IP - ensure proper port forwarding if needed');
        }
      } catch {
        // Invalid URL already handled above
      }
    }

    const success = issues.length === 0 && (
      details.connectionStatus === 'success' || 
      details.connectionStatus === 'success_but_opaque'
    );

    return {
      success,
      issues,
      suggestions,
      details
    };
  }

  /**
   * Send test SMS
   */
  async sendTestSMS(phoneNumber: string, message: string): Promise<SMSResponse> {
    return await this.sendSMS({
      to: phoneNumber,
      message: message,
      campaignId: 'test'
    });
  }
}

/**
 * Initialize SMS service with configuration
 */
export const initializeSMSService = (config: SMSConfig): SMSService => {
  return new SMSService(config);
};

/**
 * Load SMS configuration from localStorage or API
 */
export const loadSMSConfig = (): SMSConfig | null => {
  try {
    const savedConfig = localStorage.getItem('smsConfig');
    if (savedConfig) {
      return JSON.parse(savedConfig);
    }
    return null;
  } catch (error) {
    console.error('Error loading SMS configuration:', error);
    return null;
  }
};

/**
 * Save SMS configuration to localStorage
 */
export const saveSMSConfig = (config: SMSConfig): void => {
  try {
    localStorage.setItem('smsConfig', JSON.stringify(config));
    console.log('✅ SMS configuration saved');
  } catch (error) {
    console.error('Error saving SMS configuration:', error);
    throw error;
  }
};
