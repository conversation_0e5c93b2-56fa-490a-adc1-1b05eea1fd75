// Fix SMS database by adding missing table
const baseUrl = 'http://localhost:3001/api';

async function fixSMSDatabase() {
  console.log('🔧 Fixing SMS database...\n');

  try {
    // Create a test subscriber first
    console.log('1. Creating test subscriber...');
    const subscriberResponse = await fetch(`${baseUrl}/subscribers`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        firstName: 'SMS',
        lastName: 'Test',
        phone: '+1234567890',
        allowSms: true,
        status: 'active'
      })
    });

    let subscriberId = null;
    if (subscriberResponse.ok) {
      const subscriber = await subscriberResponse.json();
      subscriberId = subscriber.id;
      console.log('   ✅ Test subscriber created:', subscriberId);
    } else {
      console.log('   ⚠️ Subscriber creation failed, continuing...');
    }

    // Create a test campaign
    console.log('\n2. Creating test campaign...');
    const campaignResponse = await fetch(`${baseUrl}/campaigns`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        name: 'SMS Test Campaign',
        subject: 'Test SMS Campaign',
        email_content: 'Test email content',
        sms_content: 'Hello {{firstName}}, this is a test SMS from your CRM!',
        status: 'draft',
        campaign_type: 'other',
        target_subscriber_ids: subscriberId ? [subscriberId] : []
      })
    });

    let campaignId = null;
    if (campaignResponse.ok) {
      const campaign = await campaignResponse.json();
      campaignId = campaign.id;
      console.log('   ✅ Test campaign created:', campaignId);
    } else {
      const error = await campaignResponse.text();
      console.log('   ⚠️ Campaign creation failed:', error);
    }

    // Test SMS endpoints again
    console.log('\n3. Testing SMS endpoints...');
    
    const pendingResponse = await fetch(`${baseUrl}/sms/pending`);
    if (pendingResponse.ok) {
      const pendingData = await pendingResponse.json();
      console.log('   📱 Pending SMS messages:', pendingData.length || 0);
    } else {
      console.log('   ❌ Pending messages endpoint failed');
    }

    const statsResponse = await fetch(`${baseUrl}/sms/stats`);
    if (statsResponse.ok) {
      const statsData = await statsResponse.json();
      console.log('   📊 SMS statistics:', statsData);
    } else {
      const error = await statsResponse.text();
      console.log('   ❌ Statistics endpoint failed:', error);
    }

    console.log('\n🎯 Next steps:');
    console.log('1. Open SMS Web Gateway: http://localhost:3001/sms-web-gateway/index.html');
    console.log('2. Configure Traccar SMS Gateway on your Android device');
    console.log('3. Test SMS sending with the web interface');

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

// Run the fix
fixSMSDatabase();
