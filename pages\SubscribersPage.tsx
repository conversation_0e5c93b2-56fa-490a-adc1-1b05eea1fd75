import React, { useState, useMemo, useEffect, useRef, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import ReactDOM from 'react-dom'; 
import Header from '../components/Header';
import Table, { Column } from '../components/Table';
import ColumnSelector from '../components/ColumnSelector';
import { Subscriber, SubscriberProfileStatus, AreaOfInterest, AuditActionType, UserRole } from '../types';
import { PlusIcon, EditIcon, DeleteIcon, UploadIcon, DownloadIcon, FileTextIcon, EyeIcon } from '../components/icons';
import { addAuditLog } from '../utils/auditUtilsDB';
import { useAuth } from '../contexts/AuthContextDB'; 
import { canUserViewItem, canUserEditDeleteItem, AccessibleItem } from '../utils/accessControlUtils';
import { formatDateForDisplay, isValidIndianBirthDateInput, parseIndianDateStringToDate, formatDateToYyyyMmDd, parseExcelDateValue } from '../utils/dateUtils';
import ConfirmationModal from '../components/ConfirmationModal';
import ImportResultModal from '../components/ImportResultModal';
import ToastNotification from '../components/ToastNotification';
import { subscriberService } from '../services/SubscriberService-API';
import { areaOfInterestService } from '../services/AreaOfInterestService-API';
import { browserDatabaseService } from '../services/BrowserDatabaseService';
import * as XLSX from 'xlsx';

const SUBSCRIBERS_TABLE_KEY = 'subscribersListTable';

const AreasOfInterestCell: React.FC<{ aoiIds: string[]; allAreas: AreaOfInterest[] }> = ({ aoiIds, allAreas }) => {
  const [showPopover, setShowPopover] = useState(false);
  const [popoverPosition, setPopoverPosition] = useState<{top: number, left: number} | null>(null);
  const cellRef = useRef<HTMLSpanElement>(null);

  const getAoiData = useMemo(() => {
    if (!aoiIds || aoiIds.length === 0) return { display: 'None', fullList: [], isTruncated: false };
    const names = aoiIds.map(id => allAreas.find(aoi => aoi.id === id)?.name || id).sort((a,b) => a.localeCompare(b));
    const maxDisplayCount = 2; 
    if (names.length <= maxDisplayCount) return { display: names.join(', '), fullList: names, isTruncated: false };
    return { display: `${names.slice(0, maxDisplayCount).join(', ')}, +${names.length - maxDisplayCount} more`, fullList: names, isTruncated: true };
  }, [aoiIds, allAreas]);

  const { display, fullList, isTruncated } = getAoiData;

  const handleMouseEnter = () => {
    if (isTruncated && cellRef.current) {
      const rect = cellRef.current.getBoundingClientRect();
      setPopoverPosition({ top: rect.bottom + window.scrollY + 5, left: rect.left + window.scrollX });
      setShowPopover(true);
    }
  };
  const handleMouseLeave = () => setShowPopover(false);

  if (fullList.length === 0) return <span className="text-gray-500">None</span>;

  return (
    <div className="relative" onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
      <span ref={cellRef} className={`inline-block ${isTruncated ? 'cursor-default' : ''}`}>{display}</span>
      {showPopover && popoverPosition && isTruncated && ReactDOM.createPortal(
        <div className="fixed bg-white border border-gray-300 rounded-md shadow-xl p-3 z-50 min-w-[200px] max-w-xs" style={{ top: `${popoverPosition.top}px`, left: `${popoverPosition.left}px`, whiteSpace: 'normal' }}>
          <h4 className="text-xs font-semibold text-gray-500 mb-2 border-b pb-1">Full List:</h4>
          <ul className="space-y-1 max-h-48 overflow-y-auto">{fullList.map((name, index) => <li key={index} className="text-xs text-gray-700 py-0.5">{name}</li>)}</ul>
        </div>,
        document.body 
      )}
    </div>
  );
};


const SubscribersPage: React.FC = () => {
  const navigate = useNavigate();
  const { currentUser } = useAuth(); 
  const [searchTerm, setSearchTerm] = useState('');
  const [subscribers, setSubscribers] = useState<Subscriber[]>([]);
  const [areasOfInterest, setAreasOfInterest] = useState<AreaOfInterest[]>([]);
  const [feedbackMessage, setFeedbackMessage] = useState<{type: 'success' | 'error' | 'info', message: string} | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedAreaOfInterestFilter, setSelectedAreaOfInterestFilter] = useState<string>('');
  const [showNoAreasFilter, setShowNoAreasFilter] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [subscriberToDelete, setSubscriberToDelete] = useState<Subscriber | null>(null);

  // Bulk delete state (Admin only)
  const [selectedSubscriberIds, setSelectedSubscriberIds] = useState<Set<string>>(new Set());
  const [showBulkDeleteModal, setShowBulkDeleteModal] = useState(false);
  
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);
  const [importResult, setImportResult] = useState<{ importedCount: number; skippedCount: number; errors: string[]; } | null>(null);
  const [importedSubscriberIds, setImportedSubscriberIds] = useState<string[]>([]);

  const defaultVisibleColumnIds = currentUser?.role === UserRole.ADMIN
    ? ['select', 'name', 'email', 'phone', 'entityName', 'status', 'areasOfInterest', 'actions']
    : ['name', 'email', 'phone', 'entityName', 'status', 'areasOfInterest', 'actions'];
  const [visibleColumnIds, setVisibleColumnIds] = useState<string[]>(defaultVisibleColumnIds);

  const showFeedback = useCallback((type: 'success' | 'error' | 'info', message: string, duration: number = 8000) => {
    setFeedbackMessage({ type, message });
  }, []);

  const loadSubscribers = useCallback(async () => {
    try {
      const allSubscribers = await subscriberService.getAllSubscribers();
      setSubscribers(allSubscribers.filter(s => canUserViewItem(s as AccessibleItem, currentUser)));
    } catch (error) {
      console.error('Error loading subscribers data:', error);
      showFeedback('error', 'Failed to load subscribers data. Please refresh the page.');
    }
  }, [currentUser, showFeedback]);

  useEffect(() => {
    const loadInitialData = async () => {
      try {
        // Load table preferences first
        if (currentUser?.user_id) {
          const sql = `
            SELECT table_preferences
            FROM users
            WHERE user_id = ?
          `;
          const result = await browserDatabaseService.query(sql, [currentUser.user_id]);
          if (result.length > 0 && result[0].table_preferences) {
            const preferences = JSON.parse(result[0].table_preferences);
            if (preferences[SUBSCRIBERS_TABLE_KEY]) {
              setVisibleColumnIds(preferences[SUBSCRIBERS_TABLE_KEY]);
              console.log('✅ Loaded saved column preferences:', preferences[SUBSCRIBERS_TABLE_KEY]);
            }
          }
        }

        // Check for URL parameters to set initial filter state
        const urlParams = new URLSearchParams(window.location.search);
        const filterParam = urlParams.get('filter');
        if (filterParam === 'no-areas') {
          setShowNoAreasFilter(true);
          showFeedback('info', 'Showing subscribers without areas of interest.');
        }

        // Then load subscribers and areas
        await loadSubscribers();
        const allAreas = await areaOfInterestService.getAllAreasOfInterest();
        setAreasOfInterest(allAreas.filter(aoi => canUserViewItem(aoi as AccessibleItem, currentUser)));
      } catch (error) {
        console.error('Error loading initial data:', error);
        showFeedback('error', 'Failed to load initial data.');
      }
    };
    loadInitialData();
  }, [currentUser, loadSubscribers, showFeedback]);

  const handleColumnVisibilityChange = async (newVisibleColumnIds: string[]) => {
    console.log('Saving new columns:', newVisibleColumnIds);
    setVisibleColumnIds(newVisibleColumnIds);
    
    if (currentUser?.user_id) {
      try {
        const sql = `
          UPDATE users 
          SET table_preferences = json_set(
            COALESCE(table_preferences, '{}'),
            '$."${SUBSCRIBERS_TABLE_KEY}"',
            json(?)
          )
          WHERE user_id = ?
        `;
        await browserDatabaseService.query(sql, [JSON.stringify(newVisibleColumnIds), currentUser.user_id]);
        console.log(`✅ Saved column preferences for table ${SUBSCRIBERS_TABLE_KEY}`);
      } catch (error) {
        console.error(`❌ Failed to save column preferences for table ${SUBSCRIBERS_TABLE_KEY}:`, error);
      }
    }
  };
  const handleAddSubscriber = () => navigate('/subscribers/add');
  const handleViewSubscriber = (subscriberId: string) => navigate(`/subscribers/view/${subscriberId}`);
  const handleEditSubscriber = (subscriberId: string) => navigate(`/subscribers/edit/${subscriberId}`);
  const handleDeleteSubscriber = (subscriber: Subscriber) => {
    setSubscriberToDelete(subscriber);
    setShowDeleteModal(true);
  };

  const confirmDeleteSubscriber = async () => {
    if (!subscriberToDelete) return;
    try {
      await subscriberService.deleteSubscriber(subscriberToDelete.id);
      await loadSubscribers();
      addAuditLog(AuditActionType.DELETE, 'Subscriber', `Deleted subscriber ${subscriberToDelete.email}`, { entityId: subscriberToDelete.id, userId: currentUser?.user_id });
      showFeedback('success', `Subscriber "${subscriberToDelete.email}" deleted successfully.`);
    } catch (error) {
      showFeedback('error', 'Failed to delete subscriber.');
    } finally {
      setShowDeleteModal(false);
      setSubscriberToDelete(null);
    }
  };

  // Bulk delete functions (Admin only)
  const handleSelectSubscriber = (subscriberId: string, isSelected: boolean) => {
    setSelectedSubscriberIds(prev => {
      const newSet = new Set(prev);
      if (isSelected) {
        newSet.add(subscriberId);
      } else {
        newSet.delete(subscriberId);
      }
      return newSet;
    });
  };

  const handleSelectAllSubscribers = (isSelected: boolean) => {
    if (isSelected) {
      setSelectedSubscriberIds(new Set(filteredSubscribers.map(s => s.id)));
    } else {
      setSelectedSubscriberIds(new Set());
    }
  };

  const handleBulkDelete = () => {
    if (currentUser?.role !== UserRole.ADMIN) {
      showFeedback('error', 'Access Denied: Only Admins can delete multiple subscribers.');
      return;
    }
    if (selectedSubscriberIds.size === 0) {
      showFeedback('info', 'Please select subscribers to delete.');
      return;
    }
    setShowBulkDeleteModal(true);
  };

  const confirmBulkDelete = async () => {
    if (currentUser?.role !== UserRole.ADMIN) {
      showFeedback('error', 'Access Denied: Only Admins can delete multiple subscribers.');
      return;
    }

    try {
      const subscribersToDelete = subscribers.filter(s => selectedSubscriberIds.has(s.id));
      const deletePromises = Array.from(selectedSubscriberIds).map(id =>
        subscriberService.deleteSubscriber(id)
      );

      await Promise.all(deletePromises);
      await loadSubscribers();

      // Log bulk delete action
      const deletedEmails = subscribersToDelete.map(s => s.email).join(', ');
      addAuditLog(
        AuditActionType.DELETE,
        'SubscriberList',
        `Bulk deleted ${selectedSubscriberIds.size} subscribers: ${deletedEmails}`,
        { userId: currentUser?.user_id }
      );

      showFeedback('success', `Successfully deleted ${selectedSubscriberIds.size} subscribers.`);
      setSelectedSubscriberIds(new Set());
    } catch (error) {
      console.error('Error during bulk delete:', error);
      showFeedback('error', 'Failed to delete some subscribers. Please try again.');
    } finally {
      setShowBulkDeleteModal(false);
    }
  };

  const handleExportSubscribers = async () => {
    if (currentUser?.role !== UserRole.ADMIN) {
      showFeedback('error', 'Access Denied: Only Admins can export subscribers.');
      return;
    }

    try {
      const workbook = XLSX.utils.book_new();
      const worksheetData = filteredSubscribers.map(subscriber => ({
        'First Name': subscriber.firstName,
        'Last Name': subscriber.lastName,
        'Email': subscriber.email,
        'Entity Name': subscriber.entityName,
        'Phone': subscriber.phone,
        'Birth Date': subscriber.birthDate,
        'Status': subscriber.status,
        'Areas of Interest': subscriber.areasOfInterestIds
          .map(id => areasOfInterest.find(aoi => aoi.id === id)?.name || id)
          .join('; '),
        'Allow WhatsApp': subscriber.allowWhatsApp,
        'Allow SMS': subscriber.allowSms
      }));

      const worksheet = XLSX.utils.json_to_sheet(worksheetData);
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Subscribers');
      XLSX.writeFile(workbook, 'subscribers_export.xlsx');
      
      addAuditLog(AuditActionType.EXPORT, 'SubscriberList', `Exported ${filteredSubscribers.length} subscribers`, { userId: currentUser?.user_id });
      showFeedback('success', `Exported ${filteredSubscribers.length} subscribers successfully.`);
    } catch (error) {
      console.error('Error exporting subscribers:', error);
      showFeedback('error', 'Failed to export subscribers.');
    }
  };
  
  const handleDownloadTemplate = () => {
    const templateData = [
      {
        'firstName': 'John',
        'lastName': 'Doe',
        'email': '<EMAIL>',
        'entityName': 'ABC Company',
        'phone': '+91-9876543210',
        'birthDate': '15/08/1985',
        'status': 'active',
        'allowWhatsApp': 'true',
        'allowSms': 'true',
        'areasOfInterestIds_str': ''
      },
      {
        'firstName': 'Jane',
        'lastName': 'Smith',
        'email': '<EMAIL>',
        'entityName': 'XYZ Corp',
        'phone': '+91-9876543211',
        'birthDate': '22/12',
        'status': 'pending_confirmation',
        'allowWhatsApp': 'false',
        'allowSms': 'true',
        'areasOfInterestIds_str': ''
      }
    ];

    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(templateData);
    
    // Add column headers with descriptions
    const headers = [
      'firstName (Required)',
      'lastName (Optional)',
      'email (Required - Must be unique)',
      'entityName (Optional)',
      'phone (Optional)',
      'birthDate (Optional - dd/mm/yyyy or dd/mm)',
      'status (Optional - active/pending_confirmation/unsubscribed)',
      'allowWhatsApp (Optional - true/false)',
      'allowSms (Optional - true/false)',
      'areasOfInterestIds_str (Optional - semicolon separated IDs)'
    ];
    
    XLSX.utils.sheet_add_aoa(worksheet, [headers], { origin: 'A1' });
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Subscribers Template');
    XLSX.writeFile(workbook, 'subscribers_import_template.xlsx');
    
    showFeedback('success', 'Template downloaded successfully. Fill in your data and import.');
  };

  const getFullName = (item: Subscriber): string => `${item.firstName || ''} ${item.lastName || ''}`.trim();

  const handleCloseImportModal = () => {
    setIsImportModalOpen(false);
    setImportResult(null);
    setImportedSubscriberIds([]);
    loadSubscribers();
  };



  const handleConfirmAndNavigate = () => {
    console.log('[DEBUG] handleConfirmAndNavigate called with IDs:', importedSubscriberIds);
    if (importedSubscriberIds.length > 0) {
      const idsQueryParam = importedSubscriberIds.join(',');
      console.log('[DEBUG] IDs to pass:', idsQueryParam);

      // Store the IDs in sessionStorage as a backup
      sessionStorage.setItem('importedSubscriberIds', idsQueryParam);
      console.log('[DEBUG] Stored IDs in sessionStorage');

      // Close the modal first
      setIsImportModalOpen(false);
      setImportResult(null);

      // For HashRouter, navigate to the path first, then handle query parameters
      const basePath = `/subscribers/post-import-areas`;
      const queryParams = `?ids=${idsQueryParam}`;
      console.log('[DEBUG] Base path:', basePath);
      console.log('[DEBUG] Query params:', queryParams);
      console.log('[DEBUG] Current location before navigation:', window.location.href);
      console.log('[DEBUG] Current window.location.hash:', window.location.hash);
      console.log('[DEBUG] Current window.location.pathname:', window.location.pathname);

      // Try navigating with query parameters using React Router v6 state instead of URL params
      console.log('[DEBUG] Using React Router navigate with state instead of URL params');
      console.log('[DEBUG] About to navigate to:', basePath);
      console.log('[DEBUG] With state:', { importedSubscriberIds: importedSubscriberIds });

      navigate(basePath, {
        state: { importedSubscriberIds: importedSubscriberIds },
        replace: false
      });

      console.log('[DEBUG] Navigation called, current location after navigate:', window.location.href);

    } else {
      console.log('[DEBUG] No imported subscriber IDs, closing modal');
      handleCloseImportModal();
    }
  };

  const handleImportSubscribers = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (currentUser?.role !== UserRole.ADMIN) {
        showFeedback('error', 'Access Denied: Only Admins can import subscribers.');
        if (fileInputRef.current) fileInputRef.current.value = ""; 
        return;
    }
    const file = event.target.files?.[0];
    if (!file) return;

    try {
        const fileBuffer = await file.arrayBuffer();
        const workbook = XLSX.read(fileBuffer, { type: 'array', cellDates: true });
        const worksheet = workbook.Sheets[workbook.SheetNames[0]];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, raw: false });

        if (jsonData.length < 2) {
            setImportResult({ importedCount: 0, skippedCount: 0, errors: ['File must contain a header row and at least one data row.'] });
            setIsImportModalOpen(true);
            return;
        }
        
        const headers = (jsonData[0] as string[]).map(h => String(h || '').trim());
        const dataRows = jsonData.slice(1).filter(row => (row as any[]).some(cell => cell != null && String(cell).trim() !== ''));

        const emailHeaderIndex = headers.indexOf('email');
        if (emailHeaderIndex === -1) {
            setImportResult({ importedCount: 0, skippedCount: 0, errors: ['File must contain an "email" column header.'] });
            setIsImportModalOpen(true);
            return;
        }

        let skippedCount = 0;
        const newSubscribersToCreate: Omit<Subscriber, 'id' | 'created_at' | 'updated_at'>[] = [];
        const importErrors: string[] = [];
        
        const allCurrentSubscribers = await subscriberService.getAllSubscribers();
        const existingEmails = new Set(allCurrentSubscribers.map(s => s.email.toLowerCase()));
        const now = new Date().toISOString();
        const availableAoiIds = new Set(areasOfInterest.map(aoi => aoi.id));

        for (let i = 0; i < dataRows.length; i++) {
            const values = dataRows[i] as string[];
            const rowData: Record<string, any> = {};
            headers.forEach((header, index) => { rowData[header] = values[index] ?? ''; });

            const email = String(rowData.email || '').trim().toLowerCase();
            if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                importErrors.push(`Row ${i + 2}: Invalid or missing email.`);
                skippedCount++;
                continue;
            }

            if (existingEmails.has(email)) {
                skippedCount++;
                continue;
            }
            
            let birthDateForStorage: string | undefined = undefined;
            if (rowData.birthDate) {
                const parseResult = parseExcelDateValue(rowData.birthDate, { allowPartialDates: true });
                
                if (parseResult) {
                    const { date, format, isPartialDate } = parseResult;
                    
                    // Validate date is not in the future
                    const today = new Date();
                    today.setHours(23, 59, 59, 999); // End of today
                    
                    if (date > today) {
                        importErrors.push(`Row ${i + 2}: Birth date for ${email} cannot be in the future.`);
                    } else {
                        // Store based on whether it's a partial date (dd/mm) or full date
                        if (isPartialDate) {
                            // Store as MM-DD for birthdays without year
                            birthDateForStorage = `${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
                        } else {
                            // Store as YYYY-MM-DD for full dates
                            birthDateForStorage = formatDateToYyyyMmDd(date);
                        }
                        
                        console.log(`[DEBUG] Row ${i + 2}: Parsed date ${rowData.birthDate} (${format}) -> ${birthDateForStorage}`);
                    }
                } else if (String(rowData.birthDate).trim()) {
                    importErrors.push(`Row ${i + 2}: Invalid birth date format for ${email}. Supported formats: dd/mm, dd/mm/yyyy, yyyy-mm-dd, mm/dd/yyyy, Excel dates.`);
                }
            }


            const newSubscriber: Omit<Subscriber, 'id' | 'created_at' | 'updated_at'> = {
                email,
                firstName: String(rowData.firstName || ''),
                lastName: String(rowData.lastName || ''),
                entityName: String(rowData.entityName || ''),
                phone: String(rowData.phone || ''),
                birthDate: birthDateForStorage,
                status: Object.values(SubscriberProfileStatus).includes(rowData.status) ? rowData.status : SubscriberProfileStatus.PENDING_CONFIRMATION,
                subscribed_at: now,
                areasOfInterestIds: String(rowData.areasOfInterestIds_str || '').split(';').map(id => id.trim()).filter(id => availableAoiIds.has(id)),
                allowWhatsApp: ['true', '1', 'yes'].includes(String(rowData.allowWhatsApp || 'true').toLowerCase()),
                allowSms: ['true', '1', 'yes'].includes(String(rowData.allowSms || 'true').toLowerCase()),
                is_admin_only: false,
                owner_user_id: undefined,
            };
            newSubscribersToCreate.push(newSubscriber);
            existingEmails.add(email);
        }

        if (newSubscribersToCreate.length > 0) {
            console.log('[DEBUG] About to create subscribers:', newSubscribersToCreate.length);
            const batchResult = await subscriberService.batchCreateSubscribers(newSubscribersToCreate);
            console.log('[DEBUG] Batch result:', batchResult);
            
            const importedCount = batchResult.createdSubscribers.length;
            const finalSkippedCount = skippedCount + batchResult.errorCount;

            addAuditLog(AuditActionType.IMPORT, 'SubscriberList', `Import from ${file.name}. Success: ${importedCount}, Skipped/Failed: ${finalSkippedCount}`, { userId: currentUser?.user_id });

            setImportResult({ importedCount, skippedCount: finalSkippedCount, errors: importErrors });
            if (importedCount > 0) {
                console.log('[DEBUG] Setting imported subscriber IDs:', batchResult.createdSubscribers.map(s => s.id));
                setImportedSubscriberIds(batchResult.createdSubscribers.map(s => s.id));
            }
            setIsImportModalOpen(true);
        } else {
            console.log('[DEBUG] No subscribers to create');
            setImportResult({ importedCount: 0, skippedCount, errors: importErrors });
            setIsImportModalOpen(true);
        }

    } catch (error) {
        console.error("Error importing subscribers:", error);
        setImportResult({ importedCount: 0, skippedCount: 0, errors: [`An unexpected error occurred: ${(error as Error).message}`] });
        setIsImportModalOpen(true);
    } finally {
        if (fileInputRef.current) fileInputRef.current.value = "";
    }
  };

  const filteredSubscribers = useMemo(() => {
    let results = [...subscribers];

    // Filter by specific area of interest
    if (selectedAreaOfInterestFilter) {
      results = results.filter(s => s.areasOfInterestIds.includes(selectedAreaOfInterestFilter));
    }

    // Filter for subscribers with no areas of interest
    if (showNoAreasFilter) {
      results = results.filter(s => s.areasOfInterestIds.length === 0);
    }

    // Search filter
    if (searchTerm.trim()) {
        const lowercasedFilter = searchTerm.toLowerCase();
        results = results.filter(s =>
            (getFullName(s).toLowerCase()).includes(lowercasedFilter) ||
            (s.entityName?.toLowerCase() || '').includes(lowercasedFilter) ||
            s.email.toLowerCase().includes(lowercasedFilter) ||
            s.status.toLowerCase().includes(lowercasedFilter)
        );
    }
    return results;
  }, [subscribers, searchTerm, selectedAreaOfInterestFilter, showNoAreasFilter]);

  const allTableColumns: Column<Subscriber>[] = [
    // Checkbox column (Admin only)
    ...(currentUser?.role === UserRole.ADMIN ? [{
      id: 'select',
      header: 'Select',
      accessor: 'id' as const,
      render: (subscriber: Subscriber) => (
        <input
          type="checkbox"
          checked={selectedSubscriberIds.has(subscriber.id)}
          onChange={(e) => handleSelectSubscriber(subscriber.id, e.target.checked)}
          className="rounded border-gray-300 text-primary focus:ring-primary"
          title={`Select ${subscriber.email}`}
        />
      ),
      sortable: false,
      isNonRemovable: true
    }] : []),
    { id: 'name', header: 'Name', accessor: (item) => getFullName(item) || 'N/A', className: 'font-medium', sortable: true },
    { id: 'entityName', header: 'Entity Name', accessor: 'entityName', render: (item) => item.entityName || 'N/A', sortable: true },
    { id: 'email', header: 'Email', accessor: 'email', sortable: true },
    { id: 'phone', header: 'Mobile', accessor: 'phone', render: (item) => item.phone || 'N/A', sortable: true },
    { id: 'status', header: 'Status', accessor: 'status', render: (item) => {
        let colorClass = 'bg-gray-100 text-gray-700';
        if (item.status === SubscriberProfileStatus.ACTIVE) colorClass = 'bg-green-100 text-green-700';
        else if (item.status === SubscriberProfileStatus.UNSUBSCRIBED) colorClass = 'bg-red-100 text-red-700';
        return <span className={`px-2 py-1 text-xs font-semibold leading-tight rounded-full ${colorClass}`}>{item.status.charAt(0).toUpperCase() + item.status.slice(1)}</span>;
      }, sortable: true },
    { id: 'areasOfInterest', header: 'Areas of Interest', accessor: 'areasOfInterestIds',
      render: (item) => {
        if (item.areasOfInterestIds.length === 0) {
          return (
            <div className="flex items-center">
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                🚨 No Areas
              </span>
            </div>
          );
        }
        return <AreasOfInterestCell aoiIds={item.areasOfInterestIds} allAreas={areasOfInterest} />;
      },
      sortable: true,
      sortValue: (item) => item.areasOfInterestIds.length
    },
    { id: 'actions', header: 'Actions', accessor: 'id', render: (subscriber) => (
        <div className="flex space-x-2">
          <button type="button" onClick={() => handleViewSubscriber(subscriber.id)} className="text-green-600 hover:text-green-800 p-1" title="View"><EyeIcon className="h-5 w-5" /></button>
          <button type="button" onClick={() => handleEditSubscriber(subscriber.id)} className="text-primary p-1" title="Edit"><EditIcon className="h-5 w-5" /></button>
          <button type="button" onClick={() => handleDeleteSubscriber(subscriber)} className="text-red-500 p-1" title="Delete"><DeleteIcon className="h-5 w-5" /></button>
        </div>
      ), isNonRemovable: true },
  ];

  // Calculate statistics
  const subscribersWithoutAreas = subscribers.filter(s => s.areasOfInterestIds.length === 0);
  const totalSubscribers = subscribers.length;
  const subscribersWithAreas = totalSubscribers - subscribersWithoutAreas.length;

  return (
    <div>
      <Header title="Subscribers" subtitle="Manage your contact list and import or export data." />

      {/* Statistics Section */}
      <div className="mb-4 grid grid-cols-1 sm:grid-cols-3 gap-4">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-bold">{totalSubscribers}</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-blue-800">Total Subscribers</p>
              <p className="text-xs text-blue-600">All registered contacts</p>
            </div>
          </div>
        </div>

        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-bold">{subscribersWithAreas}</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-green-800">With Areas</p>
              <p className="text-xs text-green-600">Have areas of interest</p>
            </div>
          </div>
        </div>

        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-bold">{subscribersWithoutAreas.length}</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-red-800">No Areas</p>
              <p className="text-xs text-red-600">Need area assignment</p>
            </div>
          </div>
        </div>
      </div>

      {feedbackMessage && (
        <ToastNotification
          message={feedbackMessage.message}
          type={feedbackMessage.type}
          onClose={() => setFeedbackMessage(null)}
          duration={8000}
        />
      )}
       <div className="mb-6 flex flex-col sm:flex-row justify-between items-start gap-4">
        <div className="flex flex-col sm:flex-row items-center gap-4 w-full sm:w-auto">
            <input 
              type="text" 
              placeholder="Search subscribers..." 
              className="w-full sm:w-64 px-4 py-2 border rounded-lg bg-surface text-textPrimary border-border focus:ring-2 focus:ring-primary focus:border-transparent placeholder-textSecondary
                dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:placeholder-gray-400" 
              value={searchTerm} 
              onChange={(e) => setSearchTerm(e.target.value)}
              aria-label="Search subscribers"
              title="Search subscribers"
            />
            <select
              value={selectedAreaOfInterestFilter}
              onChange={(e) => {
                setSelectedAreaOfInterestFilter(e.target.value);
                if (e.target.value) setShowNoAreasFilter(false); // Clear "No Areas" filter when selecting specific area
              }}
              className="w-full sm:w-auto px-3 py-2 border rounded-lg"
              aria-label="Filter by area of interest"
              title="Filter by area of interest"
            >
                <option value="">All Areas of Interest</option>
                {areasOfInterest.map(aoi => <option key={aoi.id} value={aoi.id}>{aoi.name}</option>)}
            </select>

            {/* No Areas Filter Button */}
            <button
              type="button"
              onClick={() => {
                setShowNoAreasFilter(!showNoAreasFilter);
                if (!showNoAreasFilter) setSelectedAreaOfInterestFilter(''); // Clear area filter when showing "No Areas"
              }}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                showNoAreasFilter
                  ? 'bg-orange-500 text-white hover:bg-orange-600'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
              title={showNoAreasFilter ? 'Show all subscribers' : 'Show only subscribers with no areas of interest'}
            >
              {showNoAreasFilter ? '🔍 No Areas (Active)' : '🔍 No Areas'}
            </button>
        </div>
        <div className="flex flex-wrap items-center gap-2 w-full sm:w-auto justify-start sm:justify-end">
            <ColumnSelector allColumns={allTableColumns} visibleColumnIds={visibleColumnIds} onSave={handleColumnVisibilityChange} defaultVisibleColumnIds={defaultVisibleColumnIds} tableKey={SUBSCRIBERS_TABLE_KEY} userId={currentUser?.user_id} />
            {currentUser?.role === UserRole.ADMIN && (
                <>
                    <button onClick={handleDownloadTemplate} className="bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-lg flex items-center text-sm"><FileTextIcon className="h-4 w-4 mr-2" />Template</button>
            <button onClick={handleExportSubscribers} className="bg-purple-500 hover:bg-purple-600 text-white font-semibold py-2 px-4 rounded-lg flex items-center text-sm"><DownloadIcon className="h-4 w-4 mr-2" />Export</button>
                    <input 
                      type="file" 
                      ref={fileInputRef} 
                      onChange={handleImportSubscribers} 
                      accept=".csv,.xlsx" 
                      className="hidden"
                      aria-label="Import subscribers file"
                      title="Import subscribers file"
                    />
                    <button onClick={() => fileInputRef.current?.click()} className="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded-lg flex items-center text-sm"><UploadIcon className="h-4 w-4 mr-2" />Import</button>
                </>
            )}
             <button onClick={handleAddSubscriber} className="bg-primary hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg flex items-center text-sm"><PlusIcon className="h-4 w-4 mr-2" />Add New</button>
        </div>
      </div>

      {/* Bulk Delete Controls (Admin only) */}
      {currentUser?.role === UserRole.ADMIN && (
        <div className="mb-4 flex items-center justify-between bg-gray-50 border border-gray-200 rounded-lg p-3">
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                checked={selectedSubscriberIds.size > 0 && selectedSubscriberIds.size === filteredSubscribers.length}
                onChange={(e) => handleSelectAllSubscribers(e.target.checked)}
                className="rounded border-gray-300 text-primary focus:ring-primary"
                title="Select all subscribers"
              />
              <label className="ml-2 text-sm font-medium text-gray-700">
                Select All ({filteredSubscribers.length})
              </label>
            </div>
            {selectedSubscriberIds.size > 0 && (
              <div className="text-sm text-gray-600">
                <span className="font-medium">{selectedSubscriberIds.size}</span> subscriber{selectedSubscriberIds.size !== 1 ? 's' : ''} selected
              </div>
            )}
          </div>
          {selectedSubscriberIds.size > 0 && (
            <button
              type="button"
              onClick={handleBulkDelete}
              className="bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded-lg flex items-center text-sm transition-colors"
              title={`Delete ${selectedSubscriberIds.size} selected subscriber${selectedSubscriberIds.size !== 1 ? 's' : ''}`}
            >
              <DeleteIcon className="h-4 w-4 mr-2" />
              Delete Selected ({selectedSubscriberIds.size})
            </button>
          )}
        </div>
      )}

      <Table<Subscriber> allColumns={allTableColumns} visibleColumnIds={visibleColumnIds} data={filteredSubscribers} rowKey="id" onRowDoubleClick={(subscriber) => handleEditSubscriber(subscriber.id)} userId={currentUser?.user_id} />
      
      {showDeleteModal && subscriberToDelete && (
        <ConfirmationModal isOpen={showDeleteModal} title="Confirm Deletion" message={<>Are you sure you want to delete subscriber: <strong>{getFullName(subscriberToDelete)} ({subscriberToDelete.email})</strong>?</>} onConfirm={confirmDeleteSubscriber} onCancel={() => { setShowDeleteModal(false); setSubscriberToDelete(null); }} confirmText="Delete" />
      )}

      {/* Bulk Delete Confirmation Modal */}
      {showBulkDeleteModal && (
        <ConfirmationModal
          isOpen={showBulkDeleteModal}
          title="Delete Multiple Subscribers"
          message={<>Are you sure you want to delete <strong>{selectedSubscriberIds.size} selected subscriber{selectedSubscriberIds.size !== 1 ? 's' : ''}</strong>? This action cannot be undone.</>}
          onConfirm={confirmBulkDelete}
          onCancel={() => setShowBulkDeleteModal(false)}
          confirmText={`Delete ${selectedSubscriberIds.size} Subscriber${selectedSubscriberIds.size !== 1 ? 's' : ''}`}
        />
      )}
      {isImportModalOpen && importResult && (
        <ImportResultModal
          isOpen={isImportModalOpen}
          result={importResult}
          onClose={handleCloseImportModal}
          onConfirm={handleConfirmAndNavigate}
        />
      )}
    </div>
  );
};

export default SubscribersPage;
