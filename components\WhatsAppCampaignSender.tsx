import React, { useState } from 'react';
import { whatsappService, WhatsAppMessage } from '../services/WhatsAppService';

interface WhatsAppCampaignSenderProps {
  campaign: {
    id: string;
    name: string;
    whatsapp_content: string;
  };
  subscribers: Array<{
    id: string;
    name: string;
    phone?: string;
    whatsapp_number?: string;
  }>;
  onComplete?: (result: { total: number; successful: number; failed: number }) => void;
  onProgress?: (current: number, total: number) => void;
}

const WhatsAppCampaignSender: React.FC<WhatsAppCampaignSenderProps> = ({
  campaign,
  subscribers,
  onComplete,
  onProgress
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState({ current: 0, total: 0 });
  const [status, setStatus] = useState<string>('');

  const isGloballyEnabled = whatsappService.isGloballyEnabled();

  const handleSendCampaign = async () => {
    if (!isGloballyEnabled) {
      setStatus('WhatsApp is globally disabled');
      return;
    }

    if (!campaign.whatsapp_content) {
      setStatus('No WhatsApp content in campaign');
      return;
    }

    // Filter subscribers with valid WhatsApp numbers
    const validSubscribers = subscribers.filter(subscriber => {
      const phone = subscriber.whatsapp_number || subscriber.phone;
      return phone && whatsappService.isValidPhoneNumber(phone);
    });

    if (validSubscribers.length === 0) {
      setStatus('No subscribers with valid WhatsApp numbers');
      return;
    }

    setIsLoading(true);
    setProgress({ current: 0, total: validSubscribers.length });
    setStatus(`Preparing to send ${validSubscribers.length} WhatsApp messages...`);

    try {
      // Prepare messages
      const messages: WhatsAppMessage[] = validSubscribers.map(subscriber => ({
        to: subscriber.whatsapp_number || subscriber.phone || '',
        message: campaign.whatsapp_content.replace(/\{name\}/g, subscriber.name),
        subscriberId: subscriber.id,
        subscriberName: subscriber.name
      }));

      // Check if we should use bulk automation or individual sending
      const config = whatsappService.getConfiguration();
      
      if (config.desktopEnabled && messages.length > 5) {
        // Use bulk desktop automation for large batches
        setStatus('Starting bulk WhatsApp automation...');
        
        const result = await whatsappService.sendBulkViaDesktop(messages);
        
        setProgress({ current: result.total, total: result.total });
        setStatus(`Bulk sending completed: ${result.successful} successful, ${result.failed} failed`);
        
        if (onComplete) {
          onComplete(result);
        }
      } else {
        // Send individually with progress updates
        let successful = 0;
        let failed = 0;
        
        for (let i = 0; i < messages.length; i++) {
          const message = messages[i];
          setProgress({ current: i + 1, total: messages.length });
          setStatus(`Sending message ${i + 1} of ${messages.length} to ${message.subscriberName}...`);
          
          if (onProgress) {
            onProgress(i + 1, messages.length);
          }
          
          try {
            const result = await whatsappService.sendMessage(message);
            
            if (result.success) {
              successful++;
            } else {
              failed++;
              console.error(`Failed to send to ${message.to}:`, result.error);
            }
          } catch (error) {
            failed++;
            console.error(`Error sending to ${message.to}:`, error);
          }
          
          // Add delay between individual messages
          if (i < messages.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 2000));
          }
        }
        
        setStatus(`Campaign completed: ${successful} successful, ${failed} failed`);
        
        if (onComplete) {
          onComplete({
            total: messages.length,
            successful,
            failed
          });
        }
      }
    } catch (error) {
      console.error('Campaign sending error:', error);
      setStatus(`Campaign failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      
      if (onComplete) {
        onComplete({
          total: validSubscribers.length,
          successful: 0,
          failed: validSubscribers.length
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Don't render if globally disabled
  if (!isGloballyEnabled) {
    return (
      <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
        <p className="text-gray-600 text-sm">
          WhatsApp messaging is globally disabled. Enable it in WhatsApp Configuration to send campaigns.
        </p>
      </div>
    );
  }

  const validSubscriberCount = subscribers.filter(subscriber => {
    const phone = subscriber.whatsapp_number || subscriber.phone;
    return phone && whatsappService.isValidPhoneNumber(phone);
  }).length;

  return (
    <div className="space-y-4">
      {/* Campaign Info */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="font-medium text-blue-900 mb-2">WhatsApp Campaign: {campaign.name}</h3>
        <p className="text-sm text-blue-800 mb-2">
          Ready to send to {validSubscriberCount} subscribers with valid WhatsApp numbers
        </p>
        {campaign.whatsapp_content && (
          <div className="bg-white border border-blue-200 rounded p-3 mt-2">
            <p className="text-sm text-gray-700 font-medium mb-1">Message Preview:</p>
            <p className="text-sm text-gray-600 whitespace-pre-wrap">
              {campaign.whatsapp_content.replace(/\{name\}/g, '[Subscriber Name]')}
            </p>
          </div>
        )}
      </div>

      {/* Progress */}
      {isLoading && (
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">Sending Progress</span>
            <span className="text-sm text-gray-500">
              {progress.current} / {progress.total}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
            <div
              className="bg-green-600 h-2 rounded-full transition-all duration-300"
              style={{
                width: progress.total > 0 ? `${(progress.current / progress.total) * 100}%` : '0%'
              }}
            />
          </div>
          <p className="text-sm text-gray-600">{status}</p>
        </div>
      )}

      {/* Status */}
      {status && !isLoading && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <p className="text-sm text-gray-700">{status}</p>
        </div>
      )}

      {/* Send Button */}
      <div className="flex justify-end">
        <button
          type="button"
          onClick={handleSendCampaign}
          disabled={isLoading || validSubscriberCount === 0 || !campaign.whatsapp_content}
          className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Sending...' : `Send WhatsApp Campaign (${validSubscriberCount} recipients)`}
        </button>
      </div>
    </div>
  );
};

export default WhatsAppCampaignSender;
