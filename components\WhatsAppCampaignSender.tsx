// WhatsApp Campaign Sender Component
// components/WhatsAppCampaignSender.tsx

import React, { useState } from 'react';
import { CheckCircleIcon, XCircleIcon, CogIcon } from './icons';
import { whatsappDualService, WhatsAppSendOptions, WhatsAppConfiguration } from '../services/WhatsAppDualService';

interface Subscriber {
  id: string;
  name: string;
  phone?: string;
  whatsapp_number?: string;
  email: string;
}

interface Campaign {
  id: string;
  name: string;
  whatsapp_content: string;
  total_recipients?: number;
}

interface WhatsAppCampaignSenderProps {
  campaign: Campaign;
  subscribers: Subscriber[];
  onSendComplete?: (result: {
    total: number;
    successful: number;
    failed: number;
    method: 'api' | 'desktop';
  }) => void;
  className?: string;
}

interface SendResult {
  total: number;
  successful: number;
  failed: number;
  results: Array<{
    phone: string;
    name: string;
    success: boolean;
    messageId?: string;
    error?: string;
  }>;
}

const WhatsAppCampaignSender: React.FC<WhatsAppCampaignSenderProps> = ({
  campaign,
  subscribers,
  onSendComplete,
  className = ''
}) => {
  const [sendMethod, setSendMethod] = useState<'api' | 'desktop' | 'automation'>('automation');
  const [sendStatus, setSendStatus] = useState<'idle' | 'sending' | 'completed' | 'error'>('idle');
  const [sendResult, setSendResult] = useState<SendResult | null>(null);
  const [progress, setProgress] = useState({ current: 0, total: 0 });
  const [statusMessage, setStatusMessage] = useState('');

  // Get configuration to check available methods
  const config: WhatsAppConfiguration = whatsappDualService.getConfiguration();

  // Filter subscribers with valid WhatsApp numbers
  const validSubscribers = subscribers.filter(sub => {
    const phone = sub.whatsapp_number || sub.phone;
    return phone && whatsappDualService.isValidPhoneNumber(phone);
  });

  // Check if methods are available
  const isApiAvailable = config.apiConfig.enabled && config.apiConfig.accessToken;
  const isDesktopAvailable = config.desktopConfig.enabled;
  const isAutomationAvailable = config.automationConfig?.enabled;

  // Auto-select available method
  React.useEffect(() => {
    if (isAutomationAvailable) {
      setSendMethod('automation');
    } else if (!isApiAvailable && isDesktopAvailable) {
      setSendMethod('desktop');
    } else if (isApiAvailable && !isDesktopAvailable) {
      setSendMethod('api');
    }
  }, [isApiAvailable, isDesktopAvailable, isAutomationAvailable]);

  const handleSendWhatsApp = async () => {
    if (validSubscribers.length === 0) {
      setStatusMessage('No subscribers with valid WhatsApp numbers found.');
      return;
    }

    if (!campaign.whatsapp_content?.trim()) {
      setStatusMessage('Campaign WhatsApp content is empty.');
      return;
    }

    setSendStatus('sending');
    setProgress({ current: 0, total: validSubscribers.length });
    setStatusMessage(`Preparing to send ${validSubscribers.length} WhatsApp messages...`);

    try {
      // Prepare messages
      const messages: WhatsAppSendOptions[] = validSubscribers.map(subscriber => ({
        method: sendMethod,
        to: subscriber.whatsapp_number || subscriber.phone || '',
        message: campaign.whatsapp_content.replace(/\{name\}/g, subscriber.name)
      }));

      let result;

      if (sendMethod === 'api') {
        // API bulk sending
        setStatusMessage('Sending messages via WhatsApp API...');
        result = await whatsappDualService.sendBulkMessages(messages);

      } else if (sendMethod === 'automation') {
        // Automation bulk sending with progress updates
        setStatusMessage('Starting nut.js automation sending...');

        result = await sendBulkWithProgress(messages);

      } else {
        // Desktop batch sending with progress updates
        setStatusMessage('Starting desktop batch sending...');

        result = await sendBulkWithProgress(messages);
      }

      // Process results
      const processedResult: SendResult = {
        total: result.total,
        successful: result.successful,
        failed: result.failed,
        results: result.results.map((r, index) => ({
          phone: r.phone,
          name: validSubscribers[index]?.name || 'Unknown',
          success: r.success,
          messageId: r.messageId,
          error: r.error
        }))
      };

      setSendResult(processedResult);
      setSendStatus('completed');
      setStatusMessage(`Campaign sent! ${result.successful} successful, ${result.failed} failed.`);

      // Call completion callback
      if (onSendComplete) {
        onSendComplete({
          total: result.total,
          successful: result.successful,
          failed: result.failed,
          method: sendMethod
        });
      }

    } catch (error) {
      console.error('Failed to send WhatsApp campaign:', error);
      setSendStatus('error');
      setStatusMessage(`Failed to send campaign: ${error.message}`);
    }
  };

  // Send with progress updates for desktop method
  const sendBulkWithProgress = async (messages: WhatsAppSendOptions[]) => {
    const results = [];
    let successful = 0;
    let failed = 0;

    for (let i = 0; i < messages.length; i++) {
      const message = messages[i];
      setProgress({ current: i + 1, total: messages.length });
      setStatusMessage(`Sending message ${i + 1} of ${messages.length}...`);

      try {
        const result = await whatsappDualService.sendMessage(message);
        
        results.push({
          phone: message.to,
          success: result.success,
          messageId: result.messageId,
          error: result.error,
          method: sendMethod
        });

        if (result.success) {
          successful++;
        } else {
          failed++;
        }

        // Add delay for desktop and automation methods
        if ((sendMethod === 'desktop' || sendMethod === 'automation') && i < messages.length - 1) {
          const delay = sendMethod === 'automation' ? 3000 : config.desktopConfig.batchDelay * 1000;
          await new Promise(resolve => setTimeout(resolve, delay));
        }

      } catch (error) {
        results.push({
          phone: message.to,
          success: false,
          error: error.message,
          method: sendMethod
        });
        failed++;
      }
    }

    return {
      total: messages.length,
      successful,
      failed,
      results
    };
  };

  const getMethodRecommendation = () => {
    const recommendation = whatsappDualService.recommendMethod(validSubscribers.length, 'medium');
    return recommendation;
  };

  const recommendation = getMethodRecommendation();

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="border-b border-gray-200 pb-4">
        <h3 className="text-lg font-semibold text-gray-900">WhatsApp Campaign Sender</h3>
        <p className="text-sm text-gray-600 mt-1">
          Send campaign to {validSubscribers.length} subscribers with valid WhatsApp numbers
        </p>
      </div>

      {/* Method Selection */}
      {(isApiAvailable || isDesktopAvailable || isAutomationAvailable) && (
        <div className="space-y-4">
          <div className="flex items-center space-x-4">
            <label className="text-sm font-medium text-gray-700">Sending Method:</label>
            <select
              value={sendMethod}
              onChange={(e) => setSendMethod(e.target.value as 'api' | 'desktop' | 'automation')}
              disabled={sendStatus === 'sending'}
              className="border border-gray-300 rounded px-3 py-1 text-sm focus:ring-2 focus:ring-green-500 focus:border-transparent"
            >
              {isAutomationAvailable && <option value="automation">🤖 nut.js Automation (Free)</option>}
              {isApiAvailable && <option value="api">📡 WhatsApp API (Paid)</option>}
              {isDesktopAvailable && <option value="desktop">🖥️ WhatsApp Desktop (Manual)</option>}
            </select>
          </div>

          {/* Smart Recommendation */}
          {recommendation && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <h4 className="font-medium text-blue-800 text-sm">💡 Smart Recommendation</h4>
              <p className="text-sm text-blue-700 mt-1">
                For {validSubscribers.length} messages, we recommend: <strong>{recommendation.method.toUpperCase()}</strong>
              </p>
              <p className="text-xs text-blue-600 mt-1">{recommendation.reason}</p>
            </div>
          )}

          {/* Method-specific Information */}
          {sendMethod === 'desktop' && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
              <h4 className="font-medium text-yellow-800 text-sm">📱 Desktop Sending Process</h4>
              <ol className="text-sm text-yellow-700 mt-2 space-y-1">
                <li>1. Each message will open in WhatsApp Web/Desktop</li>
                <li>2. Review the message and click Send manually</li>
                <li>3. Return to CRM and confirm to continue</li>
                <li>4. Process repeats for all {validSubscribers.length} recipients</li>
              </ol>
              <p className="text-xs text-yellow-600 mt-2">
                Delay between messages: {config.desktopConfig.batchDelay} seconds
              </p>
            </div>
          )}

          {sendMethod === 'automation' && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <h4 className="font-medium text-blue-800 text-sm">🤖 nut.js Automation Process</h4>
              <ul className="text-sm text-blue-700 mt-2 space-y-1">
                <li>• Messages sent automatically via WhatsApp Desktop</li>
                <li>• Completely free - no API costs</li>
                <li>• Uses your personal WhatsApp account</li>
                <li>• Automated typing and sending via nut.js</li>
                <li>• No manual intervention required</li>
              </ul>
              <p className="text-xs text-blue-600 mt-2">
                Ensure WhatsApp Desktop is open and logged in before starting.
              </p>
            </div>
          )}

          {sendMethod === 'api' && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-3">
              <h4 className="font-medium text-green-800 text-sm">🚀 API Sending Process</h4>
              <ul className="text-sm text-green-700 mt-2 space-y-1">
                <li>• Messages sent automatically via WhatsApp Business API</li>
                <li>• Real-time delivery status tracking</li>
                <li>• Bulk processing with rate limiting</li>
                <li>• No manual intervention required</li>
              </ul>
            </div>
          )}
        </div>
      )}

      {/* Status and Progress */}
      {sendStatus !== 'idle' && (
        <div className="space-y-3">
          {/* Status Message */}
          {statusMessage && (
            <div className={`p-3 rounded-lg flex items-center space-x-3 ${
              sendStatus === 'error' 
                ? 'bg-red-50 text-red-800 border border-red-200'
                : sendStatus === 'completed'
                ? 'bg-green-50 text-green-800 border border-green-200'
                : 'bg-blue-50 text-blue-800 border border-blue-200'
            }`}>
              {sendStatus === 'sending' && (
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-current"></div>
              )}
              {sendStatus === 'completed' && <CheckCircleIcon className="h-5 w-5" />}
              {sendStatus === 'error' && <XCircleIcon className="h-5 w-5" />}
              <span className="text-sm">{statusMessage}</span>
            </div>
          )}

          {/* Progress Bar */}
          {sendStatus === 'sending' && progress.total > 0 && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm text-gray-600">
                <span>Progress: {progress.current} of {progress.total}</span>
                <span>{Math.round((progress.current / progress.total) * 100)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(progress.current / progress.total) * 100}%` }}
                ></div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Results Summary */}
      {sendResult && sendStatus === 'completed' && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 space-y-3">
          <h4 className="font-medium text-gray-900">📊 Sending Results</h4>
          
          <div className="grid grid-cols-3 gap-4 text-center">
            <div className="bg-white rounded-lg p-3 border">
              <div className="text-2xl font-bold text-gray-900">{sendResult.total}</div>
              <div className="text-sm text-gray-600">Total</div>
            </div>
            <div className="bg-white rounded-lg p-3 border">
              <div className="text-2xl font-bold text-green-600">{sendResult.successful}</div>
              <div className="text-sm text-gray-600">Successful</div>
            </div>
            <div className="bg-white rounded-lg p-3 border">
              <div className="text-2xl font-bold text-red-600">{sendResult.failed}</div>
              <div className="text-sm text-gray-600">Failed</div>
            </div>
          </div>

          {/* Failed Messages Details */}
          {sendResult.failed > 0 && (
            <details className="mt-4">
              <summary className="cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900">
                View Failed Messages ({sendResult.failed})
              </summary>
              <div className="mt-2 space-y-2">
                {sendResult.results
                  .filter(r => !r.success)
                  .map((result, index) => (
                    <div key={index} className="text-sm bg-red-50 border border-red-200 rounded p-2">
                      <div className="font-medium text-red-800">{result.name} ({result.phone})</div>
                      <div className="text-red-600">{result.error}</div>
                    </div>
                  ))}
              </div>
            </details>
          )}
        </div>
      )}

      {/* Send Button */}
      {(!isApiAvailable && !isDesktopAvailable && !isAutomationAvailable) ? (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
          <CogIcon className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
          <h4 className="font-medium text-yellow-800">WhatsApp Not Configured</h4>
          <p className="text-sm text-yellow-700 mt-1">
            Please configure WhatsApp API or Desktop integration in Settings to send campaigns.
          </p>
        </div>
      ) : (
        <div className="flex justify-center">
          <button 
            onClick={handleSendWhatsApp}
            disabled={sendStatus === 'sending' || validSubscribers.length === 0 || !campaign.whatsapp_content?.trim()}
            className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {sendStatus === 'sending' ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-current"></div>
                <span>Sending...</span>
              </>
            ) : (
              <>
                <span>Send to {validSubscribers.length} Recipients via {sendMethod.toUpperCase()}</span>
              </>
            )}
          </button>
        </div>
      )}

      {/* Validation Messages */}
      {validSubscribers.length === 0 && subscribers.length > 0 && (
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 text-center">
          <p className="text-sm text-orange-700">
            No subscribers have valid WhatsApp numbers. Please update subscriber records with WhatsApp numbers.
          </p>
        </div>
      )}

      {!campaign.whatsapp_content?.trim() && (
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 text-center">
          <p className="text-sm text-orange-700">
            Please add WhatsApp content to the campaign before sending.
          </p>
        </div>
      )}
    </div>
  );
};

export default WhatsAppCampaignSender;