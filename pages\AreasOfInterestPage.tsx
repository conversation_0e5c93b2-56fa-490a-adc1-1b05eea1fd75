import React, { useState, useMemo, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Header from '../components/Header';
import Table, { Column } from '../components/Table';
import { AreaOfInterest, AuditActionType, Subscriber } from '../types';
import { PlusIcon, EditIcon, DeleteIcon, EyeIcon } from '../components/icons';
import { addAuditLog } from '../utils/auditUtils';
import ConfirmationModal from '../components/ConfirmationModal';
import { useAuth } from '../contexts/AuthContextDB';
import { areaOfInterestService } from '../services/AreaOfInterestService-API';
import { subscriberService } from '../services/SubscriberService-API';
import type { ApiError } from '../services/apiClient';
import SubscribersModal from '../components/SubscribersModal';
import EnhancedAreaSubscribersModal from '../components/EnhancedAreaSubscribersModal';
import ColumnSelector from '../components/ColumnSelector';

const AreasOfInterestPage: React.FC = () => {
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [areasOfInterest, setAreasOfInterest] = useState<AreaOfInterest[]>([]);
  const [loading, setLoading] = useState(true);
  const [feedbackMessage, setFeedbackMessage] = useState<string | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [areaToDelete, setAreaToDelete] = useState<AreaOfInterest | null>(null);
  const [showSubscribersModal, setShowSubscribersModal] = useState(false);
  const [selectedArea, setSelectedArea] = useState<AreaOfInterest | null>(null);
  const [subscribers, setSubscribers] = useState<Subscriber[]>([]);
  const [subscriberCounts, setSubscriberCounts] = useState<Record<string, number>>({});
  const [showEnhancedModal, setShowEnhancedModal] = useState(false);

  // Load areas from API
  // Load subscriber counts for all areas
  useEffect(() => {
    const loadSubscriberCounts = async () => {
      try {
        const counts: Record<string, number> = {};
        const allSubscribers = await subscriberService.getAllSubscribers();
        
        areasOfInterest.forEach(area => {
          counts[area.id] = allSubscribers.filter(
            (sub: Subscriber) => sub.areasOfInterestIds.includes(area.id)
          ).length;
        });

        setSubscriberCounts(counts);
      } catch (error) {
        console.error('Error loading subscriber counts:', error);
      }
    };

    if (areasOfInterest.length > 0) {
      loadSubscriberCounts();
    }
  }, [areasOfInterest]);

  // Load subscribers when modal is opened
  useEffect(() => {
    const loadSubscribersForArea = async () => {
      if (!selectedArea) return;
      
      try {
        const allSubscribers = await subscriberService.getAllSubscribers();
        const filtered = allSubscribers.filter(
          (sub: Subscriber) => sub.areasOfInterestIds.includes(selectedArea.id)
        );
        setSubscribers(filtered);
      } catch (error) {
        console.error('Error loading subscribers:', error);
        showFeedback('Error loading subscribers for this area');
      }
    };

    if (showSubscribersModal && selectedArea) {
      loadSubscribersForArea();
    }
  }, [showSubscribersModal, selectedArea]);

  useEffect(() => {
    const loadAreasOfInterest = async () => {
      try {
        setLoading(true);
        const data = await areaOfInterestService.getAllAreasOfInterest();
        setAreasOfInterest(data);
      } catch (error) {
        console.error('Error loading areas of interest:', error);
        const err = error as ApiError;
        showFeedback('Error loading areas of interest: ' + err.message);
      } finally {
        setLoading(false);
      }
    };

    loadAreasOfInterest();
  }, []);

  const showFeedback = (message: string) => {
    setFeedbackMessage(message);
    setTimeout(() => setFeedbackMessage(null), 3000);
  };

  const handleAddAreaOfInterest = () => {
    navigate('/areas-of-interest/add');
  };

  const handleViewAreaOfInterest = (areaId: string) => {
    navigate(`/areas-of-interest/view/${areaId}`);
  };

  const handleEditAreaOfInterest = (areaId: string) => {
    navigate(`/areas-of-interest/edit/${areaId}`);
  };

  const confirmDeleteAreaOfInterest = async () => {
    if (!areaToDelete) return;
    
    try {
      await areaOfInterestService.deleteAreaOfInterest(areaToDelete.id);
      const updatedAreas = areasOfInterest.filter(a => a.id !== areaToDelete.id);
      setAreasOfInterest(updatedAreas);
      addAuditLog(AuditActionType.DELETE, 'Area of Interest', { 
        entityId: areaToDelete.id, 
        entityName: areaToDelete.name,
        userId: currentUser?.user_id 
      });
      showFeedback(`Area of Interest "${areaToDelete.name}" deleted successfully.`);
    } catch (error) {
      console.error('Error deleting area:', error);
        const err = error as ApiError;
        showFeedback('Error deleting area: ' + err.message);
    }
    
    setShowDeleteModal(false);
    setAreaToDelete(null);
  };

  const handleDeleteAreaOfInterest = (area: AreaOfInterest) => {
    setAreaToDelete(area);
    setShowDeleteModal(true);
  };

  const handleManageSubscribers = (area: AreaOfInterest) => {
    setSelectedArea(area);
    setShowEnhancedModal(true);
  };

  const handleSubscriberAdded = (subscriber: any) => {
    if (selectedArea) {
      // Update subscriber count
      setSubscriberCounts(prev => ({
        ...prev,
        [selectedArea.id]: (prev[selectedArea.id] || 0) + 1
      }));
    }
  };

  const handleSubscriberRemoved = (subscriber: any) => {
    if (selectedArea) {
      // Update subscriber count
      setSubscriberCounts(prev => ({
        ...prev,
        [selectedArea.id]: Math.max((prev[selectedArea.id] || 0) - 1, 0)
      }));
    }
  };

  const allTableColumns: Column<AreaOfInterest>[] = [
    { 
      id: 'name',
      header: 'Name', 
      accessor: 'name', 
      className: 'font-medium',
      sortable: true,
      sortValue: item => item.name.toLowerCase()
    },
    { 
      id: 'description',
      header: 'Description', 
      accessor: 'description', 
      render: (item) => item.description || 'N/A',
      sortable: true,
      sortValue: item => item.description?.toLowerCase() || ''
    },
    {
      id: 'subscribers',
      header: 'Subscribers',
      accessor: 'id',
      render: (item) => {
        const count = subscriberCounts[item.id] || 0;
        return (
          <button
            type="button"
            className="text-primary cursor-pointer hover:underline font-medium"
            onClick={() => handleManageSubscribers(item)}
            title={`Manage subscribers for ${item.name}`}
          >
            Manage ({count})
          </button>
        );
      },
      sortable: true,
      sortValue: item => subscriberCounts[item.id] || 0
    },
    { 
      id: 'created_at',
      header: 'Created At', 
      accessor: 'created_at', 
      render: (item) => new Date(item.created_at).toLocaleDateString(),
      sortable: true,
      sortValue: item => new Date(item.created_at).getTime()
    },
    {
      id: 'actions',
      header: 'Actions',
      accessor: 'id',
      render: (area) => (
        <div className="flex space-x-2">
          <button
            onClick={() => handleViewAreaOfInterest(area.id)}
            className="text-green-600 hover:text-green-800 p-1"
            title={`View ${area.name}`}
            aria-label={`View ${area.name}`}
          >
            <EyeIcon className="h-5 w-5" />
          </button>
          <button
            onClick={() => handleEditAreaOfInterest(area.id)}
            className="text-primary hover:text-blue-700 p-1"
            title={`Edit ${area.name}`}
            aria-label={`Edit ${area.name}`}
          >
            <EditIcon className="h-5 w-5" />
          </button>
          <button
            onClick={() => handleDeleteAreaOfInterest(area)}
            className="text-red-500 hover:text-red-700 p-1"
            title={`Delete ${area.name}`}
            aria-label={`Delete ${area.name}`}
          >
            <DeleteIcon className="h-5 w-5" />
          </button>
        </div>
      )
    },
  ];

  const defaultVisibleColumnIds = ['name', 'description', 'subscribers', 'created_at', 'actions'];
  const [visibleColumnIds, setVisibleColumnIds] = useState<string[]>(defaultVisibleColumnIds);
  const [visibleColumns, setVisibleColumns] = useState<string[]>(defaultVisibleColumnIds);

  const filteredAreasOfInterest = useMemo(() => {
    if (!searchTerm.trim()) {
      return areasOfInterest;
    }
    const lowercasedFilter = searchTerm.toLowerCase();
    return areasOfInterest.filter(area =>
      area.name.toLowerCase().includes(lowercasedFilter) ||
      (area.description && area.description.toLowerCase().includes(lowercasedFilter))
    );
  }, [areasOfInterest, searchTerm]);

  if (loading) {
    return (
      <div className="text-textPrimary">
        <Header title="Areas of Interest" subtitle="Manage subscriber interest categories." />
        <div className="flex justify-center items-center py-8">
          <div className="text-lg text-textSecondary">Loading areas of interest...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="text-textPrimary">
      <Header title="Areas of Interest" subtitle="Manage subscriber interest categories." />
      
      {feedbackMessage && (
        <div className="mb-4 p-3 bg-green-100 text-green-700 border border-green-300 rounded-md shadow-sm text-sm dark:bg-green-700 dark:text-green-100 dark:border-green-500" role="alert">
          {feedbackMessage}
        </div>
      )}
      
      <div className="mb-6 flex flex-col sm:flex-row justify-between items-center gap-4">
        <div className="flex items-center gap-2 w-full sm:w-auto">
          <input
            type="text"
            placeholder="Search areas of interest..."
            className="px-4 py-2 border border-border bg-surface rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent flex-grow"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            aria-label="Search areas of interest"
          />
          <ColumnSelector 
            allColumns={allTableColumns} 
            visibleColumnIds={visibleColumns} 
            onSave={(newColumns) => {
              console.log('Saving new columns:', newColumns);
              setVisibleColumns(newColumns);
              setVisibleColumnIds(newColumns); // Maintain backward compatibility
            }}
            defaultVisibleColumnIds={defaultVisibleColumnIds}
            tableKey="areasOfInterestTable"
            userId={currentUser?.user_id}
          />
        </div>
        <button
          onClick={handleAddAreaOfInterest}
          className="w-full sm:w-auto font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transition duration-150 ease-in-out flex items-center justify-center text-white"
          style={{
            backgroundColor: 'var(--color-primary)',
            borderColor: 'var(--color-primary)'
          }}
          onMouseOver={(e) => {
            e.currentTarget.style.backgroundColor = 'var(--color-primary-dark)';
          }}
          onMouseOut={(e) => {
            e.currentTarget.style.backgroundColor = 'var(--color-primary)';
          }}
          aria-label="Add new area of interest"
        >
          <PlusIcon className="h-5 w-5 mr-2" />
          Add New Area
        </button>
      </div>
      
      <Table<AreaOfInterest> 
        allColumns={allTableColumns} 
        visibleColumnIds={visibleColumns}
        data={filteredAreasOfInterest} 
        caption="List of Areas of Interest" 
        rowKey="id"
        onRowDoubleClick={(area) => handleEditAreaOfInterest(area.id)}
        userId={currentUser?.user_id}
      />
      
      {showDeleteModal && areaToDelete && (
        <ConfirmationModal
          isOpen={showDeleteModal}
          title="Confirm Deletion"
          message={<>Are you sure you want to delete area: <strong>{areaToDelete.name}</strong>?</>}
          onConfirm={confirmDeleteAreaOfInterest}
          onCancel={() => { setShowDeleteModal(false); setAreaToDelete(null); }}
          confirmText="Delete"
        />
      )}

      {showSubscribersModal && selectedArea && (
        <SubscribersModal
          isOpen={showSubscribersModal}
          area={selectedArea}
          subscribers={subscribers}
          onClose={() => {
            setShowSubscribersModal(false);
            setSelectedArea(null);
          }}
        />
      )}

      {showEnhancedModal && selectedArea && (
        <EnhancedAreaSubscribersModal
          isOpen={showEnhancedModal}
          area={selectedArea}
          onClose={() => {
            setShowEnhancedModal(false);
            setSelectedArea(null);
          }}
          onSubscriberAdded={handleSubscriberAdded}
          onSubscriberRemoved={handleSubscriberRemoved}
        />
      )}
    </div>
  );
};

export default AreasOfInterestPage;
