<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">CRM SMS Gateway</string>
    <string name="server_url_hint">http://192.168.1.100:3001</string>
    <string name="test_connection">Test Connection</string>
    <string name="start_service">Start SMS Service</string>
    <string name="stop_service">Stop SMS Service</string>
    <string name="auto_mode">Auto Mode (Check every 5 seconds)</string>
    <string name="status_ready">Ready to connect</string>
    <string name="status_connected">Connected to CRM server</string>
    <string name="status_disconnected">Connection failed</string>
    <string name="pending_count">Pending: %d</string>
    <string name="last_sent">Last sent: %s</string>
    <string name="instructions">1. Enter your CRM server URL\n2. Test the connection\n3. Enable Auto Mode\n4. App will automatically send SMS from your CRM campaigns\n\n⚠️ Keep this app running in background for automatic SMS sending</string>
</resources>
