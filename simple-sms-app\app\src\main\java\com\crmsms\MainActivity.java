package com.crmsms;

import android.Manifest;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.telephony.SmsManager;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import org.json.JSONArray;
import org.json.JSONObject;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class MainActivity extends AppCompatActivity {
    
    private static final int SMS_PERMISSION_CODE = 101;
    private EditText serverUrlInput;
    private Switch autoModeSwitch;
    private Button testConnectionBtn, startStopBtn;
    private TextView statusText, messageCountText, lastMessageText;
    
    private Handler mainHandler;
    private ExecutorService executor;
    private Runnable smsChecker;
    private boolean isRunning = false;
    private String serverUrl = "http://192.168.1.100:3001";
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        initializeViews();
        setupClickListeners();
        requestSmsPermission();
        
        mainHandler = new Handler(Looper.getMainLooper());
        executor = Executors.newSingleThreadExecutor();
        
        // Load saved server URL
        loadSettings();
    }
    
    private void initializeViews() {
        serverUrlInput = findViewById(R.id.serverUrlInput);
        autoModeSwitch = findViewById(R.id.autoModeSwitch);
        testConnectionBtn = findViewById(R.id.testConnectionBtn);
        startStopBtn = findViewById(R.id.startStopBtn);
        statusText = findViewById(R.id.statusText);
        messageCountText = findViewById(R.id.messageCountText);
        lastMessageText = findViewById(R.id.lastMessageText);
        
        serverUrlInput.setText(serverUrl);
        updateStatus("Ready to connect", false);
    }
    
    private void setupClickListeners() {
        testConnectionBtn.setOnClickListener(v -> testConnection());
        startStopBtn.setOnClickListener(v -> toggleSmsService());
        
        autoModeSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked && !isRunning) {
                startSmsService();
            } else if (!isChecked && isRunning) {
                stopSmsService();
            }
        });
    }
    
    private void requestSmsPermission() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.SEND_SMS) 
            != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this, 
                new String[]{Manifest.permission.SEND_SMS}, SMS_PERMISSION_CODE);
        }
    }
    
    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == SMS_PERMISSION_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                showToast("SMS permission granted");
            } else {
                showToast("SMS permission required for app to work");
            }
        }
    }
    
    private void testConnection() {
        serverUrl = serverUrlInput.getText().toString().trim();
        if (serverUrl.isEmpty()) {
            showToast("Please enter server URL");
            return;
        }
        
        updateStatus("Testing connection...", false);
        testConnectionBtn.setEnabled(false);
        
        executor.execute(() -> {
            try {
                URL url = new URL(serverUrl + "/api/sms/status");
                HttpURLConnection conn = (HttpURLConnection) url.openConnection();
                conn.setRequestMethod("GET");
                conn.setConnectTimeout(5000);
                conn.setReadTimeout(5000);
                
                int responseCode = conn.getResponseCode();
                
                mainHandler.post(() -> {
                    testConnectionBtn.setEnabled(true);
                    if (responseCode == 200) {
                        updateStatus("Connected to CRM server", true);
                        showToast("Connection successful!");
                        saveSettings();
                    } else {
                        updateStatus("Connection failed (Code: " + responseCode + ")", false);
                        showToast("Connection failed");
                    }
                });
                
            } catch (Exception e) {
                mainHandler.post(() -> {
                    testConnectionBtn.setEnabled(true);
                    updateStatus("Connection error: " + e.getMessage(), false);
                    showToast("Connection error");
                });
            }
        });
    }
    
    private void toggleSmsService() {
        if (isRunning) {
            stopSmsService();
        } else {
            startSmsService();
        }
    }
    
    private void startSmsService() {
        if (serverUrl.isEmpty()) {
            showToast("Please test connection first");
            return;
        }
        
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.SEND_SMS) 
            != PackageManager.PERMISSION_GRANTED) {
            showToast("SMS permission required");
            requestSmsPermission();
            return;
        }
        
        isRunning = true;
        autoModeSwitch.setChecked(true);
        startStopBtn.setText("Stop SMS Service");
        updateStatus("SMS service running", true);
        
        smsChecker = new Runnable() {
            @Override
            public void run() {
                if (isRunning) {
                    checkForPendingMessages();
                    mainHandler.postDelayed(this, 5000); // Check every 5 seconds
                }
            }
        };
        
        mainHandler.post(smsChecker);
        showToast("SMS service started");
    }
    
    private void stopSmsService() {
        isRunning = false;
        autoModeSwitch.setChecked(false);
        startStopBtn.setText("Start SMS Service");
        updateStatus("SMS service stopped", false);
        
        if (smsChecker != null) {
            mainHandler.removeCallbacks(smsChecker);
        }
        
        showToast("SMS service stopped");
    }
    
    private void checkForPendingMessages() {
        executor.execute(() -> {
            try {
                URL url = new URL(serverUrl + "/api/sms/pending");
                HttpURLConnection conn = (HttpURLConnection) url.openConnection();
                conn.setRequestMethod("GET");
                conn.setConnectTimeout(5000);
                conn.setReadTimeout(5000);
                
                if (conn.getResponseCode() == 200) {
                    BufferedReader reader = new BufferedReader(
                        new InputStreamReader(conn.getInputStream()));
                    StringBuilder response = new StringBuilder();
                    String line;
                    
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    reader.close();
                    
                    processPendingMessages(response.toString());
                }
                
            } catch (Exception e) {
                mainHandler.post(() -> {
                    updateStatus("Error checking messages: " + e.getMessage(), false);
                });
            }
        });
    }
    
    private void processPendingMessages(String jsonResponse) {
        try {
            JSONArray messages = new JSONArray(jsonResponse);
            
            mainHandler.post(() -> {
                messageCountText.setText("Pending: " + messages.length());
            });
            
            for (int i = 0; i < messages.length(); i++) {
                JSONObject message = messages.getJSONObject(i);
                String messageId = message.getString("id");
                String recipient = message.getString("recipient");
                String messageText = message.getString("message");
                
                sendSmsMessage(messageId, recipient, messageText);
                
                // Small delay between messages
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    break;
                }
            }
            
        } catch (Exception e) {
            mainHandler.post(() -> {
                updateStatus("Error processing messages: " + e.getMessage(), false);
            });
        }
    }
    
    private void sendSmsMessage(String messageId, String recipient, String messageText) {
        try {
            SmsManager smsManager = SmsManager.getDefault();
            smsManager.sendTextMessage(recipient, null, messageText, null, null);
            
            mainHandler.post(() -> {
                lastMessageText.setText("Last sent: " + recipient);
                showToast("SMS sent to " + recipient);
            });
            
            // Report success to server
            reportMessageStatus(messageId, "sent", null);
            
        } catch (Exception e) {
            mainHandler.post(() -> {
                showToast("Failed to send SMS to " + recipient);
            });
            
            // Report failure to server
            reportMessageStatus(messageId, "failed", e.getMessage());
        }
    }
    
    private void reportMessageStatus(String messageId, String status, String error) {
        executor.execute(() -> {
            try {
                URL url = new URL(serverUrl + "/api/sms/status");
                HttpURLConnection conn = (HttpURLConnection) url.openConnection();
                conn.setRequestMethod("POST");
                conn.setRequestProperty("Content-Type", "application/json");
                conn.setDoOutput(true);
                
                JSONObject statusData = new JSONObject();
                statusData.put("messageId", messageId);
                statusData.put("status", status);
                statusData.put("timestamp", System.currentTimeMillis());
                if (error != null) {
                    statusData.put("error", error);
                }
                
                OutputStream os = conn.getOutputStream();
                os.write(statusData.toString().getBytes());
                os.flush();
                os.close();
                
                conn.getResponseCode(); // Execute request
                
            } catch (Exception e) {
                // Silently handle reporting errors
            }
        });
    }
    
    private void updateStatus(String status, boolean isConnected) {
        statusText.setText(status);
        statusText.setTextColor(isConnected ? 
            getResources().getColor(android.R.color.holo_green_dark) :
            getResources().getColor(android.R.color.holo_red_dark));
    }
    
    private void showToast(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }
    
    private void saveSettings() {
        getSharedPreferences("CRMSMSSettings", MODE_PRIVATE)
            .edit()
            .putString("serverUrl", serverUrl)
            .apply();
    }
    
    private void loadSettings() {
        serverUrl = getSharedPreferences("CRMSMSSettings", MODE_PRIVATE)
            .getString("serverUrl", "http://192.168.1.100:3001");
        serverUrlInput.setText(serverUrl);
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        stopSmsService();
        if (executor != null) {
            executor.shutdown();
        }
    }
}
