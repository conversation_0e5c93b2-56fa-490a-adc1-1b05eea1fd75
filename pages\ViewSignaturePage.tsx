import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeftIcon } from '../components/icons';
import { MessageSignature } from '../types';
import { signatureService } from '../services/SignatureService-API';

const ViewSignaturePage: React.FC = () => {
  const { signatureId } = useParams<{ signatureId: string }>();
  const navigate = useNavigate();
  const [signature, setSignature] = useState<MessageSignature | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadSignature = async () => {
      if (!signatureId) {
        setError('No signature ID provided');
        setLoading(false);
        return;
      }

      try {
        const signatureData = await signatureService.getSignatureById(signatureId);
        setSignature(signatureData);
      } catch (err) {
        console.error('Error loading signature:', err);
        setError('Failed to load signature details');
      } finally {
        setLoading(false);
      }
    };

    loadSignature();
  }, [signatureId]);

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not set';
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return dateString;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-textPrimary">Loading signature details...</div>
      </div>
    );
  }

  if (error || !signature) {
    return (
      <div className="min-h-screen bg-background p-6">
        <div className="max-w-4xl mx-auto">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h2 className="text-lg font-semibold text-red-800 mb-2">Error</h2>
            <p className="text-red-600">{error || 'Signature not found'}</p>
            <button
              onClick={() => navigate('/signatures')}
              className="mt-4 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
            >
              Back to Signatures
            </button>
          </div>
        </div>
      </div>
    );
  }

  const fieldClass = "block text-sm font-medium text-textPrimary mb-1";
  const valueClass = "w-full p-3 border border-border rounded-lg bg-gray-50 text-textPrimary";

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-4xl mx-auto p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate('/signatures')}
              className="flex items-center text-primary hover:text-opacity-80"
            >
              <ArrowLeftIcon className="h-5 w-5 mr-2" />
              Back to Signatures
            </button>
            <h1 className="text-2xl font-bold text-textPrimary">View Signature</h1>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => navigate(`/signatures/edit/${signature.id}`)}
              className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-opacity-90"
            >
              Edit Signature
            </button>
          </div>
        </div>

        {/* Signature Details */}
        <div className="bg-cardBackground border border-border rounded-lg shadow-sm">
          <div className="p-6">
            <h2 className="text-lg font-semibold text-textPrimary mb-6">Signature Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Basic Information */}
              <div>
                <label className={fieldClass}>Signature Name</label>
                <div className={valueClass}>{signature.name}</div>
              </div>

              <div>
                <label className={fieldClass}>Channel</label>
                <div className={valueClass}>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    signature.channel === 'email' ? 'bg-blue-100 text-blue-800' :
                    signature.channel === 'whatsapp' ? 'bg-green-100 text-green-800' :
                    'bg-purple-100 text-purple-800'
                  }`}>
                    {signature.channel.toUpperCase()}
                  </span>
                </div>
              </div>

              <div>
                <label className={fieldClass}>Created By</label>
                <div className={valueClass}>{signature.created_by || 'Not specified'}</div>
              </div>

              <div>
                <label className={fieldClass}>Created At</label>
                <div className={valueClass}>{formatDate(signature.created_at)}</div>
              </div>

              <div>
                <label className={fieldClass}>Last Updated</label>
                <div className={valueClass}>{formatDate(signature.updated_at)}</div>
              </div>

              <div>
                <label className={fieldClass}>Active</label>
                <div className={valueClass}>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    signature.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  }`}>
                    {signature.is_active ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>
            </div>

            {/* Content Section */}
            {signature.content && (
              <div className="mt-6">
                <label className={fieldClass}>Signature Content</label>
                <div className="w-full p-3 border border-border rounded-lg bg-gray-50 text-textPrimary min-h-[200px] whitespace-pre-wrap">
                  {signature.content}
                </div>
              </div>
            )}

            {/* Description */}
            {signature.description && (
              <div className="mt-6">
                <label className={fieldClass}>Description</label>
                <div className="w-full p-3 border border-border rounded-lg bg-gray-50 text-textPrimary min-h-[100px] whitespace-pre-wrap">
                  {signature.description}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewSignaturePage;
