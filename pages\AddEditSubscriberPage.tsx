
import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import Header from '../components/Header';
import { Subscriber, SubscriberProfileStatus, AreaOfInterest, AuditActionType, UserRole } from '../types';
import { DeleteIcon, LockClosedIcon } from '../components/icons';
import ToastNotification from '../components/ToastNotification';
// Removed localStorage imports - now using API services
import { formatEnumValueForDisplay } from '../utils/displayUtils';
import { addAuditLog } from '../utils/auditUtils';
import { useAuth } from '../contexts/AuthContextDB';
import { canUserEditDeleteItem, AccessibleItem } from '../utils/accessControlUtils';
import { isValidIndianBirthDateInput, parseIndianDateStringToDate, formatDateToYyyyMmDd, formatDateForDisplay } from '../utils/dateUtils';
import { subscriberService } from '../services/SubscriberService-API';
import { areaOfInterestService } from '../services/AreaOfInterestService-API';
import FloatingActionBar from '../components/FloatingActionBar';
// import MultiSelectDropdown from '../components/MultiSelectDropdown'; // Temporarily disabled

// --- START OF HARDCODED MOCK DATA (Used as initial seed for localStorage if empty) ---
const initialSubscribersSeed: Subscriber[] = [
  {
    id: 'subprof1', email: '<EMAIL>', firstName: 'John', lastName: 'Doe', entityName: 'Doe Industries', phone: '************',
    birthDate: '11-15', // Stored as MM-DD
    status: SubscriberProfileStatus.ACTIVE, subscribed_at: '2023-01-15T09:00:00Z', areasOfInterestIds: ['aoi1', 'aoi3'],
    allowWhatsApp: true, allowSms: true,
    created_at: '2023-01-15T09:00:00Z', updated_at: '2023-10-20T10:00:00Z',
    is_admin_only: false, owner_user_id: undefined,
  },
].map(p => ({ 
    ...p,
    allowWhatsApp: typeof p.allowWhatsApp === 'boolean' ? p.allowWhatsApp : true,
    allowSms: typeof p.allowSms === 'boolean' ? p.allowSms : true,
    birthDate: p.birthDate || undefined,
    entityName: p.entityName || '',
    is_admin_only: p.is_admin_only || false,
    owner_user_id: p.owner_user_id || undefined,
}));



const initialFormData: Omit<Subscriber, 'id' | 'created_at' | 'updated_at' | 'subscribed_at' | 'unsubscribed_at' | 'customFields'> & { 
  areasOfInterestIds: string[]; 
  birthDateInput: string;
  subscribed_at?: string;
  unsubscribed_at?: string;
  customFields?: any;
} = {
  email: '',
  firstName: '',
  lastName: '',
  entityName: '', 
  phone: '',
  birthDateInput: '', // For dd/mm or dd/mm/yy input
  birthDate: undefined, // For MM-DD or YYYY-MM-DD storage
  status: SubscriberProfileStatus.PENDING_CONFIRMATION,
  areasOfInterestIds: [],
  allowWhatsApp: true,
  allowSms: true,
  is_admin_only: false,
  owner_user_id: undefined,
  remarks: '',
};


const AddEditSubscriberPage: React.FC = () => {
  const navigate = useNavigate();
  const { subscriberId: routeSubscriberId } = useParams<{ subscriberId?: string }>(); 
  const { currentUser } = useAuth();
  const [isEditMode, setIsEditMode] = useState(false);
  const [formData, setFormData] = useState(initialFormData);
  const [isFormDisabled, setIsFormDisabled] = useState(false);
  const [feedbackMessage, setFeedbackMessage] = useState<{ type: 'success' | 'error'; message: string } | null>(null);
  const [originalEmail, setOriginalEmail] = useState<string>('');
  const [availableAreasOfInterest, setAvailableAreasOfInterest] = useState<AreaOfInterest[]>([]);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    const loadData = async () => {
      try {
        // Load areas of interest from API
        const allAreas = await areaOfInterestService.getAllAreasOfInterest();
        setAvailableAreasOfInterest(allAreas);

        if (routeSubscriberId) {
          setIsEditMode(true);
          
          // Load specific subscriber from API
          const profileToEdit = await subscriberService.getSubscriberById(routeSubscriberId);
          
          if (profileToEdit) {
            if (!canUserEditDeleteItem(profileToEdit as AccessibleItem, currentUser)) {
              setFeedbackMessage({ type: 'error', message: "Access Denied: You do not have permission to edit this subscriber." });
              setIsFormDisabled(true);
            } else {
              setIsFormDisabled(false);
            }
            
            const { id, subscribed_at, created_at, updated_at, unsubscribed_at, customFields, birthDate, ...editableData } = profileToEdit;
            
            let birthDateInputDisplay = '';
            if (birthDate) {
            if (/^\d{4}-\d{2}-\d{2}$/.test(birthDate)) { // YYYY-MM-DD
                const [year, month, day] = birthDate.split('-');
                birthDateInputDisplay = `${day}/${month}/${year.substring(2)}`; // dd/mm/yy
            } else if (/^\d{2}-\d{2}$/.test(birthDate)) { // MM-DD
                const [month, day] = birthDate.split('-');
                birthDateInputDisplay = `${day}/${month}`; // dd/mm
            }
            }

            setFormData({
              ...initialFormData,
              ...editableData,
              birthDateInput: birthDateInputDisplay,
              birthDate: birthDate, // Store the original internal format
              entityName: editableData.entityName || '',
              allowWhatsApp: typeof editableData.allowWhatsApp === 'boolean' ? editableData.allowWhatsApp : true,
              allowSms: typeof editableData.allowSms === 'boolean' ? editableData.allowSms : true,
              areasOfInterestIds: profileToEdit.areasOfInterestIds || [],
              is_admin_only: !!editableData.is_admin_only,
              owner_user_id: editableData.owner_user_id || undefined,
              remarks: editableData.remarks || '',
            });
            setOriginalEmail(profileToEdit.email);
          } else {
            setFeedbackMessage({ type: 'error', message: 'Subscriber profile not found.' });
            navigate('/subscribers');
          }
        } else { // Add mode
          setIsEditMode(false);
          setIsFormDisabled(currentUser?.role === UserRole.VIEWER);
          if (currentUser?.role === UserRole.VIEWER) {
            setFeedbackMessage({ type: 'error', message: 'Access Denied: Viewers cannot create subscribers.' });
          }
          setFormData(initialFormData);
          setOriginalEmail('');
        }
      } catch (error) {
        console.error('Error loading subscriber data:', error);
        setFeedbackMessage({ 
          type: 'error', 
          message: `Failed to load subscriber data: ${error instanceof Error ? error.message : 'Unknown error'}. Please check your connection and try again.`
        });
      }
    };

    loadData();
  }, [routeSubscriberId, currentUser, navigate]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    if (isFormDisabled) return;
    const { name, value, type } = e.target;
    if (type === 'checkbox') {
        const checked = (e.target as HTMLInputElement).checked;
        if (name === "is_admin_only") {
            setFormData(prev => ({
                ...prev,
                is_admin_only: checked,
                owner_user_id: !checked ? undefined : prev.owner_user_id,
            }));
        } else if (name === "owner_user_id_toggle") {
            setFormData(prev => ({
                ...prev,
                owner_user_id: checked && currentUser ? currentUser.user_id : undefined,
            }));
        } else {
            setFormData(prev => ({ ...prev, [name]: checked }));
        }
    } else {
      if (name === 'birthDateInput') {
        setFormData(prev => ({ ...prev, birthDateInput: value, birthDate: undefined })); // Clear internal birthDate on input change
      } else {
        setFormData(prev => ({ ...prev, [name]: value, }));
      }
    }
  };

  // Areas of Interest functions removed - now handled by MultiSelectDropdown component


  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (isFormDisabled) {
      setFeedbackMessage({type: 'error', message: 'Form is disabled due to insufficient permissions.'});
      return;
    }
    setFeedbackMessage(null);

    try {

    if (!formData.email.trim()) {
        setFeedbackMessage({ type: 'error', message: 'Email is required.'});
        return;
    }
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email.trim())) {
      setFeedbackMessage({ type: 'error', message: 'Invalid email format.' });
      return;
    }
    
    let birthDateForStorage: string | undefined = undefined;
    if (formData.birthDateInput && formData.birthDateInput.trim()) {
        if (!isValidIndianBirthDateInput(formData.birthDateInput.trim())) {
            setFeedbackMessage({ type: 'error', message: 'Invalid Birth Date format. Use dd/mm or dd/mm/yy.' });
            return;
        }
        const parts = formData.birthDateInput.trim().split('/');
        if (parts.length === 2) { // dd/mm
            birthDateForStorage = `${parts[1].padStart(2, '0')}-${parts[0].padStart(2, '0')}`; // MM-DD
        } else if (parts.length === 3) { // dd/mm/yy or dd/mm/yyyy
            const parsedDate = parseIndianDateStringToDate(formData.birthDateInput.trim());
            if (parsedDate) {
                birthDateForStorage = formatDateToYyyyMmDd(parsedDate); // YYYY-MM-DD
            } else {
                 setFeedbackMessage({ type: 'error', message: 'Invalid Birth Date.' }); return;
            }
        }
    } else {
      birthDateForStorage = undefined; // Ensure it's undefined if input is empty
    }

    // Check for email conflicts using API
    const allSubscribers = await subscriberService.getAllSubscribers();
    
    if ((!isEditMode || (isEditMode && formData.email !== originalEmail)) && 
        allSubscribers.some(profile => profile.email.toLowerCase() === formData.email.trim().toLowerCase())) {
        setFeedbackMessage({ type: 'error', message: `Email "${formData.email.trim()}" already exists.`});
        return;
    }
    
    let updatedSubscribersList: Subscriber[];
    let savedSubscriber: Subscriber;

    let finalIsAdminOnly = formData.is_admin_only;
    let finalOwnerUserId = formData.owner_user_id;

    if (currentUser?.role !== UserRole.ADMIN) {
        finalIsAdminOnly = false;
        finalOwnerUserId = undefined;
    } else if (finalOwnerUserId) {
        finalIsAdminOnly = true;
    }

    // Exclude birthDateInput from data to be saved
    const { birthDateInput, ...dataToSave } = formData;

    const now = new Date().toISOString();
    const subscriberDataToSave = {
        ...dataToSave,
        birthDate: birthDateForStorage,
        is_admin_only: finalIsAdminOnly,
        owner_user_id: finalOwnerUserId,
        subscribed_at: isEditMode ? (dataToSave.subscribed_at || now) : now
    };

    // Save subscriber using API service
    if (isEditMode && routeSubscriberId) {
      savedSubscriber = await subscriberService.updateSubscriber(routeSubscriberId, subscriberDataToSave);
      setFeedbackMessage({ type: 'success', message: 'Subscriber profile updated successfully.' });
      addAuditLog(AuditActionType.UPDATE, 'Subscriber', { entityId: savedSubscriber.id, entityName: savedSubscriber.email, userId: currentUser?.user_id });
    } else {
      savedSubscriber = await subscriberService.createSubscriber(subscriberDataToSave);
      setFeedbackMessage({ type: 'success', message: 'Subscriber profile created successfully.' });
      addAuditLog(AuditActionType.CREATE, 'Subscriber', { entityId: savedSubscriber.id, entityName: savedSubscriber.email, userId: currentUser?.user_id });
    }
    
    setTimeout(() => {
      navigate('/subscribers');
    }, 1500);
    
    } catch (error) {
      console.error('Error saving subscriber:', error);
      setFeedbackMessage({ type: 'error', message: 'Failed to save subscriber. Please try again.' });
    }
  };

  const handleDelete = async () => {
    if (isFormDisabled || !routeSubscriberId || !isEditMode) return;

    try {
      const subscriberToDelete = await subscriberService.getSubscriberById(routeSubscriberId);
      
      if (!subscriberToDelete) {
        setFeedbackMessage({ type: 'error', message: 'Cannot delete: Subscriber not found.'});
        return;
      }
      
      if (!canUserEditDeleteItem(subscriberToDelete as AccessibleItem, currentUser)) {
        setFeedbackMessage({ type: 'error', message: "Access Denied: You do not have permission to delete this subscriber."});
        return;
      }
      
      const profileIdentifier = formData.email || originalEmail; 
      if (window.confirm(`Are you sure you want to delete subscriber: "${profileIdentifier}"?`)) {
        await subscriberService.deleteSubscriber(routeSubscriberId);
        addAuditLog(AuditActionType.DELETE, 'Subscriber', { entityId: routeSubscriberId, entityName: subscriberToDelete?.email, userId: currentUser?.user_id });
        setFeedbackMessage({ type: 'success', message: `Subscriber "${profileIdentifier}" deleted successfully.` });
        setTimeout(() => {
          navigate('/subscribers');
        }, 1500);
      }
    } catch (error) {
      console.error('Error deleting subscriber:', error);
      setFeedbackMessage({ type: 'error', message: 'Failed to delete subscriber. Please try again.' });
    }
  };

  const handleCancel = () => {
    navigate('/subscribers');
  };

  // Floating action bar handlers
  const handleFloatingSave = async () => {
    setIsSaving(true);
    try {
      // Trigger form submission
      const form = document.querySelector('form');
      if (form) {
        const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
        form.dispatchEvent(submitEvent);
      }
    } finally {
      // Reset saving state after a delay to show feedback
      setTimeout(() => setIsSaving(false), 1000);
    }
  };

  const handleFloatingCancel = () => {
    handleCancel();
  };

  const handleFloatingDelete = () => {
    handleDelete();
  };

  const inputClass = "mt-1 block w-full px-3 py-2 bg-surface text-textPrimary border-border rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm disabled:bg-gray-100 disabled:cursor-not-allowed";
  const labelClass = "block text-sm font-medium text-textPrimary";
  const sectionClass = "p-6 bg-surface shadow-md rounded-lg mb-6 border border-border";
  const fieldGroupClass = "grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6";
  const sectionHeaderClass = "text-lg font-medium leading-6 bg-primary text-white px-4 py-3 -mx-6 -mt-6 mb-6 rounded-t-lg shadow-sm flex items-center";
  const checkboxItemClass = "flex items-center";
  const checkboxInputClass = "h-4 w-4 text-primary border-border rounded focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed";
  const checkboxLabelClass = "ml-2 block text-sm text-textPrimary";


  return (
    <div>
      <Header 
        title={isEditMode ? `Edit Subscriber` : "Add New Subscriber"} 
        subtitle={isEditMode ? `Updating profile for: ${originalEmail}` : "Fill in the details for the new subscriber."}
      />

      {feedbackMessage && (
        <ToastNotification
          message={feedbackMessage.message}
          type={feedbackMessage.type}
          onClose={() => setFeedbackMessage(null)}
          duration={8000}
        />
      )}

      <form onSubmit={handleSubmit} className="space-y-8">
        <fieldset disabled={isFormDisabled}>
        <div className={sectionClass}>
          <h3 className={sectionHeaderClass}>Subscriber Information</h3>
          <div className={fieldGroupClass}>
            <div className="sm:col-span-3">
              <label htmlFor="firstName" className={labelClass}>First Name</label>
              <input type="text" name="firstName" id="firstName" value={formData.firstName || ''} onChange={handleChange} className={inputClass} />
            </div>
            <div className="sm:col-span-3">
              <label htmlFor="lastName" className={labelClass}>Last Name</label>
              <input type="text" name="lastName" id="lastName" value={formData.lastName || ''} onChange={handleChange} className={inputClass} />
            </div>
            <div className="sm:col-span-3">
              <label htmlFor="entityName" className={labelClass}>Entity Name</label>
              <input type="text" name="entityName" id="entityName" value={formData.entityName || ''} onChange={handleChange} className={inputClass} />
            </div>
             <div className="sm:col-span-3">
              <label htmlFor="phone" className={labelClass}>Phone Number</label>
              <input type="tel" name="phone" id="phone" value={formData.phone || ''} onChange={handleChange} className={inputClass} />
            </div>
            <div className="sm:col-span-3">
              <label htmlFor="email" className={labelClass}>Email Address *</label>
              <input type="email" name="email" id="email" value={formData.email} onChange={handleChange} className={inputClass} required aria-required="true" />
            </div>
            <div className="sm:col-span-3">
              <label htmlFor="status" className={labelClass}>Profile Status *</label>
              <select name="status" id="status" value={formData.status} onChange={handleChange} className={inputClass} required aria-required="true">
                {Object.values(SubscriberProfileStatus).map(statusValue => (
                  <option key={statusValue} value={statusValue}>{formatEnumValueForDisplay(statusValue)}</option>
                ))}
              </select>
            </div>
            <div className="sm:col-span-3">
              <label htmlFor="birthDateInput" className={labelClass}>Birth Date</label>
              <input
                type="text"
                name="birthDateInput"
                id="birthDateInput"
                value={formData.birthDateInput || ''}
                onChange={handleChange}
                className={inputClass}
                placeholder="dd/mm or dd/mm/yy"
                aria-describedby="birthDateHelp"
              />
              <p id="birthDateHelp" className="mt-1 text-xs text-textSecondary">Enter day and month (e.g., 15/08) or full date (e.g., 15/08/90).</p>
            </div>
            <div className="sm:col-span-6">
              <label htmlFor="remarks" className={labelClass}>Remarks (Optional)</label>
              <textarea
                name="remarks"
                id="remarks"
                rows={3}
                value={formData.remarks || ''}
                onChange={(e) => {
                  if (isFormDisabled) return;
                  setFormData(prev => ({ ...prev, remarks: e.target.value }));
                }}
                className={inputClass}
                placeholder="Add any notes or remarks about this subscriber for future reference..."
                disabled={isFormDisabled}
              />
              <p className="mt-1 text-xs text-textSecondary">
                Use this field to keep notes about the subscriber for future reference, such as special instructions, context, or reminders.
              </p>
            </div>
          </div>
        </div>

        <div className={sectionClass}>
          <h3 className={sectionHeaderClass}>Communication Preferences</h3>
          <div className="space-y-4 sm:flex sm:items-center sm:space-y-0 sm:space-x-10">
            <div className="flex items-center">
                <input
                    id="allowWhatsApp"
                    name="allowWhatsApp"
                    type="checkbox"
                    checked={formData.allowWhatsApp}
                    onChange={handleChange}
                    className="h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary"
                />
                <label htmlFor="allowWhatsApp" className="ml-2 block text-sm text-gray-700">Allow WhatsApp Communication</label>
            </div>
            <div className="flex items-center">
                <input
                    id="allowSms"
                    name="allowSms"
                    type="checkbox"
                    checked={formData.allowSms}
                    onChange={handleChange}
                    className="h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary"
                />
                <label htmlFor="allowSms" className="ml-2 block text-sm text-gray-700">Allow SMS Communication</label>
            </div>
          </div>
        </div>


        <div className={sectionClass}>
          <h3 className={sectionHeaderClass}>Areas of Interest</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            {[...availableAreasOfInterest]
              .sort((a, b) => a.name.localeCompare(b.name))
              .map((aoi: AreaOfInterest) => (
                <div key={aoi.id} className="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded">
                  <input
                    id={`aoi-${aoi.id}`}
                    name="areasOfInterestIds"
                    type="checkbox"
                    value={aoi.id}
                    checked={formData.areasOfInterestIds.includes(aoi.id)}
                    onChange={() => {
                      if (isFormDisabled) return;
                      setFormData(prev => {
                        const newAoiIds = prev.areasOfInterestIds.includes(aoi.id)
                          ? prev.areasOfInterestIds.filter(id => id !== aoi.id)
                          : [...prev.areasOfInterestIds, aoi.id];
                        return { ...prev, areasOfInterestIds: newAoiIds };
                      });
                    }}
                    className="h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary"
                    disabled={isFormDisabled}
                  />
                  <label htmlFor={`aoi-${aoi.id}`} className="text-sm text-gray-700">
                    {aoi.name}
                    {aoi.description && (
                      <span className="block text-xs text-gray-500 mt-1">{aoi.description}</span>
                    )}
                  </label>
                </div>
              ))}
          </div>
          {availableAreasOfInterest.length === 0 && (
            <p className="text-sm text-gray-500 mt-2">
              No areas of interest defined. Manage them under "Areas of Interest" section.
            </p>
          )}
        </div>

        {currentUser?.role === UserRole.ADMIN && (
            <div className={sectionClass}>
                <h3 className={sectionHeaderClass}><LockClosedIcon className="h-5 w-5 mr-2"/>Access Control</h3>
                <div className="space-y-4">
                    <div className={checkboxItemClass}>
                        <input 
                            id="is_admin_only" 
                            name="is_admin_only" 
                            type="checkbox" 
                            checked={formData.is_admin_only || !!formData.owner_user_id} 
                            onChange={handleChange} 
                            className={checkboxInputClass} 
                            disabled={isFormDisabled || !!formData.owner_user_id} 
                        />
                        <label htmlFor="is_admin_only" className={checkboxLabelClass}>Admin Only Access</label>
                    </div>
                    
                    <div className={checkboxItemClass}>
                        <input 
                            id="owner_user_id_toggle" 
                            name="owner_user_id_toggle" 
                            type="checkbox" 
                            checked={!!formData.owner_user_id} 
                            onChange={handleChange} 
                            className={checkboxInputClass} 
                            disabled={isFormDisabled} 
                        />
                        <label htmlFor="owner_user_id_toggle" className={checkboxLabelClass}>For Me Only (Private to You)</label>
                    </div>
                </div>
                 <p className="mt-2 text-xs text-textSecondary">
                    If "For Me Only" is checked, this item will only be visible and editable by you. It automatically implies "Admin Only".<br/>
                    If only "Admin Only" is checked, any admin can view/edit, but non-admins cannot.
                </p>
            </div>
        )}
        
        {/* Form buttons removed - now using floating action bar */}
        <div className="pb-20">
          {/* Add padding bottom to account for floating action bar */}
        </div>
        </fieldset>
      </form>

      {/* Floating Action Bar */}
      <FloatingActionBar
        isEditMode={isEditMode}
        isDisabled={isFormDisabled}
        isSaving={isSaving}
        saveText={isEditMode ? "Save Changes" : "Create Subscriber"}
        onSave={handleFloatingSave}
        onCancel={handleFloatingCancel}
        onDelete={handleFloatingDelete}
        showDelete={isEditMode && canUserEditDeleteItem(formData as unknown as AccessibleItem, currentUser)}
      />
    </div>
  );
};

export default AddEditSubscriberPage;
