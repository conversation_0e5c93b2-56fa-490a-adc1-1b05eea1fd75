# Quick Setup Steps for Automate SMS Integration

## 🚀 **5-Minute Setup Guide**

### **Step 1: Install Automate App (2 minutes)**
1. **Open Google Play Store** on your Android device
2. **Search "Automate"** by LlamaLab
3. **Install the free app**
4. **Open and grant permissions**:
   - ✅ SMS permission
   - ✅ Phone permission  
   - ✅ Storage permission
   - ✅ Network permission

### **Step 2: Find Your PC's IP Address (1 minute)**
**On your Windows PC:**
1. **Press Windows + R**
2. **Type `cmd` and press Enter**
3. **Type `ipconfig` and press Enter**
4. **Look for "IPv4 Address"** (example: `*************`)
5. **Write down this IP address**

### **Step 3: Create the Flow (2 minutes)**

#### **Option A: Import Ready-Made Flow (Recommended)**
1. **Download the flow file** `CRM-SMS-Gateway.flo` to your Android device
2. **Open Automate app**
3. **Tap the "+" button**
4. **Select "Import"**
5. **Choose the downloaded .flo file**
6. **Edit the IP address** in the flow to match your PC's IP

#### **Option B: Manual Creation**
1. **Open Automate app**
2. **Tap "+" → "Create new flow"**
3. **Name it "CRM SMS Gateway"**
4. **Follow the detailed guide** in `Automate-Setup-Guide.md`

### **Step 4: Configure Network Settings (30 seconds)**
1. **Edit the flow** in Automate
2. **Find the HTTP request blocks**
3. **Replace `*************`** with your PC's actual IP address
4. **Save the flow**

### **Step 5: Test and Start (30 seconds)**
1. **Tap the "Play" button** to start the flow
2. **Create a test SMS campaign** in your CRM
3. **Check if SMS is sent** from your Android device
4. **Enable "Run in background"** in flow settings

## 📱 **What the Flow Does**

```
Every 5 seconds:
1. Check CRM server for pending SMS messages
2. For each message found:
   - Send SMS via Android device
   - Report success/failure back to CRM
3. Wait 5 seconds and repeat
```

## ⚙️ **Configuration Details**

### **URLs to Configure**
Replace `*************` with your PC's IP address:

- **Check for messages**: `http://[YOUR_IP]:3001/api/sms/pending`
- **Report status**: `http://[YOUR_IP]:3001/api/sms/status`

### **Flow Settings**
- **Poll interval**: 5 seconds (adjustable)
- **Background operation**: Enabled
- **Auto-start**: Enabled
- **Error handling**: Built-in retry logic

## 🔧 **Troubleshooting**

### **Common Issues & Solutions**

**❌ "Network error" messages**
- ✅ Check if both devices are on same WiFi
- ✅ Verify PC's IP address hasn't changed
- ✅ Ensure CRM server is running (port 3001)

**❌ SMS not sending**
- ✅ Grant SMS permission to Automate
- ✅ Check SIM card is active
- ✅ Verify phone number format (+1234567890)

**❌ Flow stops running**
- ✅ Disable battery optimization for Automate
- ✅ Keep Automate app in recent apps
- ✅ Enable "Run in background" in flow settings

**❌ Can't find PC's IP**
- ✅ Try `ipconfig` command on PC
- ✅ Check router admin panel
- ✅ Use network scanner app on Android

## 📊 **Monitoring**

### **Check if Working**
1. **Open your web SMS gateway** (`sms-web-gateway/index.html`)
2. **Click "Load Statistics"**
3. **Create test SMS campaign** in CRM
4. **Watch pending messages** decrease as they're sent
5. **Check "Recent Activity"** for sent messages

### **Android Monitoring**
1. **Open Automate app**
2. **Check flow status** (should show "Running")
3. **View logs** for any errors
4. **Check notifications** for SMS activity

## 🎯 **Expected Results**

### **When Working Correctly**
- ✅ **CRM campaigns** automatically send SMS via Android
- ✅ **Real-time delivery** within 5-10 seconds
- ✅ **Status updates** in CRM (sent/failed)
- ✅ **Background operation** continues even when phone is locked
- ✅ **Error handling** retries failed messages

### **Performance**
- **Polling frequency**: Every 5 seconds
- **SMS delivery**: Near real-time
- **Battery usage**: Minimal (optimized polling)
- **Network usage**: Very low (small JSON requests)

## 🔄 **Integration Workflow**

```
1. CRM creates SMS campaign
   ↓
2. Messages queued in database
   ↓
3. Android polls CRM every 5 seconds
   ↓
4. Automate sends SMS via Android
   ↓
5. Status reported back to CRM
   ↓
6. CRM updates campaign statistics
```

## 📋 **Files Provided**

- **`Automate-Setup-Guide.md`** - Detailed setup instructions
- **`CRM-SMS-Gateway.flo`** - Ready-to-import Automate flow
- **`Quick-Setup-Steps.md`** - This quick guide

## 🎉 **Success Indicators**

You'll know it's working when:
- ✅ **Automate flow shows "Running" status**
- ✅ **SMS messages are sent from Android device**
- ✅ **CRM campaign status updates to "sent"**
- ✅ **Web gateway shows decreasing pending messages**
- ✅ **Statistics show sent message counts**

## 🆘 **Need Help?**

1. **Check the detailed guide**: `Automate-Setup-Guide.md`
2. **Test individual components**:
   - Can Android reach PC? (ping test)
   - Is CRM server running? (browser test)
   - Does SMS work manually? (send test SMS)
3. **Review Automate logs** for specific error messages
4. **Verify all permissions** are granted to Automate

**🚀 With this setup, your CRM will automatically send SMS messages through your Android device!**
