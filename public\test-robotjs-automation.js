/**
 * Simple robotjs automation test for WhatsApp
 * This file can be loaded in the browser to test basic automation
 */

// Test function that can be called from browser console
window.testWhatsAppAutomation = async function() {
  console.log('🤖 Starting WhatsApp robotjs automation test...');
  
  try {
    // Test 1: Check if automation API is available
    console.log('📡 Testing automation API...');
    const systemCheck = await fetch('/api/whatsapp-automation/system-check');
    const systemResult = await systemCheck.json();
    
    console.log('🔍 System check result:', systemResult);
    
    if (!systemResult.robotjsAvailable) {
      console.error('❌ robotjs is not available');
      return false;
    }
    
    // Test 2: Test basic automation functionality
    console.log('🧪 Testing automation functionality...');
    const testResponse = await fetch('/api/whatsapp-automation/test', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    const testResult = await testResponse.json();
    console.log('🧪 Test result:', testResult);
    
    if (testResult.success) {
      console.log('✅ robotjs automation is working!');
      
      // Test 3: Check preferences
      console.log('⚙️ Checking automation preferences...');
      const prefsResponse = await fetch('/api/whatsapp-automation/preferences');
      const preferences = await prefsResponse.json();
      console.log('⚙️ Current preferences:', preferences);
      
      // Test 4: Enable automation if not enabled
      if (!preferences.enableRobotjsAutomation) {
        console.log('🔧 Enabling robotjs automation...');
        const updateResponse = await fetch('/api/whatsapp-automation/preferences', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            ...preferences,
            enableRobotjsAutomation: true
          })
        });
        
        const updateResult = await updateResponse.json();
        console.log('🔧 Preferences updated:', updateResult);
      }
      
      console.log('🎉 WhatsApp robotjs automation test completed successfully!');
      console.log('📋 Next steps:');
      console.log('   1. Open WhatsApp Desktop and log in');
      console.log('   2. Go to Settings → WhatsApp Configuration');
      console.log('   3. Enable "nut.js WhatsApp Desktop Automation"');
      console.log('   4. Click "Test Automation" button');
      console.log('   5. Create a test campaign and try automation');
      
      return true;
    } else {
      console.error('❌ Automation test failed:', testResult.message);
      return false;
    }
    
  } catch (error) {
    console.error('❌ Automation test error:', error);
    return false;
  }
};

// Test function for basic robotjs functionality (backend only)
window.testRobotjsBasic = async function() {
  console.log('🤖 Testing basic robotjs functionality...');
  
  try {
    const response = await fetch('/api/whatsapp-automation/test', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        testType: 'basic'
      })
    });
    
    const result = await response.json();
    console.log('🧪 Basic robotjs test result:', result);
    
    if (result.success) {
      console.log('✅ robotjs is working correctly!');
      console.log('📋 You can now use WhatsApp automation features');
    } else {
      console.error('❌ robotjs test failed:', result.message);
      console.log('🔧 Troubleshooting:');
      console.log('   - Make sure robotjs is properly installed');
      console.log('   - Check if you have necessary system permissions');
      console.log('   - Try restarting the application');
    }
    
    return result.success;
  } catch (error) {
    console.error('❌ Error testing robotjs:', error);
    return false;
  }
};

// Auto-run basic test when page loads
document.addEventListener('DOMContentLoaded', function() {
  console.log('🚀 WhatsApp robotjs automation test script loaded');
  console.log('📋 Available test functions:');
  console.log('   - testWhatsAppAutomation() - Full automation test');
  console.log('   - testRobotjsBasic() - Basic robotjs test');
  console.log('');
  console.log('💡 Run testWhatsAppAutomation() in console to test the automation system');
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testWhatsAppAutomation: window.testWhatsAppAutomation,
    testRobotjsBasic: window.testRobotjsBasic
  };
}
