<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM SMS Gateway</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .status {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-connected {
            background: #4CAF50;
        }
        
        .status-disconnected {
            background: #f44336;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 25px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            background: #fafafa;
        }
        
        .section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.4em;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }
        
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus, .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: transform 0.2s;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #f44336 0%, #da190b 100%);
        }
        
        .message-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
        }
        
        .message-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .message-item:last-child {
            border-bottom: none;
        }
        
        .message-info {
            flex: 1;
        }
        
        .message-recipient {
            font-weight: 600;
            color: #333;
        }
        
        .message-text {
            color: #666;
            margin: 5px 0;
            font-size: 14px;
        }
        
        .message-time {
            color: #999;
            font-size: 12px;
        }
        
        .message-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-sent {
            background: #d4edda;
            color: #155724;
        }
        
        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }
        
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: #667eea;
        }
        
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid #e0e0e0;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        
        .alert {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            font-weight: 500;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .content {
                padding: 20px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📱 CRM SMS Gateway</h1>
            <p>Web-based SMS sending interface for your CRM system</p>
        </div>
        
        <div class="status">
            <span class="status-indicator" id="statusIndicator"></span>
            <span id="statusText">Checking connection...</span>
        </div>
        
        <div class="content">
            <!-- Configuration Section -->
            <div class="section">
                <h2>⚙️ Configuration</h2>
                <div class="form-group">
                    <label for="serverUrl">CRM Server URL:</label>
                    <input type="text" id="serverUrl" placeholder="http://localhost:3001" value="http://localhost:3001">
                </div>
                <div class="form-group">
                    <label for="apiKey">API Key (optional):</label>
                    <input type="password" id="apiKey" placeholder="Enter API key if required">
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="autoMode"> Enable Auto Mode (Check for pending messages every 5 seconds)
                    </label>
                </div>
                <button class="btn" onclick="saveConfig()">💾 Save Configuration</button>
                <button class="btn btn-success" onclick="testConnection()">🔗 Test Connection</button>
            </div>

            <!-- Traccar SMS Gateway Section -->
            <div class="section" id="traccarSection">
                <h2>📱 Traccar SMS Gateway</h2>
                <div class="alert alert-warning">
                    <strong>Professional SMS Solution:</strong> Traccar SMS Gateway is a reliable Android app that provides HTTP API for SMS sending. Install it from Google Play Store.
                </div>

                <!-- Connection Status -->
                <div id="traccarStatus" class="alert alert-warning" style="display: none;">
                    <strong>🔗 Connection Status:</strong> <span id="traccarStatusText">Not tested</span>
                </div>

                <div class="form-group">
                    <label for="traccarUrl">Android Device IP:</label>
                    <input type="text" id="traccarUrl" placeholder="*************:8080">
                </div>
                <div class="form-group">
                    <label for="traccarKey">API Key:</label>
                    <input type="password" id="traccarKey" placeholder="Enter API key from Traccar app">
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="traccarEnabled"> Enable Traccar SMS Gateway
                    </label>
                </div>
                <button class="btn" onclick="saveTraccarConfig()">💾 Save Traccar Config</button>
                <button class="btn btn-success" onclick="testTraccarConnection()">🔗 Test Traccar</button>
                <button class="btn btn-warning" onclick="testTraccarConnection()" id="retryTraccarBtn" style="display: none;">🔄 Retry Test</button>
                <button class="btn btn-primary" onclick="sendViaTraccar()" id="sendTraccarBtn" disabled>📱 Send All via Traccar</button>
                <button class="btn btn-success" onclick="generateSMSIntents()" id="generateSMSBtn">📲 Generate SMS Links</button>

                <div class="alert alert-info" style="margin-top: 15px;">
                    <strong>⚠️ Important:</strong> SMS will only be sent if Traccar connection is successful. Test the connection first!
                </div>
            </div>

            <!-- Manual SMS Section -->
            <div class="section">
                <h2>📤 Send Manual SMS</h2>
                <div class="alert alert-warning">
                    <strong>Note:</strong> This web interface can queue SMS messages, but actual sending requires an Android device with SMS capability or integration with an SMS service provider.
                </div>
                <div class="form-group">
                    <label for="recipient">Recipient Phone Number:</label>
                    <input type="tel" id="recipient" placeholder="+**********">
                </div>
                <div class="form-group">
                    <label for="message">Message:</label>
                    <textarea id="message" rows="4" placeholder="Enter your SMS message here..."></textarea>
                </div>
                <button class="btn btn-success" onclick="sendManualSMS()">📱 Queue SMS</button>
            </div>
            
            <!-- Pending Messages Section -->
            <div class="section">
                <h2>📋 Pending Messages</h2>
                <button class="btn" onclick="loadPendingMessages()">🔄 Refresh</button>
                <button class="btn btn-success" onclick="processAllPending()" id="processBtn" disabled>▶️ Process All</button>
                <button class="btn btn-primary" onclick="showTraccarConfig()">📱 Traccar Setup</button>
                <div class="message-list" id="pendingMessages">
                    <div style="padding: 20px; text-align: center; color: #666;">
                        Click "Refresh" to load pending messages
                    </div>
                </div>
            </div>
            
            <!-- Statistics Section -->
            <div class="section">
                <h2>📊 Statistics</h2>
                <button class="btn" onclick="loadStats()">📈 Load Statistics</button>
                <div class="stats" id="statsContainer">
                    <!-- Stats will be loaded here -->
                </div>
            </div>
            
            <!-- Message History Section -->
            <div class="section">
                <h2>📜 Recent Activity</h2>
                <div class="message-list" id="messageHistory">
                    <div style="padding: 20px; text-align: center; color: #666;">
                        No recent activity
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Configuration
        let config = {
            serverUrl: 'http://localhost:3001',
            apiKey: '',
            autoMode: false
        };
        
        let autoModeInterval = null;
        let messageHistory = [];
        
        // Load configuration from localStorage
        function loadConfig() {
            const saved = localStorage.getItem('sms_gateway_config');
            if (saved) {
                config = { ...config, ...JSON.parse(saved) };
                document.getElementById('serverUrl').value = config.serverUrl;
                document.getElementById('apiKey').value = config.apiKey;
                document.getElementById('autoMode').checked = config.autoMode;
                
                if (config.autoMode) {
                    startAutoMode();
                }
            }
        }
        
        // Save configuration to localStorage
        function saveConfig() {
            config.serverUrl = document.getElementById('serverUrl').value;
            config.apiKey = document.getElementById('apiKey').value;
            config.autoMode = document.getElementById('autoMode').checked;
            
            localStorage.setItem('sms_gateway_config', JSON.stringify(config));
            
            showAlert('Configuration saved successfully!', 'success');
            
            if (config.autoMode) {
                startAutoMode();
            } else {
                stopAutoMode();
            }
        }
        
        // Test connection to CRM server
        async function testConnection() {
            try {
                updateStatus('Connecting...', false);
                
                const response = await fetch(`${config.serverUrl}/api/sms/status`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        ...(config.apiKey && { 'Authorization': `Bearer ${config.apiKey}` })
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    updateStatus(`Connected to ${data.server || 'CRM Server'}`, true);
                    showAlert('Connection successful!', 'success');
                } else {
                    updateStatus('Connection failed', false);
                    showAlert('Connection failed. Check server URL and ensure CRM is running.', 'error');
                }
            } catch (error) {
                updateStatus('Connection error', false);
                showAlert(`Connection error: ${error.message}`, 'error');
            }
        }
        
        // Update connection status
        function updateStatus(text, connected) {
            document.getElementById('statusText').textContent = text;
            const indicator = document.getElementById('statusIndicator');
            indicator.className = `status-indicator ${connected ? 'status-connected' : 'status-disconnected'}`;
        }
        
        // Show alert message
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            
            const content = document.querySelector('.content');
            content.insertBefore(alertDiv, content.firstChild);
            
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }
        
        // Send manual SMS
        async function sendManualSMS() {
            const recipient = document.getElementById('recipient').value;
            const message = document.getElementById('message').value;
            
            if (!recipient || !message) {
                showAlert('Please enter both recipient and message', 'error');
                return;
            }
            
            try {
                const response = await fetch(`${config.serverUrl}/api/sms/send`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...(config.apiKey && { 'Authorization': `Bearer ${config.apiKey}` })
                    },
                    body: JSON.stringify({
                        recipient,
                        message
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showAlert('SMS queued successfully!', 'success');
                    
                    // Add to history
                    addToHistory({
                        id: data.messageId,
                        recipient,
                        message,
                        status: 'pending',
                        timestamp: new Date().toISOString()
                    });
                    
                    // Clear form
                    document.getElementById('recipient').value = '';
                    document.getElementById('message').value = '';
                } else {
                    showAlert('Failed to queue SMS', 'error');
                }
            } catch (error) {
                showAlert(`Error: ${error.message}`, 'error');
            }
        }
        
        // Load pending messages
        async function loadPendingMessages() {
            try {
                const response = await fetch(`${config.serverUrl}/api/sms/pending`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        ...(config.apiKey && { 'Authorization': `Bearer ${config.apiKey}` })
                    }
                });
                
                if (response.ok) {
                    const messages = await response.json();
                    displayPendingMessages(messages);
                    
                    const processBtn = document.getElementById('processBtn');
                    processBtn.disabled = messages.length === 0;
                } else {
                    showAlert('Failed to load pending messages', 'error');
                }
            } catch (error) {
                showAlert(`Error loading messages: ${error.message}`, 'error');
            }
        }
        
        // Display pending messages
        function displayPendingMessages(messages) {
            const container = document.getElementById('pendingMessages');
            
            if (messages.length === 0) {
                container.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">No pending messages</div>';
                return;
            }
            
            container.innerHTML = messages.map(msg => `
                <div class="message-item">
                    <div class="message-info">
                        <div class="message-recipient">${msg.recipient}</div>
                        <div class="message-text">${msg.message.substring(0, 100)}${msg.message.length > 100 ? '...' : ''}</div>
                        <div class="message-time">Campaign: ${msg.campaignName || 'Manual'}</div>
                    </div>
                    <div class="message-status status-pending">Pending</div>
                </div>
            `).join('');
        }
        
        // Process all pending messages
        async function processAllPending() {
            console.log('🚀 Starting processAllPending...');

            // Check if Traccar is configured and enabled
            if (traccarConfig.enabled) {
                console.log('📱 Traccar is enabled, using Traccar to send messages');
                showAlert('Processing messages via Traccar...', 'info');
                await sendViaTraccar();
            } else {
                console.log('⚠️ Traccar not enabled, showing placeholder message');
                showAlert('Processing pending messages... (Note: This web interface can only queue messages. Actual SMS sending requires Android device or SMS service integration)', 'warning');

                // In a real implementation, this would trigger the Android app or SMS service
                // For now, we'll just mark them as "queued for processing"
                setTimeout(() => {
                    showAlert('All messages have been queued for processing', 'success');
                    loadPendingMessages();
                }, 2000);
            }

            console.log('✅ processAllPending complete');
        }
        
        // Load statistics
        async function loadStats() {
            try {
                const response = await fetch(`${config.serverUrl}/api/sms/stats`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        ...(config.apiKey && { 'Authorization': `Bearer ${config.apiKey}` })
                    }
                });
                
                if (response.ok) {
                    const stats = await response.json();
                    displayStats(stats);
                } else {
                    showAlert('Failed to load statistics', 'error');
                }
            } catch (error) {
                showAlert(`Error loading stats: ${error.message}`, 'error');
            }
        }
        
        // Display statistics
        function displayStats(stats) {
            const container = document.getElementById('statsContainer');
            const overall = stats.overall || {};
            const recent = stats.last24Hours || {};
            
            container.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${overall.total || 0}</div>
                    <div class="stat-label">Total Messages</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${overall.pending || 0}</div>
                    <div class="stat-label">Pending</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${overall.sent || 0}</div>
                    <div class="stat-label">Sent</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${overall.failed || 0}</div>
                    <div class="stat-label">Failed</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${recent.sent || 0}</div>
                    <div class="stat-label">Sent (24h)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${recent.failed || 0}</div>
                    <div class="stat-label">Failed (24h)</div>
                </div>
            `;
        }
        
        // Add message to history
        function addToHistory(message) {
            messageHistory.unshift(message);
            if (messageHistory.length > 50) {
                messageHistory = messageHistory.slice(0, 50);
            }
            displayHistory();
        }
        
        // Display message history
        function displayHistory() {
            const container = document.getElementById('messageHistory');
            
            if (messageHistory.length === 0) {
                container.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">No recent activity</div>';
                return;
            }
            
            container.innerHTML = messageHistory.map(msg => `
                <div class="message-item">
                    <div class="message-info">
                        <div class="message-recipient">${msg.recipient}</div>
                        <div class="message-text">${msg.message.substring(0, 100)}${msg.message.length > 100 ? '...' : ''}</div>
                        <div class="message-time">${new Date(msg.timestamp).toLocaleString()}</div>
                    </div>
                    <div class="message-status status-${msg.status}">${msg.status}</div>
                </div>
            `).join('');
        }
        
        // Start auto mode
        function startAutoMode() {
            if (autoModeInterval) return;
            
            autoModeInterval = setInterval(() => {
                loadPendingMessages();
            }, 5000);
            
            showAlert('Auto mode started - checking for pending messages every 5 seconds', 'success');
        }
        
        // Stop auto mode
        function stopAutoMode() {
            if (autoModeInterval) {
                clearInterval(autoModeInterval);
                autoModeInterval = null;
                showAlert('Auto mode stopped', 'warning');
            }
        }

        // Traccar SMS Gateway functions
        let traccarConfig = {
            url: '',
            apiKey: '',
            enabled: false
        };

        // Load Traccar configuration
        function loadTraccarConfig() {
            const saved = localStorage.getItem('traccar_config');
            if (saved) {
                traccarConfig = { ...traccarConfig, ...JSON.parse(saved) };
                document.getElementById('traccarUrl').value = traccarConfig.url;
                document.getElementById('traccarKey').value = traccarConfig.apiKey;
                document.getElementById('traccarEnabled').checked = traccarConfig.enabled;
            }
        }

        // Save Traccar configuration
        function saveTraccarConfig() {
            const url = document.getElementById('traccarUrl').value;
            const apiKey = document.getElementById('traccarKey').value;
            const enabled = document.getElementById('traccarEnabled').checked;

            if (!url || !apiKey) {
                showAlert('Please enter both URL and API key', 'error');
                return;
            }

            traccarConfig.url = url;
            traccarConfig.apiKey = apiKey;
            traccarConfig.enabled = enabled;

            localStorage.setItem('traccar_config', JSON.stringify(traccarConfig));
            showAlert('Configuration saved locally. Click "Test Traccar" to verify connection.', 'success');

            // Update status to show next step
            updateTraccarStatus('Configuration saved - Click "Test Traccar" to verify', false);
        }

        // Test Traccar connection
        async function testTraccarConnection() {
            // Get current values from form
            const url = document.getElementById('traccarUrl').value;
            const apiKey = document.getElementById('traccarKey').value;
            const enabled = document.getElementById('traccarEnabled').checked;

            if (!url || !apiKey) {
                showAlert('Please enter Traccar URL and API key first', 'error');
                updateTraccarStatus('Configuration incomplete - Enter Android device details above', false);
                return;
            }

            // Update config and save it first
            traccarConfig.url = url;
            traccarConfig.apiKey = apiKey;
            traccarConfig.enabled = enabled;

            updateTraccarStatus('Saving configuration...', false);

            // Save config to backend first
            try {
                const configResponse = await fetch(`${config.serverUrl}/api/sms/traccar/config`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...(config.apiKey && { 'Authorization': `Bearer ${config.apiKey}` })
                    },
                    body: JSON.stringify({
                        baseUrl: `http://${traccarConfig.url}`,
                        apiKey: traccarConfig.apiKey,
                        enabled: traccarConfig.enabled
                    })
                });

                if (!configResponse.ok) {
                    throw new Error('Failed to save configuration to server');
                }

                // Save to localStorage
                localStorage.setItem('traccar_config', JSON.stringify(traccarConfig));

                updateTraccarStatus('Configuration saved, testing connection...', false);

                // Wait a moment for server to process the configuration
                await new Promise(resolve => setTimeout(resolve, 500));

            } catch (configError) {
                showAlert(`Failed to save configuration: ${configError.message}`, 'error');
                updateTraccarStatus('Configuration save failed', false);
                return;
            }

            try {
                const response = await fetch(`${config.serverUrl}/api/sms/traccar/test`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        ...(config.apiKey && { 'Authorization': `Bearer ${config.apiKey}` })
                    }
                });

                if (response.ok) {
                    const result = await response.json();

                    if (result.success) {
                        showAlert('Traccar connection successful! SMS sending is now enabled.', 'success');
                        updateTraccarStatus('Connected and ready', true);
                        document.getElementById('sendTraccarBtn').disabled = false;
                    } else {
                        let errorMsg = result.error || 'Unknown error';
                        if (result.status === 'not_configured') {
                            // Retry once after a short delay
                            updateTraccarStatus('Configuration not yet applied, retrying...', false);
                            await new Promise(resolve => setTimeout(resolve, 1000));

                            // Retry the test
                            const retryResponse = await fetch(`${config.serverUrl}/api/sms/traccar/test`, {
                                method: 'GET',
                                headers: {
                                    'Content-Type': 'application/json',
                                    ...(config.apiKey && { 'Authorization': `Bearer ${config.apiKey}` })
                                }
                            });

                            if (retryResponse.ok) {
                                const retryResult = await retryResponse.json();
                                if (retryResult.success) {
                                    showAlert('Traccar connection successful! SMS sending is now enabled.', 'success');
                                    updateTraccarStatus('Connected and ready', true);
                                    document.getElementById('sendTraccarBtn').disabled = false;
                                    return;
                                } else {
                                    errorMsg = retryResult.error || 'Connection failed after retry';
                                }
                            } else {
                                errorMsg = 'Server error on retry';
                            }
                        }
                        showAlert(`Traccar connection failed: ${errorMsg}`, 'error');
                        updateTraccarStatus(`Connection failed: ${errorMsg}`, false);
                        document.getElementById('sendTraccarBtn').disabled = true;
                    }
                } else {
                    const errorText = await response.text();
                    showAlert(`Server error: ${response.status} - ${errorText}`, 'error');
                    updateTraccarStatus(`Server error: ${response.status}`, false);
                    document.getElementById('sendTraccarBtn').disabled = true;
                }
            } catch (error) {
                showAlert(`Error testing Traccar: ${error.message}`, 'error');
                updateTraccarStatus(`Error: ${error.message}`, false);
                document.getElementById('sendTraccarBtn').disabled = true;
            }
        }

        // Update Traccar connection status
        function updateTraccarStatus(text, connected) {
            const statusDiv = document.getElementById('traccarStatus');
            const statusText = document.getElementById('traccarStatusText');
            const retryBtn = document.getElementById('retryTraccarBtn');

            statusDiv.style.display = 'block';
            statusText.textContent = text;

            // Update alert class based on connection status
            statusDiv.className = `alert ${connected ? 'alert-success' : 'alert-warning'}`;

            // Show retry button if connection failed (but not for "not configured" or "testing" states)
            if (!connected && !text.includes('Testing') && !text.includes('Not configured') && !text.includes('Configuration')) {
                retryBtn.style.display = 'inline-block';
            } else {
                retryBtn.style.display = 'none';
            }
        }

        // Send all pending messages via Traccar
        async function sendViaTraccar() {
            console.log('🚀 Starting sendViaTraccar process...');
            console.log('📋 Traccar config:', traccarConfig);

            if (!traccarConfig.enabled) {
                console.log('❌ Traccar not enabled');
                showAlert('Please enable and configure Traccar first', 'error');
                return;
            }

            try {
                console.log('📥 Fetching pending messages...');
                // Get pending messages
                const response = await fetch(`${config.serverUrl}/api/sms/pending`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        ...(config.apiKey && { 'Authorization': `Bearer ${config.apiKey}` })
                    }
                });

                console.log(`📊 Pending messages response: ${response.status} ${response.statusText}`);

                if (!response.ok) {
                    console.log('❌ Failed to load pending messages');
                    showAlert('Failed to load pending messages', 'error');
                    return;
                }

                const messages = await response.json();
                console.log(`📋 Loaded ${messages.length} pending messages:`, messages);

                if (messages.length === 0) {
                    console.log('⚠️ No pending messages to send');
                    showAlert('No pending messages to send', 'warning');
                    return;
                }

                showAlert(`Sending ${messages.length} messages via Traccar...`, 'warning');

                const requestPayload = {
                    messages: messages.map(msg => ({
                        messageId: msg.id,
                        recipient: msg.recipient,
                        message: msg.message
                    }))
                };

                console.log('📤 Sending bulk SMS request...');
                console.log('🔗 URL:', `${config.serverUrl}/api/sms/traccar/send-bulk`);
                console.log('📦 Payload:', requestPayload);

                // Send bulk SMS via Traccar
                const bulkResponse = await fetch(`${config.serverUrl}/api/sms/traccar/send-bulk`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...(config.apiKey && { 'Authorization': `Bearer ${config.apiKey}` })
                    },
                    body: JSON.stringify(requestPayload)
                });

                console.log(`📊 Bulk SMS response: ${bulkResponse.status} ${bulkResponse.statusText}`);

                if (bulkResponse.ok) {
                    const result = await bulkResponse.json();
                    console.log('✅ Bulk SMS result:', result);

                    // Log detailed results
                    if (result.results && Array.isArray(result.results)) {
                        console.log('📊 Detailed results:');
                        result.results.forEach((res, index) => {
                            console.log(`  ${index + 1}. ${res.recipient}: ${res.success ? '✅ SUCCESS' : '❌ FAILED'}`,
                                       res.error || res.message || '');
                        });
                    }

                    const sent = result.summary?.sent || 0;
                    const failed = result.summary?.failed || 0;
                    const total = sent + failed;

                    console.log(`📈 Summary: ${sent}/${total} sent successfully, ${failed}/${total} failed`);

                    // Show appropriate message based on results
                    if (failed === 0) {
                        showAlert(`✅ All ${sent} messages sent successfully via Traccar!`, 'success');
                    } else if (sent === 0) {
                        showAlert(`❌ All ${total} messages failed to send via Traccar. Check Traccar connection.`, 'error');
                    } else {
                        showAlert(`⚠️ Partial success: ${sent} sent, ${failed} failed via Traccar`, 'warning');
                    }

                    loadPendingMessages(); // Refresh the list
                    loadStats(); // Refresh statistics
                } else {
                    const errorText = await bulkResponse.text();
                    console.log('❌ Bulk SMS failed:', errorText);
                    showAlert(`Failed to send messages via Traccar: ${bulkResponse.status} ${errorText}`, 'error');
                }

            } catch (error) {
                console.error('💥 Exception in sendViaTraccar:', error);
                showAlert(`Error sending via Traccar: ${error.message}`, 'error');
            }

            console.log('✅ sendViaTraccar process complete');
        }

        // Generate SMS Intent Links (Alternative to Traccar)
        async function generateSMSIntents() {
            console.log('🚀 Starting generateSMSIntents...');

            try {
                console.log('📥 Fetching pending messages...');
                const response = await fetch(`${config.serverUrl}/api/sms/pending`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        ...(config.apiKey && { 'Authorization': `Bearer ${config.apiKey}` })
                    }
                });

                if (!response.ok) {
                    console.log('❌ Failed to load pending messages');
                    showAlert('Failed to load pending messages', 'error');
                    return;
                }

                const messages = await response.json();
                console.log(`📋 Loaded ${messages.length} pending messages:`, messages);

                if (messages.length === 0) {
                    console.log('⚠️ No pending messages to process');
                    showAlert('No pending messages to process', 'warning');
                    return;
                }

                // Generate SMS intent URLs
                const smsIntents = messages.map(msg => {
                    const encodedMessage = encodeURIComponent(msg.message);
                    const encodedPhone = encodeURIComponent(msg.recipient);
                    return {
                        id: msg.id,
                        recipient: msg.recipient,
                        message: msg.message,
                        intentUrl: `sms:${encodedPhone}?body=${encodedMessage}`,
                        webUrl: `https://messages.google.com/web/conversations/new?recipient=${encodedPhone}&text=${encodedMessage}`
                    };
                });

                console.log('📲 Generated SMS intents:', smsIntents);

                // Display SMS intents in a modal
                showSMSIntentsModal(smsIntents);

            } catch (error) {
                console.error('💥 Exception in generateSMSIntents:', error);
                showAlert(`Error generating SMS intents: ${error.message}`, 'error');
            }

            console.log('✅ generateSMSIntents process complete');
        }

        // Show SMS Intents Modal
        function showSMSIntentsModal(smsIntents) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            `;

            const content = document.createElement('div');
            content.style.cssText = `
                background: white;
                padding: 20px;
                border-radius: 10px;
                max-width: 90%;
                max-height: 90%;
                overflow-y: auto;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            `;

            content.innerHTML = `
                <h2>📲 SMS Intent Links</h2>
                <p><strong>Instructions:</strong> Click the links below to open your SMS app with pre-filled messages.</p>
                <div style="margin: 20px 0;">
                    ${smsIntents.map((intent, index) => `
                        <div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px;">
                            <div><strong>📱 ${intent.recipient}</strong></div>
                            <div style="margin: 5px 0; color: #666; font-size: 14px;">
                                ${intent.message.substring(0, 100)}${intent.message.length > 100 ? '...' : ''}
                            </div>
                            <div style="margin: 10px 0;">
                                <a href="${intent.intentUrl}" style="display: inline-block; padding: 8px 15px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin-right: 10px;">
                                    📱 Open SMS App
                                </a>
                                <a href="${intent.webUrl}" target="_blank" style="display: inline-block; padding: 8px 15px; background: #28a745; color: white; text-decoration: none; border-radius: 5px;">
                                    🌐 Open Web SMS
                                </a>
                            </div>
                        </div>
                    `).join('')}
                </div>
                <div style="text-align: center; margin-top: 20px;">
                    <button onclick="this.closest('.modal').remove()" style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer;">
                        ✅ Close
                    </button>
                    <button onclick="downloadSMSData(${JSON.stringify(smsIntents).replace(/"/g, '&quot;')})" style="padding: 10px 20px; background: #17a2b8; color: white; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px;">
                        💾 Download SMS Data
                    </button>
                </div>
            `;

            modal.className = 'modal';
            modal.appendChild(content);
            document.body.appendChild(modal);

            // Close modal when clicking outside
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.remove();
                }
            });
        }

        // Download SMS data as JSON file
        function downloadSMSData(smsIntents) {
            const dataStr = JSON.stringify(smsIntents, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `sms-queue-${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            showAlert('SMS data downloaded successfully!', 'success');
        }

        // Show/hide Traccar configuration
        function showTraccarConfig() {
            const section = document.getElementById('traccarSection');
            if (section.style.display === 'none') {
                section.style.display = 'block';
                loadTraccarConfig();
            } else {
                section.style.display = 'none';
            }
        }
        
        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            loadConfig();
            loadTraccarConfig();
            testConnection();
            loadPendingMessages();
            loadStats();

            // Auto-test Traccar connection if configured
            setTimeout(() => {
                if (traccarConfig.url && traccarConfig.apiKey && traccarConfig.enabled) {
                    testTraccarConnection();
                } else {
                    updateTraccarStatus('Not configured - Enter Android device details above', false);
                }
            }, 1000);

            // Auto mode toggle handler
            document.getElementById('autoMode').addEventListener('change', function() {
                if (this.checked) {
                    startAutoMode();
                } else {
                    stopAutoMode();
                }
            });
        });
    </script>
</body>
</html>
