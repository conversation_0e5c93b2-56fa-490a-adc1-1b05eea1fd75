import React, { useState, useEffect } from 'react';
import { CheckCircleIcon, XCircleIcon, CogIcon } from '../components/icons';
import { whatsappService, WhatsAppConfig } from '../services/WhatsAppService';

const WhatsAppConfigurationPage: React.FC = () => {
  const [config, setConfig] = useState<WhatsAppConfig>({
    globalEnabled: true,
    apiEnabled: false,
    phoneNumberId: '',
    accessToken: '',
    webhookSecret: '',
    verifyToken: '',
    defaultCountryCode: '91',
    desktopEnabled: true,
    batchDelay: 3,
    maxBatchSize: 50,
    autoClose: false
  });

  const [testMessage, setTestMessage] = useState({
    phone: '',
    message: 'Hello! This is a test message from CRM4CA.'
  });

  const [status, setStatus] = useState<{
    type: 'idle' | 'testing' | 'success' | 'error';
    message: string;
  }>({ type: 'idle', message: '' });

  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    // Load existing configuration
    const savedConfig = whatsappService.getConfiguration();
    setConfig(savedConfig);
  }, []);

  const handleConfigChange = (field: keyof WhatsAppConfig, value: string | boolean | number) => {
    setConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      whatsappService.saveConfiguration(config);
      setStatus({
        type: 'success',
        message: '✅ WhatsApp configuration saved successfully!'
      });
      
      setTimeout(() => {
        setStatus({ type: 'idle', message: '' });
      }, 3000);
    } catch (error) {
      setStatus({
        type: 'error',
        message: `❌ Failed to save configuration: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleTestAPI = async () => {
    setStatus({ type: 'testing', message: 'Testing WhatsApp Business API connection...' });
    
    try {
      const result = await whatsappService.testAPI();
      
      if (result.success) {
        setStatus({
          type: 'success',
          message: `✅ ${result.message}`
        });
      } else {
        setStatus({
          type: 'error',
          message: `❌ API Test Failed: ${result.error}`
        });
      }
    } catch (error) {
      setStatus({
        type: 'error',
        message: `❌ API Test Error: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    }

    setTimeout(() => {
      setStatus({ type: 'idle', message: '' });
    }, 5000);
  };

  const handleTestDesktop = async () => {
    setStatus({ type: 'testing', message: 'Testing WhatsApp Desktop automation...' });
    
    try {
      const result = await whatsappService.testDesktop();
      
      if (result.success) {
        setStatus({
          type: 'success',
          message: `✅ ${result.message}`
        });
      } else {
        setStatus({
          type: 'error',
          message: `❌ Desktop Test Failed: ${result.error}`
        });
      }
    } catch (error) {
      setStatus({
        type: 'error',
        message: `❌ Desktop Test Error: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    }

    setTimeout(() => {
      setStatus({ type: 'idle', message: '' });
    }, 5000);
  };

  const handleSendTestMessage = async () => {
    if (!testMessage.phone || !testMessage.message) {
      setStatus({
        type: 'error',
        message: 'Please provide both phone number and message for testing.'
      });
      return;
    }

    setStatus({ type: 'testing', message: 'Sending test message...' });
    
    try {
      const result = await whatsappService.sendMessage({
        to: testMessage.phone,
        message: testMessage.message
      });
      
      if (result.success) {
        setStatus({
          type: 'success',
          message: `✅ Test message sent successfully via ${result.method.toUpperCase()}! ${result.messageId ? `Message ID: ${result.messageId}` : ''}`
        });
      } else {
        setStatus({
          type: 'error',
          message: `❌ Failed to send test message: ${result.error}`
        });
      }
    } catch (error) {
      setStatus({
        type: 'error',
        message: `❌ Test Message Error: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    }

    setTimeout(() => {
      setStatus({ type: 'idle', message: '' });
    }, 8000);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <CogIcon className="h-8 w-8 text-primary" />
        <div>
          <h1 className="text-3xl font-bold text-textPrimary">WhatsApp Configuration</h1>
          <p className="text-textSecondary mt-1">
            Configure WhatsApp messaging with API integration and desktop automation
          </p>
        </div>
      </div>

      {/* Status Message */}
      {status.message && (
        <div className={`p-4 rounded-lg flex items-center space-x-3 ${
          status.type === 'success' ? 'bg-green-50 text-green-800 border border-green-200' :
          status.type === 'error' ? 'bg-red-50 text-red-800 border border-red-200' :
          'bg-blue-50 text-blue-800 border border-blue-200'
        }`}>
          {status.type === 'success' && <CheckCircleIcon className="h-5 w-5" />}
          {status.type === 'error' && <XCircleIcon className="h-5 w-5" />}
          {status.type === 'testing' && (
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-current"></div>
          )}
          <span className="whitespace-pre-line">{status.message}</span>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Global Control */}
        <div className="bg-surface rounded-lg shadow-sm border border-default p-6">
          <h2 className="text-xl font-semibold text-textPrimary mb-4">Global WhatsApp Control</h2>
          
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h3 className="font-medium text-textPrimary">WhatsApp Channel Status</h3>
              <p className="text-sm text-textSecondary mt-1">
                {config.globalEnabled 
                  ? "WhatsApp messaging is globally enabled for all campaigns and templates."
                  : "WhatsApp messaging is globally disabled. No messages will be sent."
                }
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <span className={`text-sm font-medium ${config.globalEnabled ? 'text-green-600' : 'text-red-600'}`}>
                {config.globalEnabled ? 'ENABLED' : 'DISABLED'}
              </span>
              <button
                type="button"
                onClick={() => handleConfigChange('globalEnabled', !config.globalEnabled)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${
                  config.globalEnabled ? 'bg-green-600' : 'bg-gray-200'
                }`}
                aria-label="Toggle WhatsApp globally"
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    config.globalEnabled ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
          </div>
        </div>

        {/* Test Messaging */}
        <div className="bg-surface rounded-lg shadow-sm border border-default p-6">
          <h2 className="text-xl font-semibold text-textPrimary mb-4">Test Messaging</h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-textPrimary mb-2">
                Test Phone Number
              </label>
              <input
                type="text"
                value={testMessage.phone}
                onChange={(e) => setTestMessage(prev => ({ ...prev, phone: e.target.value }))}
                placeholder="Enter phone number (e.g., 9876543210)"
                className="w-full px-3 py-2 border border-default rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              />
              <p className="text-xs text-textSecondary mt-1">
                Phone number without country code (will use default: +{config.defaultCountryCode})
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-textPrimary mb-2">
                Test Message
              </label>
              <textarea
                value={testMessage.message}
                onChange={(e) => setTestMessage(prev => ({ ...prev, message: e.target.value }))}
                rows={3}
                className="w-full px-3 py-2 border border-default rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>

            <button
              type="button"
              onClick={handleSendTestMessage}
              disabled={status.type === 'testing' || !config.globalEnabled}
              className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {status.type === 'testing' ? 'Sending...' : 'Send Test Message'}
            </button>
          </div>
        </div>
      </div>

      {/* Configuration Sections */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* API Configuration */}
        <div className="bg-surface rounded-lg shadow-sm border border-default p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-textPrimary">WhatsApp Business API</h2>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="apiEnabled"
                checked={config.apiEnabled}
                onChange={(e) => handleConfigChange('apiEnabled', e.target.checked)}
                className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
              />
              <label htmlFor="apiEnabled" className="text-sm font-medium text-textPrimary">
                Enable API
              </label>
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-textPrimary mb-2">
                Phone Number ID
              </label>
              <input
                type="text"
                value={config.phoneNumberId}
                onChange={(e) => handleConfigChange('phoneNumberId', e.target.value)}
                placeholder="Enter your WhatsApp Phone Number ID"
                disabled={!config.apiEnabled}
                className="w-full px-3 py-2 border border-default rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent disabled:bg-gray-100"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-textPrimary mb-2">
                Access Token
              </label>
              <input
                type="password"
                value={config.accessToken}
                onChange={(e) => handleConfigChange('accessToken', e.target.value)}
                placeholder="Enter your permanent access token"
                disabled={!config.apiEnabled}
                className="w-full px-3 py-2 border border-default rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent disabled:bg-gray-100"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-textPrimary mb-2">
                Default Country Code
              </label>
              <input
                type="text"
                value={config.defaultCountryCode}
                onChange={(e) => handleConfigChange('defaultCountryCode', e.target.value)}
                placeholder="91"
                className="w-full px-3 py-2 border border-default rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>

            <button
              type="button"
              onClick={handleTestAPI}
              disabled={status.type === 'testing' || !config.apiEnabled}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {status.type === 'testing' ? 'Testing...' : 'Test API Connection'}
            </button>
          </div>
        </div>

        {/* Desktop Automation */}
        <div className="bg-surface rounded-lg shadow-sm border border-default p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-textPrimary">Desktop Automation</h2>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="desktopEnabled"
                checked={config.desktopEnabled}
                onChange={(e) => handleConfigChange('desktopEnabled', e.target.checked)}
                className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
              />
              <label htmlFor="desktopEnabled" className="text-sm font-medium text-textPrimary">
                Enable Desktop
              </label>
            </div>
          </div>

          <div className="space-y-4">
            <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
              <p className="text-sm text-blue-800">
                <strong>Desktop Automation:</strong> Automatically sends bulk messages through WhatsApp Web using browser automation. Completely free with high reliability.
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-textPrimary mb-2">
                Delay Between Messages (seconds)
              </label>
              <input
                type="number"
                value={config.batchDelay}
                onChange={(e) => handleConfigChange('batchDelay', parseInt(e.target.value) || 3)}
                min="1"
                max="60"
                disabled={!config.desktopEnabled}
                className="w-full px-3 py-2 border border-default rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent disabled:bg-gray-100"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-textPrimary mb-2">
                Max Batch Size
              </label>
              <input
                type="number"
                value={config.maxBatchSize}
                onChange={(e) => handleConfigChange('maxBatchSize', parseInt(e.target.value) || 50)}
                min="1"
                max="200"
                disabled={!config.desktopEnabled}
                className="w-full px-3 py-2 border border-default rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent disabled:bg-gray-100"
              />
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="autoClose"
                checked={config.autoClose}
                onChange={(e) => handleConfigChange('autoClose', e.target.checked)}
                disabled={!config.desktopEnabled}
                className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded disabled:opacity-50"
              />
              <label htmlFor="autoClose" className="text-sm text-textPrimary">
                Auto-close browser after sending
              </label>
            </div>

            <button
              type="button"
              onClick={handleTestDesktop}
              disabled={status.type === 'testing' || !config.desktopEnabled}
              className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {status.type === 'testing' ? 'Testing...' : 'Test Desktop Automation'}
            </button>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <button
          type="button"
          onClick={handleSave}
          disabled={isSaving}
          className="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSaving ? 'Saving...' : 'Save Configuration'}
        </button>
      </div>
    </div>
  );
};

export default WhatsAppConfigurationPage;
