import React, { useState, useMemo, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Header from '../components/Header';
import Table, { Column } from '../components/Table';
import ColumnSelector from '../components/ColumnSelector';
import { MessageSignature, Channel, User, UserRole, AuditActionType, UserTablePreferences } from '../types';
import { PlusIcon, EditIcon, DeleteIcon, EyeIcon } from '../components/icons';
// Removed localStorage imports - now using API services
import { useAuth } from '../contexts/AuthContextDB';
import { canUserViewItem, canUserEditDeleteItem, AccessibleItem } from '../utils/accessControlUtils';
import { addAuditLog } from '../utils/auditUtilsDB';
import ConfirmationModal from '../components/ConfirmationModal';
import { signatureService } from '../services/SignatureService-API';
import { userService } from '../services/userService-API';

const SIGNATURES_TABLE_KEY = 'signaturesListTable';


const SignaturesPage: React.FC = () => {
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [signatures, setSignatures] = useState<MessageSignature[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [feedbackMessage, setFeedbackMessage] = useState<{ message: string; type: 'success' | 'error' | 'info' } | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false); // Modal state
  const [signatureToDelete, setSignatureToDelete] = useState<MessageSignature | null>(null); // Item to delete
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  
  // Column visibility state
  const defaultVisibleColumnIds = ['name', 'channel', 'content', 'is_default', 'is_active', 'actions'];
  const [visibleColumnIds, setVisibleColumnIds] = useState<string[]>(defaultVisibleColumnIds);
  const [visibleColumns, setVisibleColumns] = useState<string[]>(defaultVisibleColumnIds);

  // Data loading function
  const loadData = async (showLoadingState = true) => {
    try {
      if (showLoadingState) setIsLoading(true);
      else setIsRefreshing(true);
      
      // Load signatures and users from API
      const [allSignatures, allUsers] = await Promise.all([
        signatureService.getAllSignatures(),
        userService.getAllUsers()
      ]);
      setSignatures(allSignatures.filter(s => canUserViewItem(s as AccessibleItem, currentUser)));
      setUsers(allUsers);
      setLastRefresh(new Date());
      
      if (!showLoadingState) {
        showFeedback('Signatures list refreshed successfully.', 'success');
      }
    } catch (error) {
      console.error('Error loading signatures data:', error);
      showFeedback('Failed to load signatures data. Please refresh the page.', 'error');
    } finally {
      if (showLoadingState) setIsLoading(false);
      else setIsRefreshing(false);
    }
  };

  // Manual refresh function
  const handleRefresh = () => {
    loadData(false);
  };

  useEffect(() => {
    loadData();
  }, [currentUser]);

  // Auto-refresh on window focus
  useEffect(() => {
    const handleFocus = () => {
      const now = new Date();
      const timeSinceLastRefresh = now.getTime() - lastRefresh.getTime();
      // Only refresh if it's been more than 30 seconds since last refresh
      if (timeSinceLastRefresh > 30000) {
        console.log('🔄 Window focused - refreshing signatures list');
        loadData(false);
      }
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [lastRefresh]);

  // Periodic auto-refresh (every 60 seconds)
  useEffect(() => {
    const interval = setInterval(() => {
      console.log('🔄 Periodic refresh - updating signatures list');
      loadData(false);
    }, 60000); // 60 seconds

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        // Load signatures and users from API
        const [allSignatures, allUsers] = await Promise.all([
          signatureService.getAllSignatures(),
          userService.getAllUsers()
        ]);
        setSignatures(allSignatures.filter(s => canUserViewItem(s as AccessibleItem, currentUser)));
        setUsers(allUsers);
      } catch (error) {
        console.error('Error loading signatures data:', error);
        showFeedback('Failed to load signatures data. Please refresh the page.');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [currentUser]);

  const showFeedback = (message: string, type: 'success' | 'error' | 'info' = 'error') => {
    setFeedbackMessage({ message, type }); 
    setTimeout(() => {
      setFeedbackMessage(null);
    }, 3000);
  };

  const handleColumnVisibilityChange = (newVisibleColumnIds: string[]) => {
    console.log('Saving new columns:', newVisibleColumnIds);
    setVisibleColumns(newVisibleColumnIds);
    setVisibleColumnIds(newVisibleColumnIds); // Maintain backward compatibility
  };

  const handleAddSignature = () => {
    if (currentUser?.role === UserRole.VIEWER) {
        showFeedback("Access Denied: Viewers cannot add new signatures.");
        return;
    }
    navigate('/signatures/add');
  };

  const handleViewSignature = (signatureId: string) => {
    navigate(`/signatures/view/${signatureId}`);
  };

  const handleEditSignature = (signatureId: string) => {
    const signature = signatures.find(s => s.id === signatureId);
    if (signature && !canUserEditDeleteItem(signature as AccessibleItem, currentUser)) {
        showFeedback("Access Denied: You do not have permission to edit this signature.");
        return;
    }
    navigate(`/signatures/edit/${signatureId}`);
  };

  const confirmDeleteSignature = async () => {
    if (!signatureToDelete) return;
    
    try {
      await signatureService.deleteSignature(signatureToDelete.id);
      setSignatures(prevSignatures => prevSignatures.filter(s => s.id !== signatureToDelete.id));
      addAuditLog(AuditActionType.DELETE, 'Signature', { entityId: signatureToDelete.id, entityName: signatureToDelete.name, userId: currentUser?.user_id });
      showFeedback(`Signature "${signatureToDelete.name}" deleted successfully.`);
    } catch (error) {
      console.error('Error deleting signature:', error);
      showFeedback('Failed to delete signature. Please try again.');
    } finally {
      setShowDeleteModal(false);
      setSignatureToDelete(null);
    }
  };

  const handleDeleteSignature = (signature: MessageSignature) => {
    if (!canUserEditDeleteItem(signature as AccessibleItem, currentUser)) {
        showFeedback("Access Denied: You do not have permission to delete this signature.");
        return;
    }
    setSignatureToDelete(signature);
    setShowDeleteModal(true);
  };

  const getUserName = (userId?: string): string => {
    if (!userId) return 'N/A';
    const user = users.find(u => u.user_id === userId);
    return user ? user.name : 'Unknown User';
  };

  const filteredSignatures = useMemo(() => {
    // Start with already permission-filtered signatures
    if (!searchTerm.trim()) {
      return signatures;
    }
    const lowercasedFilter = searchTerm.toLowerCase();
    return signatures.filter(signature =>
      signature.name.toLowerCase().includes(lowercasedFilter) ||
      signature.channel.toLowerCase().includes(lowercasedFilter) ||
      signature.content.toLowerCase().includes(lowercasedFilter) ||
      getUserName(signature.created_by).toLowerCase().includes(lowercasedFilter)
    );
  }, [signatures, searchTerm]);

  const allTableColumns: Column<MessageSignature>[] = [
    { 
      id: 'name',
      header: 'Name', 
      accessor: 'name', 
      className: 'font-medium',
      sortable: true,
      sortValue: item => item.name.toLowerCase() 
    },
    { 
      id: 'channel',
      header: 'Channel', 
      accessor: 'channel',
      sortable: true,
      sortValue: item => item.channel
    },
    { 
      id: 'content',
      header: 'Content', 
      accessor: 'content', 
      render: (item) => item.content, // Let the table cell handle wrapping
      sortable: true,
      sortValue: item => item.content.toLowerCase()
    },
    { 
      id: 'is_default',
      header: 'Default', 
      accessor: 'is_default',
      sortable: true
    },
    { 
      id: 'is_active',
      header: 'Active', 
      accessor: 'is_active',
      sortable: true
    },
    { 
      id: 'created_by',
      header: 'Created By', 
      accessor: (item) => getUserName(item.created_by),
      sortable: true,
      sortValue: item => getUserName(item.created_by).toLowerCase()
    },
    { 
      id: 'created_at',
      header: 'Created At', 
      accessor: 'created_at', 
      render: (item) => new Date(item.created_at).toLocaleDateString(),
      sortable: true,
      sortValue: item => new Date(item.created_at).getTime()
    },
    {
      id: 'actions',
      header: 'Actions',
      accessor: 'id',
      render: (signature) => (
        <div className="flex space-x-2">
          <button
            onClick={() => handleViewSignature(signature.id)}
            className="text-green-600 hover:text-green-800 p-1"
            title={`View ${signature.name}`}
            aria-label={`View ${signature.name}`}
          >
            <EyeIcon className="h-5 w-5" />
          </button>
          <button
            onClick={() => handleEditSignature(signature.id)}
            disabled={!canUserEditDeleteItem(signature as AccessibleItem, currentUser)}
            className={`text-primary hover:text-blue-700 p-1 ${!canUserEditDeleteItem(signature as AccessibleItem, currentUser) ? 'opacity-50 cursor-not-allowed' : ''}`}
            title={canUserEditDeleteItem(signature as AccessibleItem, currentUser) ? `Edit ${signature.name}` : "Permission Denied"}
            aria-label={`Edit ${signature.name}`}
          >
            <EditIcon className="h-5 w-5" />
          </button>
          <button
            onClick={() => handleDeleteSignature(signature)}
            disabled={!canUserEditDeleteItem(signature as AccessibleItem, currentUser)}
            className={`text-red-500 hover:text-red-700 p-1 ${!canUserEditDeleteItem(signature as AccessibleItem, currentUser) ? 'opacity-50 cursor-not-allowed' : ''}`}
            title={canUserEditDeleteItem(signature as AccessibleItem, currentUser) ? `Delete ${signature.name}` : "Permission Denied"}
            aria-label={`Delete ${signature.name}`}
          >
            <DeleteIcon className="h-5 w-5" />
          </button>
        </div>
      ),
    },
  ];

  return (
    <div>
      <Header 
        title="Message Signatures" 
        subtitle="Manage reusable signatures for communications." 
        actions={
          <div className="flex items-center gap-2">
            <button
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
              title="Refresh signatures list"
            >
              <svg className={`h-4 w-4 mr-1 ${isRefreshing ? 'animate-spin' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              {isRefreshing ? 'Refreshing...' : 'Refresh'}
            </button>
          </div>
        }
      />
      {feedbackMessage && (
        <div className={`mb-4 p-3 border rounded-md shadow-sm text-sm ${
          feedbackMessage.type === 'success' ? 'bg-green-100 text-green-700 border-green-300' :
          feedbackMessage.type === 'info' ? 'bg-blue-100 text-blue-700 border-blue-300' :
          'bg-red-100 text-red-700 border-red-300'
        }`} role="alert">
          {feedbackMessage.message}
        </div>
      )}
      {isLoading ? (
        <div className="flex items-center justify-center p-8">
          <div className="text-gray-500">Loading signatures...</div>
        </div>
      ) : (
        <>
          <div className="mb-6 flex flex-col sm:flex-row justify-between items-center gap-4">
        <input
          type="text"
          placeholder="Search signatures (name, channel, content, created by)..."
          className="w-full sm:w-auto px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          aria-label="Search message signatures"
        />
        <div className="flex items-center gap-2 flex-wrap">
          <ColumnSelector
            allColumns={allTableColumns}
            visibleColumnIds={visibleColumns}
            onSave={handleColumnVisibilityChange}
            defaultVisibleColumnIds={defaultVisibleColumnIds}
            tableKey={SIGNATURES_TABLE_KEY}
            userId={currentUser?.user_id}
          />
          <button
            onClick={handleAddSignature}
            disabled={currentUser?.role === UserRole.VIEWER}
            className={`bg-primary hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transition duration-150 ease-in-out flex items-center justify-center ${currentUser?.role === UserRole.VIEWER ? 'opacity-50 cursor-not-allowed' : ''}`}
            aria-label="Add new message signature"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            Add New Signature
          </button>
        </div>
        </div>
        <Table<MessageSignature> 
          allColumns={allTableColumns} 
          visibleColumnIds={visibleColumns}
          data={filteredSignatures} 
          userId={currentUser?.user_id}
          caption="List of Message Signatures" 
          rowKey="id" 
          onRowDoubleClick={(signature) => handleEditSignature(signature.id)}
        />
      </>
    )}
    {showDeleteModal && signatureToDelete && (
        <ConfirmationModal
          isOpen={showDeleteModal}
          title="Confirm Deletion"
          message={<>Are you sure you want to delete signature: <strong>{signatureToDelete.name}</strong>?</>}
          onConfirm={confirmDeleteSignature}
          onCancel={() => { setShowDeleteModal(false); setSignatureToDelete(null); }}
          confirmText="Delete"
        />
      )}
    </div>
  );
};

export default SignaturesPage;
